import 'package:chucker_flutter/chucker_flutter.dart';
import 'package:rider/app_exports.dart';
import 'package:flutter/services.dart';
import 'package:http/http.dart' as http;
import '../main.dart';

final chuckerHttpClient = ChuckerHttpClient(http.Client());

Map<String, String> buildHeaderTokens() {
  Map<String, String> header = {
    HttpHeaders.contentTypeHeader: 'application/json; charset=utf-8',
    HttpHeaders.cacheControlHeader: 'no-cache',
    HttpHeaders.acceptHeader: 'application/json; charset=utf-8',
    'Access-Control-Allow-Headers': '*',
    'Access-Control-Allow-Origin': '*',
  };
  if (Globals.isUserLoggedIn) {
    header.putIfAbsent(HttpHeaders.authorizationHeader,
        () => 'Bearer ${Globals.user.apiToken}');
  }

  header.putIfAbsent('darkMode', () => Globals.isDarkModeOn.toString());
  header.putIfAbsent('language', () => Globals.selectedLanguageCode);
  if (Globals.riderRegionId != -1) {
    header.putIfAbsent('region_id', () => Globals.riderRegionId.toString());
  }

  return header;
}

Uri buildBaseUrl(String endPoint) {
  Uri url = Uri.parse(endPoint);
  if (!endPoint.startsWith('http')) {
    url = Uri.parse(Constants.apiURL + endPoint);
  }

  return url;
}

Future<Response> buildHttpResponse(String endPoint,
    {HttpMethod method = HttpMethod.get, Map? request}) async {
  var headers = buildHeaderTokens();
  Uri url = buildBaseUrl(endPoint);

  int maxRetries = 3;

  for (var i = 0; i < maxRetries; i++) {
    try {
      Response response;

      if (method == HttpMethod.post) {
        response = await chuckerHttpClient
            .post(url, body: jsonEncode(request), headers: headers)
            .timeout(const Duration(seconds: 8),
                onTimeout: () => throw 'Timeout');
      } else if (method == HttpMethod.delete) {
        response = await delete(url, headers: headers).timeout(
            const Duration(seconds: 8),
            onTimeout: () => throw 'Timeout');
      } else if (method == HttpMethod.put) {
        response = await put(url, body: jsonEncode(request), headers: headers)
            .timeout(const Duration(seconds: 8),
                onTimeout: () => throw 'Timeout');
      } else {
        response = await chuckerHttpClient.get(url, headers: headers).timeout(
            const Duration(seconds: 8),
            onTimeout: () => throw 'Timeout');
      }

      if (response.statusCode == 401) {
        logOutSuccess();
      } else if (response.statusCode == 500) {
        Response invalidResponse = Response(
            jsonEncode({
              "status": false,
              "message": Globals.language.serverErrorMsg,
            }),
            500);

        if (kDebugMode) {
          showAppDialog(
            dialogType: AppDialogType.error,
            title: 'API error 500',
            onAccept: () {
              Clipboard.setData(ClipboardData(
                  text: '500 error in $endPoint api\n\n${response.body}'));

              Navigator.of(navigatorKey.currentContext!).pop();
            },
          );
        }
        return invalidResponse;
      }

      return response;
    } catch (e) {
      if (((e as dynamic)?.message ?? '').startsWith('Failed host lookup') ||
          ((e as dynamic)?.message ?? '')
              .startsWith('Network is unreachable')) {
        HelperMethods.showWWWConnectionError(
            context: navigatorKey.currentContext!);
      }
      continue;
      // throw 'Something Went Wrong';
    }
  }
  throw 'Something Went Wrong';
}

//region Common
Future handleResponse(Response response, [bool? avoidTokenError]) async {
  if (!await isNetworkAvailable()) {
    throw 'Your internet is not working';
  }

  if (response.statusCode == 200) {
    return jsonDecode(response.body);
  } else {
    try {
      var body = jsonDecode(response.body);
      throw (body['message']);
    } on Exception {
      throw 'Something Went Wrong';
    }
  }
}

enum HttpMethod { get, post, delete, put }
