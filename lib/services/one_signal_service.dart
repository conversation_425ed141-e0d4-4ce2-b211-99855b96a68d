import 'package:flutter/material.dart';
import 'package:flutter_timezone/flutter_timezone.dart';
import 'package:onesignal_flutter/onesignal_flutter.dart';
import 'package:rider/app_counters.dart';
import 'package:rider/components/care_details_screen.dart';
import 'package:rider/globals.dart';
import 'package:rider/main.dart';
import 'package:rider/model/CareDataModel.dart';
import 'package:rider/network/rest_apis.dart';
import 'package:rider/screens/CareScreen.dart';
import 'package:rider/screens/CreateRideHelpTabScreen.dart';
import 'package:rider/screens/NotificationScreen.dart';
import 'package:rider/screens/RideHelpdetailsScreen.dart';
import 'package:rider/screens/chat_screen.dart';
import 'package:rider/screens/dashboard_wrapper_screen.dart';
import 'package:rider/screens/inbox_screen.dart';
import 'package:rider/utils/Extensions/confirmation_dialog.dart';
import 'package:rider/utils/Extensions/app_common.dart';
import 'package:rider/utils/constants.dart';

class OneSignalService {
  static bool _isInitialised = false;
  static bool _isSentToServer = false;
  static bool _isSendingToServer = false;

  static Future<OneSignalServiceStatus> init({
    required String appId,
  }) async {
    try {
      if (!_isInitialised) {
        OneSignal.initialize(appId);
        OneSignal.Notifications.addForegroundWillDisplayListener((event) {
          if (Globals.lastNotificationId != event.notification.notificationId) {
            Globals.lastNotificationId = event.notification.notificationId;
            _handleNotification(event.notification);
          }
        });

        OneSignal.Notifications.addClickListener((event) {
          _handleNotification(event.notification, fromTerminated: true);
        });
        _isInitialised = true;
      }
      if (!_isSentToServer) {
        await Future.delayed(
          const Duration(
            seconds: 2,
          ),
        );

        OneSignal.User.pushSubscription.addObserver((observer) async {
          final String currentTimeZone =
              await FlutterTimezone.getLocalTimezone();
          String? id = observer.current.id;
          if ((id ?? "").trim().isNotEmpty) {
            Globals.playerId = id!;
            if (!_isSendingToServer) {
              _isSendingToServer = true;
              var result = await updatePlayerId(
                  playerId: Globals.playerId, timezone: currentTimeZone);
              if (result.status) {
                _isSentToServer = true;
              }
            }
          }
        });

        await OneSignal.User.pushSubscription.optIn();
        final String currentTimeZone = await FlutterTimezone.getLocalTimezone();
        String? id = OneSignal.User.pushSubscription.id;
        if ((id ?? "").trim().isNotEmpty) {
          Globals.playerId = id!;
          if (!_isSendingToServer) {
            _isSendingToServer = true;
            var result = await updatePlayerId(
                playerId: Globals.playerId, timezone: currentTimeZone);
            if (result.status) {
              _isSentToServer = true;
            }
          }
        }
      }

      return OneSignalServiceStatus.initialised;
    } catch (e) {
      rethrow;
    }
  }

  static void _handleNotification(OSNotification notification,
      {bool fromTerminated = false}) {
    if (notification.additionalData?['type'] ==
        NotificationTypes.rideAlertNotification) {
      if (!fromTerminated) {
        showAppDialog(
          dialogType: AppDialogType.info,
          title: '${notification.title ?? ''}\n\n${notification.body ?? ''}',
          onAccept: () {
            Navigator.of(navigatorKey.currentContext!).pop();
          },
        );
      }
    } else if (notification.additionalData?['type'] ==
        NotificationTypes.rideNearStartNotification) {
      if (!fromTerminated) {
        showAppDialog(
          dialogType: AppDialogType.info,
          title: '${notification.title ?? ''}\n\n${notification.body ?? ''}',
          onAccept: () {
            Navigator.of(navigatorKey.currentContext!).pop();
          },
        );
      }
    } else if (notification.additionalData?['type'] ==
        NotificationTypes.rideStartNotification) {
      launchScreen(
        const DashboardWrapperScreen(),
        isNewTask: true,
      );
    } else if (notification.additionalData?['type'] ==
        NotificationTypes.chatMsgNotification) {
      if (!Globals.isChatScreenAlreadyOpened) {
        launchScreen(ChatScreen(
          userData: Globals.chatDriverData,
          rideId: Globals.currentRideId,
        ));
      }
    } else if (notification.additionalData?['type'] ==
        NotificationTypes.inboxMsgNotification) {
      if (fromTerminated) {
        launchScreen(const InboxScreen());
      } else {
        // AppCounters.incrementInboxCount();
      }
    } else if (notification.additionalData?['type'] ==
        NotificationTypes.careMsgNotification) {
      if (fromTerminated) {
        launchScreen(const CareScreen());
      } else {
        // AppCounters.incrementCareCount();
      }
    } else if (notification.additionalData?['type'] ==
        NotificationTypes.notificationMsgNotification) {
      if (fromTerminated) {
        launchScreen(const NotificationScreen());
      } else {
        // AppCounters.incrementNotificationCount();
      }
    } else if (notification.additionalData?['type'] ==
        NotificationTypes.newCareRelatedMessage) {
      if (fromTerminated) {
        Navigator.of(navigatorKey.currentContext!).popUntil(
          (route) => route.isFirst,
        );

        int careId = int.tryParse(
                (notification.additionalData?['complaint_id'] ?? 0)
                    .toString()) ??
            0;
        bool isClosed = notification.additionalData?['is_closed'] ?? false;
        if (careId == 0) {
          return;
        }
        launchScreen(CareDetailsScreen(
          care: CareData(
              id: careId,
              subject: "",
              message: "",
              status: "",
              createdAt: null,
              updatedAt: null,
              isFromAdmin: null),
          preStateUpdater: () {},
          isClosed: isClosed,
        ));
      } else {
        // AppCounters.incrementNotificationCount();
      }
    } else if (notification.additionalData?['type'] ==
        NotificationTypes.newIssueRelatedMessage) {
      if (fromTerminated) {
        Navigator.of(navigatorKey.currentContext!).popUntil(
          (route) => route.isFirst,
        );

        int issueId = int.tryParse(
                (notification.additionalData?['issue_id'] ?? 0).toString()) ??
            0;
        bool isClosed = notification.additionalData?['is_closed'] ?? false;
        if (issueId == 0) {
          return;
        }
        launchScreen(
          RideHelpDetailsScreen(
            helpData: RideHelpData(
              id: issueId,
              booking_date: "booking_date",
              booking_from: "booking_from",
              booking_to: "booking_to",
              created_at: "created_at",
              help_count: 0,
              ride_request_id: 0,
              subject: "subject",
              description: "description",
            ),
            isClosed: isClosed,
            cardStateUpdater: () {},
          ),
        );
      } else {
        // AppCounters.incrementNotificationCount();
      }
    }
  else if (notification.additionalData?['type'] ==
        NotificationTypes.newOpportunityAccepted) {
      if (fromTerminated) {
        Navigator.of(navigatorKey.currentContext!).popUntil(
          (route) => route.isFirst,
        );
       Globals.homePageToMyRidesNavigator();
      } else {
        // AppCounters.incrementNotificationCount();
      }
    }
  
  }


  static Future<void> dispose() async {
    try {
      _isInitialised = false;
      OneSignal.Notifications.clearAll();
      OneSignal.User.pushSubscription.optOut();
      OneSignal.logout();
    } catch (e) {
      //
    }
  }
}

enum OneSignalServiceStatus {
  permissionDenied,
  initError,
  initialised,
}
