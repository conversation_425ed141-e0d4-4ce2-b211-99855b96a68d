import 'package:flutter_stripe/flutter_stripe.dart';
import 'package:rider/app_exports.dart';
import 'package:rider/model/StripePayModel.dart';
import 'package:http/http.dart' as http;

abstract class StripeService {
  static String _secretKey = "";
  static String publishableKey = "";
  static List<PaymentModel> _paymentTypeList = [];

  static Future<bool> getPaymentTypeList() async {
    if (_secretKey.isEmpty) {
      PaymentListResponseModel? response = await getPaymentList();
      if (response == null) {
        return false;
      }

      _paymentTypeList = response.data ?? [];
      if (_paymentTypeList.isEmpty) {
        return false;
      } else {
        for (var element in _paymentTypeList) {
          if (element.type == "stripe") {
            if (element.isTest == 1) {
              _secretKey = element.testValue!.secretKey!;
              publishableKey = element.testValue!.publishableKey!;
            } else {
              _secretKey = element.liveValue!.secretKey!;
              publishableKey = element.liveValue!.publishableKey!;
            }

            Stripe.publishableKey = publishableKey;
            if (Platform.isIOS) {
              Stripe.merchantIdentifier = 'merchant.au.rooo';
            }
          }
        }
        await Stripe.instance.applySettings();
        return true;
      }
    }
    return true;
  }

  static Future<List<SavedCard>?> getSavedCard(String customerId) async {
    if (!await getPaymentTypeList()) {
      return null;
    }
    List<SavedCard> cards = [];

    Map<String, String> headers = {
      HttpHeaders.authorizationHeader: 'Bearer $_secretKey',
      HttpHeaders.contentTypeHeader: 'application/x-www-form-urlencoded',
    };

    var request = http.Request(
      'GET',
      Uri.parse(
          'https://api.stripe.com/v1/customers/$customerId/payment_methods'),
    );

    request.headers.addAll(headers);

    return await request.send().then((value) async {
      return await http.Response.fromStream(value).then((response) async {
        if (response.statusCode == 200) {
          for (var element
              in (jsonDecode(response.body)['data'] as List<dynamic>)) {
            cards.add(
              SavedCard.fromJson(
                element,
              ),
            );
          }

          final ids = <dynamic>{};
          cards.retainWhere(
            (x) => ids.add(
              x.fingerPrint,
            ),
          );

          return cards;
        }
      });
    }).catchError((onError) {
      return null;
    });
  }

  static Future<StripePayModel?> getPaymentObject({
    required num payableAmount,
    required String customerId,
    required String userId,
    required String paymentType,
    required num? walletDeductionAmount,
  }) async {
    if (!(await getPaymentTypeList())) {
      return null;
    }
    late StripePayModel stripePayModel;
    Map<String, String> headers = {
      HttpHeaders.authorizationHeader: 'Bearer $_secretKey',
      HttpHeaders.contentTypeHeader: 'application/x-www-form-urlencoded',
    };

    var request = http.Request('POST', Uri.parse(Constants.stripeURL));

    request.bodyFields = {
      'amount': (payableAmount * 100).toInt().toString(),
      'currency': "aud",
      "capture_method": "manual",
      // if (!isTestType) "customer": STRIPE_CUSTOMER_ID,
      "customer": customerId,
      "setup_future_usage": "off_session",
      "metadata[user_id]": userId,
      "metadata[payment_type]": paymentType,
      "metadata[wallet_deduction_amount]": walletDeductionAmount==null ? "0": walletDeductionAmount.toString(),
    };

    request.headers.addAll(headers);

    return await request.send().then((value) async {
      return await http.Response.fromStream(value).then((response) async {
        if (response.statusCode == 200) {
          stripePayModel =
              StripePayModel.fromJson(await handleResponse(response));
          return stripePayModel;
        }
        return null;
      });
    }).catchError((onError) {
      return null;
    });
  }

  static Future<bool> deleteCard({
    required String cardId,
  }) async {
    if (!(await getPaymentTypeList())) {
      return false;
    }
    Map<String, String> headers = {
      HttpHeaders.authorizationHeader: 'Bearer $_secretKey',
      HttpHeaders.contentTypeHeader: 'application/x-www-form-urlencoded',
    };

    var request = cardId.startsWith('card')
        ? http.Request(
            'DELETE',
            Uri.parse(
                'https://api.stripe.com/v1/customers/${Globals.user.stripeCustomerId}/cards/$cardId'))
        : http.Request(
            'POST',
            Uri.parse(
                'https://api.stripe.com/v1/payment_methods/$cardId/detach'));

    request.headers.addAll(headers);

    return await request.send().then((value) {
      return http.Response.fromStream(value).then((response) async {
        if (response.statusCode == 200) {
          return true;
        } else {
          return false;
        }
      });
    }).onError((error, stackTrace) {
      return false;
    });
  }

  static Future<bool> saveCard({
    required CardFieldInputDetails card,
  }) async {
    if (!(await getPaymentTypeList())) {
      return false;
    }
    dynamic result = await getTokenToSaveCard(Globals.user.stripeCustomerId);
    if (result == null || result['status'] == false) {
      return false;
    } else {
      return await Stripe.instance
          .confirmSetupIntent(
        paymentIntentClientSecret: result['setupIntent'],
        params: const PaymentMethodParams.card(
            paymentMethodData: PaymentMethodData(
          billingDetails: BillingDetails(),
        )),
      )
          .then((value) {
        return true;
      }).onError((error, stackTrace) {
        return false;
      });
    }
  }

  static Future<StripePayModel?> getWalletPaymentObject({
    required num payableAmount,
    required String customerId,
    required String userId,
    required String paymentType,
  }) async {
    if (!(await getPaymentTypeList())) {
      return null;
    }
    late StripePayModel stripePayModel;
    Map<String, String> headers = {
      HttpHeaders.authorizationHeader: 'Bearer $_secretKey',
      HttpHeaders.contentTypeHeader: 'application/x-www-form-urlencoded',
    };

    var request = http.Request('POST', Uri.parse(Constants.stripeURL));

    request.bodyFields = {
      'amount': (payableAmount * 100).toInt().toString(),
      'currency': "aud",
      "customer": customerId,
      "metadata[user_id]": userId,
      "metadata[payment_type]": paymentType,
    };

    request.headers.addAll(headers);

    return await request.send().then((value) async {
      return await http.Response.fromStream(value).then((response) async {
        if (response.statusCode == 200) {
          stripePayModel =
              StripePayModel.fromJson(await handleResponse(response));
          return stripePayModel;
        }
        return null;
      });
    }).catchError((onError) {
      return null;
    });
  }
}
