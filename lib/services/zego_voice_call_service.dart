import 'dart:io';

import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:zego_uikit_prebuilt_call/zego_uikit_prebuilt_call.dart';
import 'package:zego_uikit_signaling_plugin/zego_uikit_signaling_plugin.dart';

class ZegoVoiceCallService {
  static bool _isinitialized = false;
  static bool _isMicrophonePermissionGranted = false;
  static bool _isSystemAlertPermissionGranted = false;

  static Future<ZegoVoiceCallServiceStatus> call() async {
    if (!_isMicrophonePermissionGranted) {
      if (!(await _checkMicrophonePermissions())) {
        return ZegoVoiceCallServiceStatus.microphonePermissionDenied;
      }
    }
    if (Platform.isAndroid) {
      if (!_isSystemAlertPermissionGranted) {
        if (!(await _checkSystemAlertPermissions())) {
          return ZegoVoiceCallServiceStatus.systemAlertPermissionDenied;
        }
      }
    }

    if (_isinitialized) {
      return ZegoVoiceCallServiceStatus.initialised;
    }
    return ZegoVoiceCallServiceStatus.initError;
  }

  static Future<void> dispose() async {
    ZegoUIKitPrebuiltCallInvitationService().uninit();
  }

  static Future<bool> _checkMicrophonePermissions() async {
    PermissionStatus status = await Permission.microphone.status;

    if (status != PermissionStatus.granted) {
      status = await Permission.microphone.request();
    }
    _isMicrophonePermissionGranted = (status == PermissionStatus.granted);
    return _isMicrophonePermissionGranted;
  }

  static Future<bool> _checkSystemAlertPermissions() async {
    PermissionStatus status = await Permission.systemAlertWindow.status;

    if (status != PermissionStatus.granted) {
      status = await Permission.systemAlertWindow.request();
    }
    _isSystemAlertPermissionGranted = (status == PermissionStatus.granted);
    return _isSystemAlertPermissionGranted;
  }

  static Future<ZegoVoiceCallServiceStatus> init({
    required GlobalKey<NavigatorState> navigatorKey,
    required int appId,
    required String appSign,
    required String callerId,
    required String callerName,
  }) async {
    ZegoUIKitPrebuiltCallInvitationService().setNavigatorKey(navigatorKey);

    await ZegoUIKit()
        .initLog()
        .catchError((onError) => ZegoVoiceCallServiceStatus.initError);

    ZegoUIKitPrebuiltCallInvitationService().useSystemCallingUI(
      [ZegoUIKitSignalingPlugin()],
    );

    await ZegoUIKitPrebuiltCallInvitationService().init(
      appID: appId,
      appSign: appSign,
      userID: callerId,
      userName: callerName,
      plugins: [ZegoUIKitSignalingPlugin()],
    ).catchError((onError) => ZegoVoiceCallServiceStatus.initError);
    _isinitialized = true;
    return ZegoVoiceCallServiceStatus.initialised;
  }
}

enum ZegoVoiceCallServiceStatus {
  microphonePermissionDenied,
  systemAlertPermissionDenied,
  initError,
  initialised,
}
