import 'package:rider/app_exports.dart';
import 'package:rider/components/care_details_screen.dart';

class CreateCareTabScreen extends StatefulWidget {
  final String? status;

  const CreateCareTabScreen({
    super.key,
    this.status,
  });

  @override
  CreateCareTabScreenState createState() => CreateCareTabScreenState();
}

class CreateCareTabScreenState extends State<CreateCareTabScreen>
    with AutomaticKeepAliveClientMixin {
  ScrollController scrollController = ScrollController();

  int currentPage = 1;
  int totalPage = 1;
  List<CareData> careData = [];
  String emptyDataMsg = '';

  @override
  void initState() {
    super.initState();
    init();
    scrollController.addListener(() {
      if (scrollController.position.pixels ==
          scrollController.position.maxScrollExtent) {
        if (currentPage < totalPage) {
          currentPage++;
          setState(() {});

          init();
        }
      }
    });
  }

  Future<void> init() async {
    showAppActivity();
    late Future task;
    if (widget.status == Status.pending) {
      task = getPendingCareData(
        page: currentPage,
      );
    } else {
      task = getCompletedCareData(
        page: currentPage,
      );
    }
    await task.then((value) {
      if (value != null) {
        currentPage = value.pagination.currentPage!;
        totalPage = value.pagination.totalPages!;
        emptyDataMsg = value.message ?? '';
        if (currentPage == 1) {
          careData.clear();
        }
        careData.addAll(value.data);
        setState(() {
          hideAppActivity();
        });
      } else {
        showErrorToast();
      }
    }).onError((error, stackTrace) {
      showErrorToast();
    });
  }

  @override
  void setState(fn) {
    if (mounted) super.setState(fn);
  }

  @override
  void dispose() {
    hideAppActivity();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Stack(
      children: [
        AnimationLimiter(
          child: RefreshIndicator(
            onRefresh: () {
              setState(() {});
              return init();
            },
            child: ListView.builder(
                physics: const AlwaysScrollableScrollPhysics(),
                itemCount: careData.length,
                controller: scrollController,
                padding: const EdgeInsets.only(
                    top: 8, bottom: 8, left: 16, right: 16),
                itemBuilder: (_, index) {
                  CareData data = careData[index];
                  return AnimationConfiguration.staggeredList(
                    delay: const Duration(milliseconds: 200),
                    position: index,
                    duration: const Duration(milliseconds: 375),
                    child: SlideAnimation(
                          verticalOffset: 50.0,
                          child: FadeInAnimation(
                            child: InkWell(
                              onTap: () {
                                 launchScreen(
                              CareDetailsScreen(
                                care: data,
                                preStateUpdater: () {},
                                isClosed: widget.status == Status.closed,
                              ),
                            );
                              },
                              child: Card(
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Container(
                                      padding: const EdgeInsets.all(8),
                                      decoration: BoxDecoration(
                                        color: Theme.of(context).primaryColor,
                                        borderRadius: const BorderRadius.only(
                                          topLeft: Radius.circular(4),
                                          topRight: Radius.circular(4),
                                        ),
                                      ),
                                      child: Row(
                                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                        children: [
                                          Text(
                                            'Issue #${data.id}',
                                            style: const TextStyle(
                                              fontWeight: FontWeight.bold,
                                              fontSize: 16,
                                              color: Colors.white,
                                            ),
                                          ),
                                          Text(
                                            data.createdAt != null 
                                              ? DateTime.parse(data.createdAt!).toLocal().toString().substring(0, 16).replaceAll('T', ' ')
                                              : '',
                                            style: const TextStyle(
                                              color: Colors.white,
                                              fontSize: 12,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    Padding(
                                      padding: const EdgeInsets.all(12),
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            data.subject,
                                            style: const TextStyle(
                                              fontSize: 16,
                                              fontWeight: FontWeight.w500,
                                            ),
                                          ),
                                          const SizedBox(height: 4),
                                          Text(
                                            data.message ?? '',
                                            maxLines: 2,
                                            overflow: TextOverflow.ellipsis,
                                            style: TextStyle(
                                              fontSize: 14,
                                              color: Colors.grey[600],
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    Container(
                                      width: double.infinity,
                                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                                      decoration: BoxDecoration(
                                        color: Theme.of(context).brightness == Brightness.dark 
                                          ? Colors.grey[800] 
                                          : Colors.grey[100],
                                        borderRadius: const BorderRadius.only(
                                          bottomLeft: Radius.circular(4),
                                          bottomRight: Radius.circular(4),
                                        ),
                                      ),
                                      child: Text(
                                        (widget.status ?? "").trim().toLowerCase() == Status.closed ?   'Closed on: ${data.updatedAt != null ? DateTime.parse(data.updatedAt!).toLocal().toString().substring(0, 16).replaceAll('T', ' ') : ''}' :
                                        'Status: ${data.status.toUpperCase()}',
                                        style: TextStyle(
                                          fontSize: 12,
                                          color: Theme.of(context).brightness == Brightness.dark 
                                            ? Colors.grey[300]
                                            : Colors.grey[600],
                                          fontStyle: FontStyle.italic,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        )
                  );
                }),
          ),
        ),
        if (careData.isEmpty && !isAppActivityRunning.value)
          emptyWidget(emptyDataMsg: emptyDataMsg),
        const ActivityIndicator(),
      ],
    );
  }

  @override
  bool get wantKeepAlive => true;
}
