import 'package:rider/app_exports.dart';
import 'package:state_beacon/state_beacon.dart';

class ActivityIndicator extends StatelessWidget {
  const ActivityIndicator({super.key});

  @override
  Widget build(
    BuildContext context,
  ) {
    final activity = isAppActivityRunning.watch(context);

    return !activity
        ? const SizedBox()
        : Container(
            color: Colors.grey[200]!.withOpacity(.7),
            child: const Center(
              child: CircularProgressIndicator(),
            ),
          );
  }
}

final isAppActivityRunning = Beacon.writable(false);

showAppActivity() {
  isAppActivityRunning.value = true;
}

hideAppActivity() {
  isAppActivityRunning.value = false;
}
