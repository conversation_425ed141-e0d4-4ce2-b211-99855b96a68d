import 'package:rider/app_exports.dart';

class BookingWidget extends StatefulWidget {
  final bool isLast;
  final int? id;
  final Function onCancel;
  final DateTime rideRequestedTime;

  const BookingWidget({
    super.key,
    required this.id,
    required this.onCancel,
    required this.rideRequestedTime,
    this.isLast = false,
  });

  @override
  BookingWidgetState createState() => BookingWidgetState();
}

class BookingWidgetState extends State<BookingWidget> {
  final int timerMaxSeconds = Globals.driverWaitingMinutes * 60;

  int currentSeconds = 0;
  int duration = 0;
  int count = 0;
  Timer? timer;
  GlobalKey<ScaffoldState> scaffoldKey = GlobalKey<ScaffoldState>();

  String get timerText =>
      '${((duration - currentSeconds) ~/ 60).toString().padLeft(2, '0')}: ${((duration - currentSeconds) % 60).toString().padLeft(2, '0')}';

  @override
  void initState() {
    super.initState();
    init();
  }

  void init() async {
    int elapsedSeconds =
        DateTime.now().difference(widget.rideRequestedTime).inSeconds;

    duration = timerMaxSeconds - elapsedSeconds;
    if (duration < 5) {
      duration = 5;
    }
    startTimeout();
  }

  startTimeout() {
    var duration2 = const Duration(seconds: 1);
    timer = Timer.periodic(duration2, (timer) {
      setState(
        () {
          currentSeconds = timer.tick;
          count++;

          if (count >= 30) {
            int data = timerMaxSeconds - count;
            Map req = {
              'max_time_for_find_driver_for_ride_request': data,
            };

            rideRequestUpdate(request: req, rideId: widget.id).then((value) {
              //
            }).onError((error, stackTrace) {
              log(error.toString());
              handleError(error, stackTrace);
            });
            count = 0;
          }
          if (timer.tick >= duration) {
            timer.cancel();
            showAppDialog(
                title: Globals.language.confirmAutoCancelRideRequest,
                dialogType: AppDialogType.info,
                barrierDismissible: false,
                // onCancel: () {
                //   setState(() {
                //     startTimeout();
                //   });
                // },
                onAccept: () {
                  showAppActivity();
                  Map req = {
                    'status': RideStatus.canceled,
                    'cancel_by': "auto",
                  };
                  rideRequestUpdate(request: req, rideId: widget.id)
                      .then((value) {
                    timer.cancel();
                    launchScreen(const DashboardWrapperScreen(
                      isRecentSignUp: false,
                    ));
                  }).onError((error, stackTrace) {
                    log(error.toString());
                    handleError(error, stackTrace);
                  });
                });
          }
        },
      );
    });
  }

  @override
  void setState(fn) {
    if (mounted) super.setState(fn);
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                Globals.language.lookingForNearbyDrivers,
                style: const TextStyle(
                  // color: Colors.black,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Container(
                padding: const EdgeInsets.all(4),
                child: Text(
                  timerText,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    // color: Colors.black,
                  ),
                ),
              )
            ],
          ),
          const SizedBox(height: 16),
          Image.asset(
            Assets.driverSearching,
            width: MediaQuery.of(context).size.width / 1.5,
            fit: BoxFit.contain,
          ),
          Text(Globals.language.weAreLookingForNearDriversAcceptsYourRide,
              style: const TextStyle(
                fontSize: 13,
                // color: Colors.black,
              ),
              textAlign: TextAlign.center),
          // const SizedBox(height: 20),
          // AppButtonWidget(
          //   width: MediaQuery.sizeOf(context).width,
          //   text: Globals.language.cancel,
          //   textStyle: boldTextStyle(color: Colors.white),
          //   onTap: () {
          //     widget.onCancel();
          //   },
          // )
        ],
      ),
    );
  }
}
