
import 'package:rider/app_exports.dart';
import 'package:rider/components/scaffold_body_wrapper.dart';

class CustomScaffold extends StatelessWidget {
  final String? title;
  final Widget? titleWidget;
  final Drawer? drawer;
  final Widget? body;
  final Widget? bottomSheet;
  final Color? bodyColor;
  final Widget? floatingActionButton;
  final PreferredSizeWidget? bottom;
  final Widget? bottomNavigation;
  const CustomScaffold({
    super.key,
    this.title,
    this.titleWidget,
    this.body,
    this.drawer,
    this.bottomSheet,
    this.bodyColor,
    this.floatingActionButton,
    this.bottom,
    this.bottomNavigation,
  });

  @override
  Widget build(
    BuildContext context,
  ) {
    return Scaffold(
      bottomNavigationBar: bottomNavigation,
      appBar: title != null || titleWidget != null
          ? AppBar(
              bottom: bottom,
              iconTheme: const IconThemeData(color: Colors.white),
              title: titleWidget ??
                  Text(
                    title!,
                    style: const TextStyle(
                      color: Colors.white,
                    ),
                  ),
            )
          : null,
      drawer: drawer,
      floatingActionButton: floatingActionButton,
      backgroundColor: bodyColor,
      bottomSheet: bottomSheet,
      body: ScaffoldBodyWrapper(
        child: body ?? const SizedBox(),
      ),
    );
  }
}
