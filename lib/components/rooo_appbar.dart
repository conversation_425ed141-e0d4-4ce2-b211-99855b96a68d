import 'package:flutter/services.dart';
import 'package:rider/app_exports.dart';

class RoooAppbar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final List<Widget>? actionIconList;
  final Icon? leadingIcon;
  final bool? hideBackButton;
  final bool isDarkOverlay;
  const RoooAppbar(
      {super.key,
      required this.title,
      this.actionIconList,
      this.leadingIcon,
      this.hideBackButton,
      this.isDarkOverlay = true});

  @override
  Widget build(BuildContext context) {
    return AppBar(
      backgroundColor: Theme.of(context).primaryColor,
      automaticallyImplyLeading: !(hideBackButton ?? false),
      title: Text(
        title,
        style: const TextStyle(color: Colors.white),
      ),
      actions: actionIconList,
      leading: leadingIcon,
      iconTheme: const IconThemeData(color: Colors.white),
      systemOverlayStyle: SystemUiOverlayStyle(
        statusBarColor: Theme.of(context).primaryColor,
        statusBarIconBrightness: Theme.of(context).brightness == Brightness.dark
            ? Brightness.dark
            : Brightness.light,
        statusBarBrightness: Theme.of(context).brightness == Brightness.dark
            ? Brightness.dark
            : Brightness.light,
      ),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(60);
}
