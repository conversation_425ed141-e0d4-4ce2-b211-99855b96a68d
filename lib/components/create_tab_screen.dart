import 'package:rider/app_exports.dart';

class CreateTabScreen extends StatefulWidget {
  final String? status;

  final bool showData;
  const CreateTabScreen({super.key, this.status, this.showData = true});

  @override
  CreateTabScreenState createState() => CreateTabScreenState();
}

class CreateTabScreenState extends State<CreateTabScreen>
    with AutomaticKeepAliveClientMixin {
  ScrollController scrollController = ScrollController();

  int currentPage = 1;
  int totalPage = 1;
  List<RiderModel> riderData = [];
  String emptyDataMsg = '';

  @override
  void initState() {
    super.initState();
    init();

    scrollController.addListener(() {
      if (scrollController.position.pixels ==
          scrollController.position.maxScrollExtent) {
        if (currentPage < totalPage) {
          currentPage++;
          setState(() {});

          init();
        }
      }
    });
    afterBuildCreated(() {
      if (widget.status == RideStatus.upcoming) {
        Globals.myUpcomingRidesDataRefresher = () {
          currentPage = 1;
          setState(() {});
          init();
        };
      }
    });
  }

  Future<void> init() async {
    showAppActivity();
    await getRiderRequestList(
      page: currentPage,
      status: widget.status,
      // status: PENDING,
      riderId: Globals.user.id,
    ).then((value) {
      if (value == null) {
        toast(Globals.language.errorMsg);
        return;
      }
      hideAppActivity();
      currentPage = value.pagination!.currentPage!;
      totalPage = value.pagination!.totalPages!;

      emptyDataMsg = value.message ?? '';
      if (currentPage == 1) {
        riderData.clear();
      }
      setState(() {
        riderData.addAll(value.data!);
      });
    }).onError((error, stackTrace) {
      toast(Globals.language.errorMsg);
      handleError(error, stackTrace);
    });
  }

  @override
  void setState(fn) {
    if (mounted) super.setState(fn);
  }

  @override
  dispose() {
    hideAppActivity();
    super.dispose();
  }

  void confirmCancelRide(int id) {
    showAppDialog(
        title: Globals.language.areYouSureYouWantToCancelThisRide,
        dialogType: AppDialogType.confirmation,
        onAccept: () {
          Navigator.of(context).pop();
          cancelRide(id, flag: true);
        });
  }

  void confirmQuietPeriodCancelRide(int id, String msg) {
    showAppDialog(
        title: msg,
        dialogType: AppDialogType.confirmation,
        onAccept: () {
          Navigator.of(context).pop();
          cancelRide(id, flag: false);
        });
  }

  Future<void> cancelRide(int id, {required bool flag}) async {
    showAppActivity();

    Map req = {
      "id": id,
      "cancel_by": 'rider',
      "status": RideStatus.canceled,
      'is_flag': flag,
    };

    await rideRequestUpdate(
      request: req,
      rideId: id,
    ).then((value) async {
      if (value.status == true) {
        if (value.is_flag == true) {
          hideAppActivity();
          confirmQuietPeriodCancelRide(id, value.message ?? '');
        } else {
          currentPage = 1;
          init();

          Globals.homePageDataRefresher();
          hideAppActivity();
        }
      } else {
        hideAppActivity();
        toast(value.message);
      }
    }).onError((error, stackTrace) {
      hideAppActivity();

      toast(Globals.language.errorMsg);
      handleError(error, stackTrace);
    });

    // dynamic result = await cancelScheduledRide(id);
    // if (result == null) {
    //   setState(() {
    //
    //   });
    //   toast(Globals.language.errorMsg);
    // } else if (result['status']) {
    //   if (result['confirm']) {
    //     confirmQuietPeriodCancelRide(id);
    //   } else {
    //     currentPage = 1;
    //     init();
    //   }
    // } else {
    //   setState(() {
    //
    //   });
    //   toast(result['message']);
    // }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Stack(
      children: [
        AnimationLimiter(
          child: RefreshIndicator(
            onRefresh: () {
              currentPage = 1;
              setState(() {});
              return init();
            },
            child: ListView.builder(
                physics: const AlwaysScrollableScrollPhysics(),
                itemCount: riderData.length,
                controller: scrollController,
                padding: const EdgeInsets.only(
                    top: 8, bottom: 8, left: 16, right: 16),
                itemBuilder: (_, index) {
                  RiderModel data = riderData[index];
                  return AnimationConfiguration.staggeredList(
                    delay: const Duration(milliseconds: 200),
                    position: index,
                    duration: const Duration(milliseconds: 375),
                    child: SlideAnimation(
                      child: IntrinsicHeight(
                        child: inkWellWidget(
                          onTap: () {
                            // if (data.status == RideStatus.completed ||
                            //     widget.status == RideStatus.upcoming) {
                            launchScreen(
                              RideDetailScreen(
                                orderId: data.id!,
                                isFromHistory: true,
                                isUpcomingRide:
                                    widget.status == RideStatus.upcoming,
                              ),
                              pageRouteAnimation:
                                  PageRouteAnimation.SlideBottomTop,
                            );
                            // }
                          },
                          child: Container(
                            padding: const EdgeInsets.only(top: 8, bottom: 8),
                            margin: const EdgeInsets.only(top: 8, bottom: 8),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(10),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.grey.withOpacity(0.4),
                                  blurRadius: 10,
                                  spreadRadius: 0,
                                  offset: const Offset(0.0, 0.0),
                                ),
                              ],
                            ),
                            child: Container(
                              padding: const EdgeInsets.all(8),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Row(
                                        children: [
                                          const Icon(Ionicons.calendar,
                                              color: Colors.black, size: 16),
                                          const SizedBox(width: 8),
                                          Padding(
                                            padding:
                                                const EdgeInsets.only(top: 2),
                                            child: Text(
                                                printDate(
                                                    data.datetime.validate()),
                                                style: const TextStyle(
                                                    color: Colors.black,
                                                    fontSize: 14)),
                                          ),
                                        ],
                                      ),
                                      Text(
                                        '${Globals.language.lblRide} #${data.id}',
                                        style: const TextStyle(
                                          fontSize: 14,
                                          color: Colors.black,
                                        ),
                                      ),
                                    ],
                                  ),
                                  const Divider(height: 24, thickness: 0.5),
                                  Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      const Column(
                                        children: [
                                          Icon(Icons.near_me,
                                              color: Colors.green, size: 18),
                                          SizedBox(height: 2),
                                          SizedBox(
                                            height: 58,
                                            child: DottedLine(
                                              direction: Axis.vertical,
                                              lineLength: double.infinity,
                                              lineThickness: 1,
                                              dashLength: 2,
                                            ),
                                          ),
                                          SizedBox(height: 2),
                                          Icon(Icons.location_on,
                                              color: Colors.red, size: 18),
                                        ],
                                      ),
                                      const SizedBox(width: 16),
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          mainAxisAlignment:
                                              MainAxisAlignment.start,
                                          children: [
                                            SizedBox(
                                              height: 58,
                                              child: Text(
                                                data.startAddress.validate(),
                                                style: const TextStyle(
                                                    color: Colors.black,
                                                    fontSize: 14),
                                                maxLines: 2,
                                                overflow: TextOverflow.ellipsis,
                                              ),
                                            ),
                                            const SizedBox(height: 17),
                                            SizedBox(
                                              height: 58,
                                              child: Text(
                                                data.endAddress.validate(),
                                                style: const TextStyle(
                                                    color: Colors.black,
                                                    fontSize: 14),
                                                maxLines: 2,
                                                overflow: TextOverflow.ellipsis,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                  widget.status == RideStatus.upcoming
                                      ? Padding(
                                          padding:
                                              const EdgeInsets.only(top: 10.0),
                                          child: Center(
                                            child: AppButtonWidget(
                                              padding: EdgeInsets.zero,
                                              text: Globals.language.cancel,
                                              textStyle: const TextStyle(
                                                fontSize: 14,
                                                color: Colors.white,
                                              ),
                                              onTap: () {
                                                confirmCancelRide(data.id!);
                                              },
                                            ),
                                          ),
                                        )
                                      : const SizedBox()
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  );
                }),
          ),
        ),
        if (riderData.isEmpty && !isAppActivityRunning.value)
          emptyWidget(emptyDataMsg: emptyDataMsg),
        const ActivityIndicator(),
      ],
    );
  }

  @override
  bool get wantKeepAlive => true;
}
