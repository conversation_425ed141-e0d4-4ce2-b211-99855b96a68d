import 'package:rider/app_exports.dart';
import 'package:rider/features/care/models/care_details_response_model.dart';
import 'package:rider/screens/care_chat_widget.dart';

class CareDetailsScreen extends StatefulWidget {
  final CareData care;
  final bool isClosed;
  final Function() preStateUpdater;

  const CareDetailsScreen({
    super.key,
    required this.care,
    required this.preStateUpdater,
    this.isClosed = false,
  });

  @override
  State<CareDetailsScreen> createState() => _CareDetailsScreenState();
}

class _CareDetailsScreenState extends State<CareDetailsScreen> {
  List<CareComment> _messages = [];

  final TextEditingController _messageCont = TextEditingController();

  // ScrollController _controller = ScrollController();

  final FocusNode _focusNode = FocusNode();
  late int userId;
  String title = "Fetching details...";
  @override
  void initState() {
    userId = Globals.user.id;

    _loadData();

    super.initState();
  }

  @override
  void dispose() {
    hideAppActivity();
    super.dispose();
  }

  Future<void> _loadData() async {
    showAppActivity();
    var result = await getCareDetailsData(careId: widget.care.id);
    if (result == null) {
      toast(Globals.language.errorMsg);
    } else {
      _messages = result.data.carecomment.reversed.toList();
      widget.preStateUpdater();

      setState(() {
        title = widget.care.subject;
        hideAppActivity();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        hideKeyboard();
      },
      child: Scaffold(
        appBar: RoooAppbar(title: title),
        body: Stack(
          children: [
            Padding(
              padding: EdgeInsets.only(
                bottom: widget.isClosed
                    ? 0
                    : Platform.isIOS
                        ? 90
                        : 70,
              ),
              child: ListView.builder(
                reverse: true,
                padding: const EdgeInsets.all(
                  10,
                ),
                itemBuilder: (context, index) {
                  return CareChatItemWidget(data: _messages[index]);
                },
                itemCount: _messages.length,
              ),
            ),
            widget.isClosed
                ? const SizedBox()
                : Positioned(
                    bottom: Platform.isIOS ? 28 : 18,
                    left: 0,
                    right: 0,
                    child: Padding(
                      padding: const EdgeInsets.all(10.0),
                      child: Container(
                        decoration: BoxDecoration(
                          borderRadius: radius(),
                          color: Theme.of(context).cardColor,
                          boxShadow: const [
                            BoxShadow(
                              spreadRadius: 0.5,
                              blurRadius: 0.5,
                            ),
                          ],
                        ),
                        child: Row(
                          children: [
                            Expanded(
                              child: TextField(
                                focusNode: _focusNode,
                                controller: _messageCont,
                                decoration: InputDecoration(
                                  border: InputBorder.none,
                                  hintText: Globals.language.writeMessage,
                                  hintStyle: secondaryTextStyle(),
                                  contentPadding:
                                      const EdgeInsets.symmetric(horizontal: 8),
                                ),
                                cursorColor: Colors.black,
                                textCapitalization:
                                    TextCapitalization.sentences,
                                keyboardType: TextInputType.multiline,
                                minLines: 1,
                                style: primaryTextStyle(),
                                textInputAction: TextInputAction.send,
                                onSubmitted: (s) {
                                  sendMessage();
                                },
                                cursorHeight: 20,
                                maxLines: 5,
                              ),
                            ),
                            IconButton(
                              icon:  Icon(Icons.send, color: Theme.of(context).brightness == Brightness.dark ? Colors.white :Colors.black),
                              onPressed: () {
                                sendMessage();
                              },
                            )
                          ],
                        ),
                      ),
                    ),
                  ),
            const ActivityIndicator(),
          ],
        ),
      ),
    );
  }

  Future<void> sendMessage() async {
    if (_messageCont.text.trim().isNotEmpty) {
      hideKeyboard();
      showAppActivity();
      var result = await saveCareMessage(
        CareCommentRequest(
          userId: userId,
          addedBy: "rider",
          careId: widget.care.id.toString(),
          comment: _messageCont.text.trim(),
        ),
      );

      if (result == null) {
        toast(Globals.language.errorMsg);
      } else if (result.status == false) {
        toast(result.message);
      } else {
        _messageCont.text = "";
        _messages.insert(0, result.data!);
      }
      setState(() {
        hideAppActivity();
      });
    }
  }
}
