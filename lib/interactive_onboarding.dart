import 'package:rider/app_exports.dart';
import 'package:rider/main.dart';
import 'package:tutorial_coach_mark/tutorial_coach_mark.dart';

class InteractiveOnboarding {
  static  TutorialCoachMark ?_current;
  static List<TargetFocus> targets = [
    TargetFocus(
      identify: "Profile",
      // keyTarget: Globals.profileWidgetKey,
      targetPosition: TargetPosition(
        const Size(100, 100),
         Offset(
           MediaQuery.sizeOf(navigatorKey.currentContext!).width - 100,
           MediaQuery.sizeOf(navigatorKey.currentContext!).height - 100,
         ),
      ),
      contents: [
        TargetContent(
          align: ContentAlign.top,
          child: const Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Text(
                "Your profile is here",
                style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                    fontSize: 20.0),
              ),
              Padding(
                padding: EdgeInsets.only(top: 10.0),
                child: Text(
                  "Here you can update your profile picture, add payment cards, and more.",
                  style: TextStyle(color: Colors.white),
                ),
              )
            ],
          ),
        ),
      ],
    ),
    TargetFocus(
      identify: "All Rides",
      keyTarget: Globals.ridesWidgetKey,
      contents: [
        TargetContent(
          align: ContentAlign.top,
          child: const Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Text(
                "Your all Rides",
                style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                    fontSize: 20.0),
              ),
              Padding(
                padding: EdgeInsets.only(top: 10.0),
                child: Text(
                  "Here you can see all your upcoming rides, past rides, and more.",
                  style: TextStyle(color: Colors.white),
                ),
              ),
              Padding(padding: EdgeInsets.only(top: 15)),
            ],
          ),
        ),
      ],
    ),
    TargetFocus(
      identify: "Dashboard",
      keyTarget: Globals.dashboardWidgetKey,
      contents: [
        TargetContent(
          align: ContentAlign.top,
          child: const Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Text(
                "Your dashboard",
                style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                    fontSize: 20.0),
              ),
              Padding(
                padding: EdgeInsets.only(
                  top: 10.0,
                  bottom: 20,
                ),
                child: Text(
                  "Here you can see all your upcoming rides, latest blog posts, and more.",
                  style: TextStyle(color: Colors.white),
                ),
              )
            ],
          ),
        ),
      ],
    ),
    TargetFocus(
      identify: "Quick Ride",
      keyTarget: Globals.instantRideWidgetKey,
      contents: [
        TargetContent(
          align: ContentAlign.bottom,
          child: const Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Padding(
                padding: EdgeInsets.only(top: 20.0),
                child: Text(
                  "Start a quick ride",
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                    fontSize: 20.0,
                  ),
                ),
              ),
              Padding(
                padding: EdgeInsets.only(top: 10.0),
                child: Text(
                  "Search for a destination, select a service, and start your ride.",
                  style: TextStyle(color: Colors.white),
                ),
              )
            ],
          ),
        ),
      ],
    ),
    TargetFocus(
      identify: "Scheduled Ride",
      keyTarget: Globals.scheduledRideWidgetKey,
      contents: [
        TargetContent(
          align: ContentAlign.bottom,
          child: const Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Text(
                "Book a scheduled ride",
                style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                    fontSize: 20.0),
              ),
              Padding(
                padding: EdgeInsets.only(top: 10.0),
                child: Text(
                  "Book a ride for a specific date and time.",
                  style: TextStyle(color: Colors.white),
                ),
              )
            ],
          ),
        ),
      ],
    ),
  ];

  static void start(Function callback) {
    _current = TutorialCoachMark(
      colorShadow: Colors.red,
      targets: targets, // List<TargetFocus>
      alignSkip: Alignment.center,
      skipWidget: OutlinedButton(
        style: OutlinedButton.styleFrom(
          side: const BorderSide(
            color: Colors.white,
          ),
        ),
        onPressed: () {},
        child: const Text(
          "SKIP TUTORIAL",
          style: TextStyle(
            color: Colors.white,
          ),
        ),
      ),
      // paddingFocus: 10,
      // opacityShadow: 0.8,
      onClickTarget: (target) {},
      onClickTargetWithTapPosition: (target, tapDetails) {},
      onClickOverlay: (target) {},
      onSkip: () {
        callback();
        return true;
      },
      onFinish: () {
        callback();
      },
    );

    _current?.show(context: navigatorKey.currentContext!);
  }
}
