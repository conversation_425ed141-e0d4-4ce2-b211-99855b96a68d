import 'package:fluttertoast/fluttertoast.dart';
import 'package:mapbox_maps_flutter/mapbox_maps_flutter.dart';
import 'package:mqtt_client/mqtt_client.dart';
import 'package:rider/app_exports.dart';
import 'package:rider/global/models/ride_model.dart';
import 'package:rider/language/LanguageEn.dart';


abstract class Globals {
  static Position? currentLocation;
  static MapboxMap? mapbox;

  static bool isDashboardMainScreen = true;

  static PointAnnotationManager? pointAnnotationManager;
  static PolylineAnnotationManager? polylineAnnotationManager;

  static String lastNotificationId = "";
  static bool isUserLoggedIn = false;
  static late UserModel user;
  static bool isDarkModeOn = false;
  static ValueNotifier<bool> isUserUpdated = ValueNotifier(false);

  static int selectedDarkMode = -1;
  static bool isMqttConnected = false;
  static MqttServerClient mqttClient = MqttServerClient.withPort(
    "roooaustralia-bqzjha.a03.euc1.aws.hivemq.cloud",
    "rooo_rider_${Globals.user.id}",
    8883,
    maxConnectionAttempts: 10,
  );
  static int driverWaitingMinutes = 5;
  static late final SharedPreferences sharedPrefs;
  static BaseLanguage language = LanguageEn();
  static String selectedLanguageCode = "en";
  static String playerId = "";
  static bool isLoggedOut = false;

  static late Function homePageDataRefresher;
  static Function homePageToMyRidesNavigator = (){};
  static late Function homePageRedirect;
  static Function? myUpcomingRidesDataRefresher;
  static Function? myUpcomingRidesNavigator;
  static bool isAdminNotifiedForSOS = false;
  static bool isChatScreenAlreadyOpened = false;
  static late UserModel chatDriverData;
  static late int currentRideId;
  static Timer? bookingTimer;
  static bool isInternetErrorOpen = false;

  // static void animateMap(
  //     {required Completer<GoogleMapController> completer, required int zoom}) {}
  static late ValueNotifier<RideModel> currentRide;

  static final FToast fToast = FToast();
  static int riderRegionId = -1;
  static String riderRegionCode = "in";
  static String aboutUsText = "";
  static String companyEmail = "";
  static String deleteAccountText = "";
  static Function? currentRideScreenUpdator;
  static bool isWaitingAndTipChargesScreenOpened = false;
  static bool isRideReviewScreenOpened = false;
  static bool isRidePendingForPayment = false;
  static int inboxCount = 0;
  static final GlobalKey profileWidgetKey = GlobalKey();
  static final GlobalKey dashboardWidgetKey = GlobalKey();
  static final GlobalKey ridesWidgetKey = GlobalKey();
  static final GlobalKey instantRideWidgetKey = GlobalKey();
  static final GlobalKey scheduledRideWidgetKey = GlobalKey();
  static bool isDashboardRiderMarkerCreated = false;
}

/* Main Mqtt Client*/
abstract class AppMqttService {
  static Future<bool> initialize() async {
    if (Globals.mqttClient.connectionStatus?.state ==
        MqttConnectionState.connected) {
      Globals.mqttClient.disconnect();
    }
    Globals.mqttClient.setProtocolV311();
    Globals.mqttClient.secure = true;
    Globals.mqttClient.securityContext = SecurityContext.defaultContext;
    // Globals.mqttClient.keepAlivePeriod = 120;
    // Globals.mqttClient.connectTimeoutPeriod = 2000;
    Globals.mqttClient.autoReconnect = true;
    Globals.mqttClient.onConnected = () {
      Globals.isMqttConnected = true;
    };
    Globals.mqttClient.onDisconnected = () {
      Globals.isMqttConnected = false;
    };
    Globals.mqttClient.onSubscribed = (String topic) {
      log('nct->Mqtt Subscription confirmed for topic $topic');
    };

    try {
      await Globals.mqttClient
          .connect("hivemq.webclient.1729573412945", "3cNh4p\$n>5LAoQ9,#gSB");
      Globals.mqttClient
          .subscribe('upcoming_ride_${Globals.user.id}', MqttQos.atLeastOnce);

      Globals.mqttClient.updates!
          .listen((List<MqttReceivedMessage<MqttMessage?>>? c) {
        final MqttPublishMessage recMess = c![0].payload as MqttPublishMessage;
        final pt =
            MqttPublishPayload.bytesToStringAsString(recMess.payload.message);

        if (jsonDecode(pt)['success_type'] ==
            NotificationTypes.rideStartNotification) {
          launchScreen(
            const DashboardWrapperScreen(
              isRecentSignUp: false,
            ),
            isNewTask: true,
          );
        }
      });
      return true;
    } catch (e) {
      log('nct-> Mqtt Connect Error $e');
      return false;
    }
  }
}
