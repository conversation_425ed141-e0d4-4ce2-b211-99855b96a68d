import 'BaseLanguage.dart';

class LanguagePa extends BaseLanguage {
  @override
  String get about => 'ਬਾਰੇ';

  @override
  String get aboutDriver => 'ਡਰਾਈਵਰ ਬਾਰੇ';

  @override
  String get aboutUs =>
      'ਚੌਫਰਰ ਰਾਈਡਰ ਵਿੱਚ ਤੁਹਾਡਾ ਸੁਆਗਤ ਹੈ - ਤੁਹਾਡਾ ਆਖਰੀ ਰਾਈਡ-ਹੇਲਿੰਗ ਹੱਲ! ਨਿਰਵਿਘਨ ਬੁਕਿੰਗ, ਭਰੋਸੇਮੰਦ ਡ੍ਰਾਈਵਰਾਂ, ਅਤੇ ਬੇਮਿਸਾਲ ਸਹੂਲਤ ਦੇ ਨਾਲ, ਅਸੀਂ ਤੁਹਾਨੂੰ ਉੱਥੇ ਪਹੁੰਚਾਉਣ ਲਈ ਹਾਂ ਜਿੱਥੇ ਤੁਹਾਨੂੰ ਲੋੜ ਹੈ, ਜਦੋਂ ਵੀ ਤੁਹਾਨੂੰ ਇਸਦੀ ਲੋੜ ਹੈ। ਹੁਣੇ ਸਾਡੇ ਨਾਲ ਜੁੜੋ ਅਤੇ ਯਾਤਰਾ ਦਾ ਅਨੁਭਵ ਕਰੋ ਜਿਵੇਂ ਪਹਿਲਾਂ ਕਦੇ ਨਹੀਂ ਹੋਇਆ';

  @override
  String get accepted => 'ਸਵੀਕਾਰ ਕੀਤਾ';

  @override
  String get account => 'ਖਾਤਾ';

  @override
  String get accountText => 'ਖਾਤਾ';

  @override
  String get activityText => 'ਸਰਗਰਮੀ';

  @override
  String get addANewPlace => 'ਇੱਕ ਨਵੀਂ ਜਗ੍ਹਾ ਸ਼ਾਮਲ ਕਰੋ';

  @override
  String get addCareRequest => 'ਬੇਨਤੀ ਸ਼ਾਮਲ ਕਰੋ';

  @override
  String get addContact => 'ਸੰਪਰਕ ਸ਼ਾਮਲ ਕਰੋ';

  @override
  String get addImage => 'छवि जोड़ें';

  @override
  String get addMoney => 'ਪੈਸਾ ਸ਼ਾਮਲ ਕਰੋ';

  @override
  String get addMore => 'ਹੋਰ ਸ਼ਾਮਲ ਕਰੋ';

  @override
  String get addMoreTip => 'ਹੋਰ ਟਿਪ ਸ਼ਾਮਲ ਕਰੋ';

  @override
  String get address => 'ਪਤਾ';

  @override
  String get addReviews => 'ਸਮੀਖਿਆਵਾਂ ਸ਼ਾਮਲ ਕਰੋ';

  @override
  String get addVehicle => 'ਵਾਹਨ ਸ਼ਾਮਲ ਕਰੋ';

  @override
  String get addVehicleDetails => 'ਵਾਹਨ ਦੇ ਵੇਰਵੇ ਸ਼ਾਮਲ ਕਰੋ';

  @override
  String get addVehicleImage => 'ਵਾਹਨ ਚਿੱਤਰ ਸ਼ਾਮਲ ਕਰੋ';

  @override
  String get allow => 'ਦੀ ਇਜਾਜ਼ਤ';

  @override
  String get allTopics => 'ਸਾਰੇ ਵਿਸ਼ੇ';

  @override
  String get alreadyHaveAnAccount => 'ਪਹਿਲਾਂ ਹੀ ਕੋਈ ਖਾਤਾ ਹੈ?';

  @override
  String get amount => 'ਰਕਮ';

  @override
  String get appName => 'ROOO Rider';

  @override
  String get areYouSureYouWantDeleteAccount =>
      'ਕੀ ਤੁਸੀਂ ਯਕੀਨਨ ਖਾਤਾ ਮਿਟਾਉਣਾ ਚਾਹੁੰਦੇ ਹੋ?';

  @override
  String get areYouSureYouWantDeleteThisNumber =>
      'ਕੀ ਤੁਸੀਂ ਯਕੀਨਨ ਇਸ ਨੰਬਰ ਨੂੰ ਮਿਟਾਉਣਾ ਚਾਹੁੰਦੇ ਹੋ?';

  @override
  String get areYouSureYouWantPleaseReadAffect =>
      'ਕੀ ਤੁਸੀਂ ਯਕੀਨਨ ਆਪਣਾ ਖਾਤਾ ਮਿਟਾਉਣਾ ਚਾਹੁੰਦੇ ਹੋ? ਕਿਰਪਾ ਕਰਕੇ ਪੜ੍ਹੋ ਕਿ ਖਾਤਾ ਮਿਟਾਉਣ ਦਾ ਕਿਵੇਂ ਪ੍ਰਭਾਵ ਪਾਓ.';

  @override
  String get areYouSureYouWantToCancelThisRide =>
      'ਕੀ ਤੁਸੀਂ ਯਕੀਨਨ ਇਸ ਸਵਾਰੀ ਨੂੰ ਰੱਦ ਕਰਨਾ ਚਾਹੁੰਦੇ ਹੋ?';

  @override
  String get areYouSureYouWantToDelete => 'ਉਸ ਲਈ ਇੱਕ ਧਾਗਾ।';

  @override
  String get areYouSureYouWantToDeleteThisVehicle =>
      'ਕੀ ਤੁਸੀਂ ਯਕੀਨੀ ਤੌਰ \'ਤੇ ਇਸ ਵਾਹਨ ਨੂੰ ਮਿਟਾਉਣਾ ਚਾਹੁੰਦੇ ਹੋ';

  @override
  String get areYouSureYouWantToLogoutThisApp =>
      'ਕੀ ਤੁਸੀਂ ਯਕੀਨਨ ਲੌਗਆਉਟ ਕਰਨਾ ਚਾਹੁੰਦੇ ਹੋ?';

  @override
  String get arrived => 'ਪਹੁੰਚਿਆ';

  @override
  String get arriving => 'ਪਹੁੰਚਣਾ';

  @override
  String get availableBalance => 'ਉਪਲਬਧ ਬਕਾਇਆ';

  @override
  String get availableOffers => 'ਉਪਲਬਧ ਪੇਸ਼ਕਸ਼ਾਂ';

  @override
  String get basePrice => 'ਬੇਸ ਕੀਮਤ';

  @override
  String get bookingRideForOthers => 'ਦੂਜਿਆਂ ਲਈ ਸਵਾਰੀ ਬੁਕਿੰਗ';

  @override
  String get bookNow => 'ਹੁਣ ਬੁੱਕ ਕਰੋ';

  @override
  String get bookScheduledRide => 'ਅਨੁਸੂਚਿਤ ਰਾਈਡ ਬੁੱਕ ਕਰੋ';

  @override
  String get cancel => 'ਰੱਦ ਕਰੋ';

  @override
  String get cancelled => 'ਰੱਦ';

  @override
  String get capacity => 'ਸਮਰੱਥਾ';

  @override
  String get care => 'ਦੇਖਭਾਲ';

  @override
  String get carModel => 'ਕਾਰ ਮਾਡਲ';

  @override
  String get cash => 'ਨਕਦ';

  @override
  String get changePassword => 'ਪਾਸਵਰਡ ਬਦਲੋ';

  @override
  String get chatWithYourDriver => 'ਆਪਣੇ ਡਰਾਈਵਰ ਨਾਲ ਗੱਲਬਾਤ ਕਰੋ';

  @override
  String get roooCare => 'ਚੌਫਰ ਕੇਅਰ';

  @override
  String get chooseOnMap => 'ਨਕਸ਼ੇ \'ਤੇ ਚੁਣੋ';

  @override
  String get chooseYouPaymentLate => 'ਹੁਣ ਜਾਂ ਦੇਰ ਨਾਲ ਤੁਸੀਂ ਭੁਗਤਾਨ ਕਰੋ';

  @override
  String get complain => 'ਸ਼ਿਕਾਇਤ';

  @override
  String get complainList => 'ਸ਼ਿਕਾਇਤ ਸੂਚੀ';

  @override
  String get completed => 'ਪੂਰਾ';

  @override
  String get confirm => 'ਪੁਸ਼ਟੀ ਕਰੋ';

  @override
  String get confirmPassword => 'ਪਾਸਵਰਡ ਪੱਕਾ ਕਰੋ';

  @override
  String get contactLength =>
      'ਸੰਪਰਕ ਨੰਬਰ ਦੀ ਲੰਬਾਈ 10 ਤੋਂ 14 ਅੰਕ ਹੋਣੀ ਚਾਹੀਦੀ ਹੈ.';

  @override
  String get contactUs => 'ਸਾਡੇ ਨਾਲ ਸੰਪਰਕ ਕਰੋ';

  @override
  String get continueD => 'ਜਾਰੀ ਰੱਖੋ';

  @override
  String get continueNewRide => 'ਨਵੀਂ ਸਵਾਰੀ ਜਾਰੀ ਰੱਖੋ';

  @override
  String get continueText => 'ਜਾਰੀ ਰੱਖੋ';

  @override
  String get couponDiscount => 'ਕੂਪਨ ਛੂਟ';

  @override
  String get createAccount => 'ਖਾਤਾ ਬਣਾਉ';

  @override
  String get createdAt => '\'ਤੇ ਬਣਾਇਆ';

  @override
  String get createYourAccountToContinue => 'ਜਾਰੀ ਰੱਖਣ ਲਈ ਆਪਣਾ ਖਾਤਾ ਬਣਾਓ';

  @override
  String get currentLocation => 'ਮੌਜੂਦਾ ਟਿਕਾਣਾ';

  @override
  String get customerName => 'ਗਾਹਕ ਦਾ ਨਾਮ';

  @override
  String get darkMode => 'ਮੋਡ ਬਦਲੋ';

  @override
  String get deleteAccount => 'ਖਾਤਾ ਮਿਟਾਓ';

  @override
  String get deletingAccountEmail =>
      'ਤੁਹਾਡਾ ਖਾਤਾ ਮਿਟਾਉਣਾ ਸਾਡੇ ਡੇਟਾਬੇਸ ਤੋਂ ਨਿੱਜੀ ਜਾਣਕਾਰੀ ਨੂੰ ਹਟਾਉਂਦਾ ਹੈ. ਤੁਹਾਡੀ ਈਮੇਲ ਸਥਾਈ ਤੌਰ ਤੇ ਰਾਖਵੀਂ ਅਤੇ ਈਮੇਲ ਨੂੰ ਨਵਾਂ ਖਾਤਾ ਰਜਿਸਟਰ ਕਰਨ ਲਈ ਦੁਬਾਰਾ ਨਹੀਂ ਵਰਤਿਆ ਜਾ ਸਕਦਾ';

  @override
  String get demoMsg => 'ਟੈਸਟਰ ਦੀ ਭੂਮਿਕਾ ਇਸ ਕਿਰਿਆ ਨੂੰ ਕਰਨ ਦੀ ਆਗਿਆ ਨਹੀਂ ਹੈ';

  @override
  String get destinationLocation => 'ਟਿਕਾਣਾ ਸਥਾਨ';

  @override
  String get detailScreen => 'ਵੇਰਵਾ ਸਕਰੀਨ';

  @override
  String get didNotReceiveTheCode => 'ਕੋਡ ਪ੍ਰਾਪਤ ਨਹੀਂ ਹੋਇਆ';

  @override
  String get distancePrice => 'ਦੂਰੀ ਕੀਮਤ';

  @override
  String get done => 'ਕੀਤਾ';

  @override
  String get donHaveAnAccount => 'ਕੀ ਤੁਹਾਡੇ ਕੋਲ ਖਾਤਾ ਨਹੀਂ ਹੈ?';

  @override
  String get doYouHaveCouponCode => 'ਕੀ ਤੁਹਾਡੇ ਕੋਲ ਕੂਪਨ ਕੋਡ ਹੈ?';

  @override
  String get driverInformation => 'ਡਰਾਈਵਰ ਜਾਣਕਾਰੀ';

  @override
  String get driverReview => 'ਡਰਾਈਵਰ ਸਮੀਖਿਆ';

  @override
  String get duration => 'ਅਵਧੀ';

  @override
  String get editProfile => 'ਸੋਧ ਪ੍ਰੋਫ਼ਾਈਲ';

  @override
  String get email => 'ਈ - ਮੇਲ';

  @override
  String get emergencyContact => 'ਐਮਰਜੈਂਸੀ ਸੰਪਰਕ';

  @override
  String get emergencyContacts => 'ਐਮਰਜੈਂਸੀ ਸੰਪਰਕ';

  @override
  String get enterAlias => 'ਉਪਨਾਮ ਦਰਜ ਕਰੋ';

  @override
  String get enterContactNumber => 'ਸੰਪਰਕ ਨੰਬਰ ਦਰਜ ਕਰੋ';

  @override
  String get enterName => 'ਨਾਮ ਦਰਜ ਕਰੋ';

  @override
  String get enterOTP => 'ਤਸਦੀਕ ਕੋਡ ਦਰਜ ਕਰੋ';

  @override
  String get enterPromoCode => 'ਪ੍ਰੋਮੋ ਕੋਡ ਦਰਜ ਕਰੋ';

  @override
  String get enterRiderDetails => 'ਰਾਈਡਰ ਵੇਰਵੇ ਦਰਜ ਕਰੋ';

  @override
  String get enterTheCodeSendTo => 'ਕੋਡ ਸੈਂਡੋ ਦਰਜ ਕਰੋ';

  @override
  String get enterTheEmailAssociatedWithYourAccount =>
      'ਆਪਣੇ ਖਾਤੇ ਨਾਲ ਸੰਬੰਧਿਤ ਈਮੇਲ ਦਰਜ ਕਰੋ';

  @override
  String get enterYourDestination => 'ਆਪਣੀ ਮੰਜ਼ਿਲ ਦਰਜ ਕਰੋ';

  @override
  String get enterYourMobileNumber => 'ਆਪਣਾ ਮੋਬਾਈਲ ਨੰਬਰ ਦਰਜ ਕਰੋ';

  @override
  String get errorMsg => 'ਗਲਤੀ ਸੁਨੇਹਾ';

  @override
  String get extraCharges => 'ਵਾਧੂ ਖਰਚੇ';

  @override
  String get failed => 'ਅਸਫਲ';

  @override
  String get fAQs => 'ਅਕਸਰ ਪੁੱਛੇ ਜਾਂਦੇ ਸਵਾਲ';

  @override
  String get fare => 'ਕਿਰਾਇਆ';

  @override
  String get female => 'ਔਰਤ';

  @override
  String get findPlace => 'ਇੱਕ ਜਗ੍ਹਾ ਲੱਭੋ ...';

  @override
  String get firstName => 'ਪਹਿਲਾ ਨਾਂ';

  @override
  String get forgotPassword => 'ਪਾਸਵਰਡ ਭੁੱਲ ਗਏ?';

  @override
  String get forInstantPayment => 'ਤੁਰੰਤ ਭੁਗਤਾਨ ਲਈ';

  @override
  String get gender => 'ਲਿੰਗ';

  @override
  String get get => 'ਪ੍ਰਾਪਤ ਕਰੋ';

  @override
  String get googleMap => 'ਗੂਗਲ ਮੈਪ';

  @override
  String get help => 'ਮਦਦ ਕਰੋ';

  @override
  String get helpSupport => 'ਸਹਾਇਤਾ ਅਤੇ ਸਹਾਇਤਾ';

  @override
  String get homeText => 'ਹੋਮ';

  @override
  String get howWasYourRide => 'ਤੁਹਾਡੀ ਸਵਾਰੀ ਕਿਵੇਂ ਸੀ?';

  @override
  String get iAgreeToThe => 'ਮੈਂ ਸਹਿਮਤ ਹਾਂ';

  @override
  String get inboxText => 'ਇਨਬਾਕਸ';

  @override
  String get inProgress => 'ਤਰੱਕੀ ਹੋ ਰਹੀ ਹੈ';

  @override
  String get invalidMessage => 'ਅਵੈਧ ਸੁਨੇਹਾ';

  @override
  String get invalidSubject => 'ਅਵੈਧ ਵਿਸ਼ਾ';

  @override
  String get invoice => 'ਚਲਾਨ';

  @override
  String get invoiceDate => 'ਚਲਾਨ ਦੀ ਤਾਰੀਖ';

  @override
  String get invoiceNo => 'ਇਨਵੌਇਸ ਨੰ';

  @override
  String get language => 'ਭਾਸ਼ਾ';

  @override
  String get lastName => 'ਆਖਰੀ ਨਾਂਮ';

  @override
  String get lblCancel => 'ਰੱਦ ਕਰੋ';

  @override
  String get lblCarNumberPlate => 'ਕਾਰ ਨੰਬਰ ਪਲੇਟ';

  @override
  String get lblCouponCode => 'ਕੂਪਨ ਕੋਡ:';

  @override
  String get lblDistance => 'ਦੂਰੀ:';

  @override
  String get lblDropOff => 'ਟਿਕਾਣਾ';

  @override
  String get lblFollowUs => 'ਸਾਡੇ ਪਿਛੇ ਆਓ';

  @override
  String get lblLessWalletAmount =>
      'ਨੋਟ: ਤੁਹਾਡੇ ਬਟੂਏ ਵਿੱਚ ਇੱਕ ਨਾਕਾਫੀ ਸੰਤੁਲਨ ਹੈ. ਰਕਮ ਸ਼ਾਮਲ ਕਰੋ, ਤੁਹਾਨੂੰ ਨਕਦ ਦੁਆਰਾ ਭੁਗਤਾਨ ਕਰਨਾ ਪਏਗਾ.';

  @override
  String get lblNext => 'ਅਗਲਾ';

  @override
  String get lblPayWhenEnds => 'ਯਾਤਰਾ ਕਰਨ ਵੇਲੇ ਭੁਗਤਾਨ ਕਰੋ';

  @override
  String get lblRide => 'ਸਵਾਰੀ';

  @override
  String get lblRideInformation => 'ਸਫ਼ਰ ਦੀ ਜਾਣਕਾਰੀ';

  @override
  String get lblSomeoneElse => 'ਕੋਈ ਹੋਰ';

  @override
  String get lblWhereAreYou => 'ਪਿਕਅੱਪ ਟਿਕਾਣਾ';

  @override
  String get lblWhoRiding => 'ਕੌਣ ਸਵਾਰ ਹੋਵੇਗਾ?';

  @override
  String get lblWhoRidingMsg =>
      'ਰਾਈਡਰ ਦੀ ਪੁਸ਼ਟੀ ਕਰੋ ਅਤੇ ਇਹ ਸੁਨਿਸ਼ਚਿਤ ਕਰੋ ਕਿ ਯਾਤਰਾ ਦੀ ਜਾਣਕਾਰੀ';

  @override
  String get lblYou => 'ਤੁਸੀਂ';

  @override
  String get logIn => 'ਲਾਗਿਨ';

  @override
  String get logOut => 'ਲਾੱਗ ਆਊਟ';

  @override
  String get lookingForNearbyDrivers => 'ਨੇੜਲੇ ਡਰਾਈਵਰਾਂ ਦੀ ਭਾਲ ਕਰ ਰਹੇ ਹਾਂ';

  @override
  String get male => 'ਮਰਦ';

  @override
  String get message => 'ਸੁਨੇਹਾ';

  @override
  String get messages => 'ਸੁਨੇਹੇ';

  @override
  String get moneyDebit => 'ਪੈਸਾ ਡੈਬਿਟ';

  @override
  String get moneyDeposited => 'ਪੈਸਾ ਜਮ੍ਹਾ';

  @override
  String get mostReliableMightyRiderApp => 'ਸਭ ਤੋਂ ਭਰੋਸੇਮੰਦ ਤਾਕਤਵਰ ਰਾਈਡਰ ਐਪ';

  @override
  String get myCars => 'ਮੇਰੀਆਂ ਕਾਰਾਂ';

  @override
  String get myProfile => 'ਮੇਰਾ ਪ੍ਰੋਫ਼ਾਈਲ';

  @override
  String get myRides => 'ਮੇਰੀਆਂ ਸਵਾਰੀਆਂ';

  @override
  String get myTrips => 'ਮੇਰੀਆਂ ਯਾਤਰਾਵਾਂ';

  @override
  String get myVehicles => 'ਮੇਰੇ ਵਾਹਨ';

  @override
  String get myWallet => 'ਮੇਰਾ ਬਟੂਆ';

  @override
  String get nameFieldIsRequired => 'ਨਾਮ ਖੇਤਰ ਲੋੜੀਂਦਾ ਹੈ';

  @override
  String get newPassword => 'ਨਵਾਂ ਪਾਸਵਰਡ';

  @override
  String get newRideRequested => 'ਨਵੀਂ ਯਾਤਰਾ ਦੀ ਬੇਨਤੀ ਕੀਤੀ ਗਈ';

  @override
  String get no => 'ਨਹੀਂ';

  @override
  String get noIAm => 'ਮੈਂ ਸਵਾਰ ਹੋਵਾਂਗਾ';

  @override
  String get noMoreData => 'ਕੋਈ ਹੋਰ ਡਾਟਾ ਨਹੀਂ';

  @override
  String get notChangeEmail => 'ਤੁਸੀਂ ਈਮੇਲ ਆਈਡੀ ਨਹੀਂ ਬਦਲ ਸਕਦੇ';

  @override
  String get notChangeUsername => 'ਤੁਸੀਂ ਉਪਯੋਗਕਰਤਾ ਨਾਮ ਨਹੀਂ ਬਦਲ ਸਕਦੇ';

  @override
  String get notification => 'ਸੂਚਨਾ';

  @override
  String get notifiedSuccessfully => 'ਸਫਲਤਾਪੂਰਕ ਸੂਚਿਤ ਕੀਤਾ';

  @override
  String get notifyAdmin => 'ਐਡਮਿਨ ਨੂੰ ਸੂਚਿਤ ਕਰੋ';

  @override
  String get now => 'ਹੁਣ';

  @override
  String get off => 'ਬੰਦ';

  @override
  String get ok => 'ਠੀਕ ਹੈ';

  @override
  String get oldPassword => 'ਪੁਰਾਣਾ ਪਾਸਵਰਡ';

  @override
  String get orderedDate => 'ਆਰਡਰ ਮਿਤੀ';

  @override
  String get orLogInWith => 'ਜਾਂ ਨਾਲ ਲੌਗਇਨ ਕਰੋ';

  @override
  String get orText => 'ਜਾਂ ਟੈਕਸਟ';

  @override
  String get other => 'ਹੋਰ';

  @override
  String get otp => 'ਪੜਤਾਲ ਕੋਡ';

  @override
  String get otpVeriFiCation => 'ਓਟੀਪੀ ਤਸਦੀਕ';

  @override
  String get otpVerification => 'ਓਟੀਪੀ ਤਸਦੀਕ';

  @override
  String get paid => 'ਦਾ ਭੁਗਤਾਨ';

  @override
  String get password => 'ਪਾਸਵਰਡ ਮੇਲ ਨਹੀਂ ਖਾਂਦਾ';

  @override
  String get passwordDoesNotMatch => 'ਪਾਸਵਰਡ ਮੇਲ ਨਹੀਂ ਖਾਂਦਾ';

  @override
  String get passwordInvalid => 'ਘੱਟੋ ਘੱਟ ਪਾਸਵਰਡ ਦੀ ਲੰਬਾਈ 8 ਹੋਣੀ ਚਾਹੀਦੀ ਹੈ';

  @override
  String get pay => 'ਤਨਖਾਹ';

  @override
  String get payment => 'ਭੁਗਤਾਨ';

  @override
  String get paymentDetail => 'ਭੁਗਤਾਨ ਦਾ ਵੇਰਵਾ';

  @override
  String get paymentDetails => 'ਭੁਗਤਾਨ ਦੇ ਵੇਰਵੇ';

  @override
  String get paymentMethod => 'ਭੁਗਤਾਨੇ ਦੇ ਢੰਗ';

  @override
  String get paymentStatus => 'ਭੁਗਤਾਨ ਦੀ ਸਥਿਤੀ';

  @override
  String get paymentType => 'ਭੁਗਤਾਨ ਦੀ ਕਿਸਮ';

  @override
  String get paymentVia => 'ਦੁਆਰਾ ਭੁਗਤਾਨ';

  @override
  String get payToPayment => 'ਭੁਗਤਾਨ ਲਈ ਭੁਗਤਾਨ ਕਰੋ';

  @override
  String get pending => 'ਬਕਾਇਆ';

  @override
  String get people => 'ਲੋਕ';

  @override
  String get phoneNumber => 'ਫੋਨ ਨੰਬਰ';

  @override
  String get phoneNumberIsRequired => 'ਫੋਨ ਨੰਬਰ ਲੋੜੀਂਦਾ ਹੈ';

  @override
  String get pickedUpButtonText => 'ਪਿਕਅੱਪ ਦਾ ਸਮਾਂ ਸੈੱਟ ਕਰੋ';

  @override
  String get pickedUpText => 'ਤੁਸੀਂ ਕਦੋਂ ਜਾਣਾ ਚਾਹੁੰਦੇ ਹੋ?';

  @override
  String get pickedUpTxt1 =>
      'ਹੌਲੀ ਪ੍ਰਗਤੀ ਦੇ ਕਾਰਨ ਤੁਹਾਡੇ ਨਵੇਂ ਡਰਾਈਵਰ ਨਾਲ ਮੇਲ ਕਰੋ।';

  @override
  String get pickedUpTxt2 => '90 ਦਿਨ ਪਹਿਲਾਂ ਤੱਕ ਆਪਣਾ ਸਹੀ ਪਿਕਅੱਪ ਸਮਾਂ ਚੁਣੋ';

  @override
  String get pickedUpTxt3 => 'ਤੁਹਾਡੀ ਸਵਾਰੀ ਨੂੰ ਮਿਲਣ ਲਈ ਵਾਧੂ ਉਡੀਕ ਸਮਾਂ ਸ਼ਾਮਲ ਹੈ';

  @override
  String get pickedUpTxt4 => 'ਬਿਨਾਂ ਕਿਸੇ ਚਾਰਜ ਦੇ 60 ਮਿੰਟ ਪਹਿਲਾਂ ਰੱਦ ਕਰੋ।';

  @override
  String get placeNotInArea => 'ਖੇਤਰ ਵਿੱਚ ਨਾ ਰੱਖੋ';

  @override
  String get plateNumber => 'ਪਲੇਟ ਨੰਬਰ';

  @override
  String get pleaseAcceptTermsOfServicePrivacyPolicy =>
      'ਕਿਰਪਾ ਕਰਕੇ ਸੇਵਾ ਅਤੇ ਗੋਪਨੀਯਤਾ ਨੀਤੀ ਦੀਆਂ ਸ਼ਰਤਾਂ ਸਵੀਕਾਰ ਕਰੋ';

  @override
  String get pleaseEnableLocationPermission =>
      'ਕਿਰਪਾ ਕਰਕੇ ਸਥਾਨ ਦੀ ਇਜਾਜ਼ਤ ਯੋਗ ਕਰੋ';

  @override
  String get pleaseEnterMsg => 'ਕਿਰਪਾ ਕਰਕੇ ਸੁਨੇਹਾ ਦਰਜ ਕਰੋ';

  @override
  String get pleaseEnterSubject => 'ਕਿਰਪਾ ਕਰਕੇ ਹੇਠ ਲਿਖਾਓ';

  @override
  String get pleaseSelectAmount => 'ਕਿਰਪਾ ਕਰਕੇ ਰਕਮ ਦੀ ਚੋਣ ਕਰੋ';

  @override
  String? get pleaseSelectImage => 'ਕਿਰਪਾ ਕਰਕੇ ਚਿੱਤਰ ਦੀ ਚੋਣ ਕਰੋ';

  @override
  String get pleaseSelectRating => 'ਕਿਰਪਾ ਕਰਕੇ ਰੇਟਿੰਗ ਦੀ ਚੋਣ ਕਰੋ';

  @override
  String? get pleaseSelectTheVehicle => 'ਕਿਰਪਾ ਕਰਕੇ ਵਾਹਨ ਦੀ ਚੋਣ ਕਰੋ';

  @override
  String? get pleaseSelectVehicleSegment => 'ਕਿਰਪਾ ਕਰਕੇ ਵਾਹਨ ਭਾਗ ਦੀ ਚੋਣ ਕਰੋ';

  @override
  String get pleaseWait => 'ਕ੍ਰਿਪਾ ਕਰਕੇ ਉਡੀਕ ਕਰੋ';

  @override
  String get priceDetail => 'ਮੁੱਲ ਦਾ ਵੇਰਵਾ';

  @override
  String get privacyPolicy => 'ਪਰਾਈਵੇਟ ਨੀਤੀ';

  @override
  String get profile => 'ਪਰੋਫਾਈਲ';

  @override
  String get profileUpdateMsg => 'ਪ੍ਰੋਫਾਈਲ ਸਫਲਤਾਪੂਰਵਕ ਅਪਡੇਟ ਕੀਤਾ ਗਿਆ';

  @override
  String get purchase => 'ਖਰੀਦ';

  @override
  String get reason => 'ਕਾਰਨ';
  @override
  String get recentTransactions => 'ਤਾਜ਼ਾ ਲੈਣ-ਦੇਣ';

  @override
  String get rememberMe => 'ਮੇਰੀ ਯਾਦ ਹੈ';

  @override
  String get resend => 'ਦੁਬਾਰਾ ਭੇਜੋ';

  @override
  String get rideHistory => 'ਰਾਈਡ ਇਤਿਹਾਸ';

  @override
  String get rideId => 'ਰਾਈਡ ਆਈਡੀ';

  @override
  String get rideIsCancelledByDriver => 'ਸਵਾਰੀ ਡਰਾਈਵਰ ਦੁਆਰਾ ਰੱਦ ਕੀਤੀ ਗਈ ਹੈ';

  @override
  String get rides => 'ਸਵਾਰ';

  @override
  String get save => 'ਸੇਵ';

  @override
  String get saveComplain => 'ਸ਼ਿਕਾਇਤ ਬਚਾਓ';

  @override
  String get savedPlaces => 'ਸੁਰੱਖਿਅਤ ਸਥਾਨ';

  @override
  String get scheduledRides => 'ਅਨੁਸੂਚਿਤ ਰਾਈਡਸ';

  @override
  String get seeTermsTxt => 'ਸ਼ਰਤਾਂ ਵੇਖੋ';

  @override
  String get selectGender => 'ਲਿੰਗ ਚੁਣੋ';

  @override
  String get selectLocation => 'ਟਿਕਾਣਾ ਚੁਣੋ';

  @override
  String get selectPlace => 'ਸਥਾਨ ਚੁਣੋ';

  @override
  String get selectVehicleSegment => 'ਸਟ੍ਰਿੰਗ ਸੇਲੈਕਟ ਵਾਹਨ ਸੇਗਮੈਂਟ ਪ੍ਰਾਪਤ ਕਰੋ';

  @override
  String get selectYourCarForYourRooo => 'ਆਪਣੇ ਚੌਫਰ ਲਈ ਆਪਣੀ ਕਾਰ ਦੀ ਚੋਣ ਕਰੋ';

  @override
  String get sendOTP => 'ਪੁਸ਼ਟੀਕਰਨ ਕੋਡ ਭੇਜੋ';

  @override
  String get serverErrorMsg => 'ਸਰਵਰ ਗਲਤੀ ਸੁਨੇਹਾ';

  @override
  String get service => 'ਸੇਵਾ';

  @override
  String get serviceDetail => 'ਸੇਵਾ ਦਾ ਵੇਰਵਾ';

  @override
  String get settings => 'ਸੈਟਿੰਗਾਂ';

  @override
  String get signInUsingYourMobileNumber =>
      'ਆਪਣੇ ਮੋਬਾਈਲ ਨੰਬਰ ਦੀ ਵਰਤੋਂ ਕਰਕੇ ਸਾਈਨ ਇਨ ਕਰੋ';

  @override
  String get signInWithFacebook => 'ਫੇਸਬੁੱਕ ਨਾਲ ਸਾਈਨ ਇਨ ਕਰੋ';

  @override
  String get signInWithGoogle => 'ਗੂਗਲ ਦੇ ਨਾਲ ਸਾਈਨ ਇਨ ਕਰੋ';

  @override
  String get signInYourAccount => 'ਆਪਣੇ ਖਾਤੇ ਵਿੱਚ ਸਾਈਨ ਇਨ ਕਰੋ';

  @override
  String get signUp => 'ਸਾਇਨ ਅਪ';

  @override
  String get someoneElse => 'ਕੋਈ ਹੋਰ';

  @override
  String get sos => 'SOS';

  @override
  String get sourceLocation => 'ਸਰੋਤ ਸਥਾਨ';

  @override
  String get subject => 'ਵਿਸ਼ਾ';

  @override
  String get submit => 'ਜਮ੍ਹਾਂ ਕਰੋ';

  @override
  String get termsConditions => 'ਨਿਯਮ ਅਤੇ ਸ਼ਰਤਾਂ';

  @override
  String get theme => 'ਥੀਮ';

  @override
  String get thisFieldRequired => 'ਇਸ ਫੀਲਡ ਦੀ ਲੋੜ ਹੈ';

  @override
  String get timePrice => 'ਸਮਾਂ ਮੁੱਲ';

  @override
  String get tip => 'ਸੰਕੇਤ';

  @override
  String get toEnjoyYourRideExperiencePleaseAllowPermissions =>
      "ਐਪ ਦੀ ਕਾਰਜਕੁਸ਼ਲਤਾ ਲਈ ਸਥਾਨ ਅਨੁਮਤੀ ਦੀ ਲੋੜ ਹੈ। ਟਿਕਾਣੇ ਦੀ ਇਜਾਜ਼ਤ ਤੋਂ ਬਿਨਾਂ, ਨਵੀਂ ਰਾਈਡ ਬੁਕਿੰਗ ਅਤੇ ਮੌਜੂਦਾ ਰਾਈਡ ਕਾਰਜਕੁਸ਼ਲਤਾ ਕੰਮ ਨਹੀਂ ਕਰੇਗੀ।";

  @override
  String get total => 'ਕੁੱਲ';

  @override
  String get transmission => 'ਸੰਚਾਰ';

  @override
  String get tripDetails => 'ਯਾਤਰਾ ਦੇ ਵੇਰਵੇ';

  @override
  String get txtURLEmpty => 'ਯੂਆਰਐਲ ਖਾਲੀ ਹੈ';

  @override
  String get upcoming => 'ਆਗਾਮੀ';

  @override
  String get updatePaymentStatus => 'ਭੁਗਤਾਨ ਸਥਿਤੀ ਨੂੰ ਅਪਡੇਟ ਕਰੋ';

  @override
  String get updateProfile => 'ਪ੍ਰੋਫਾਈਲ ਅਪਡੇਟ ਕਰੋ';

  @override
  String get updateVehicle => 'ਅੱਪਡੇਟ ਵਾਹਨ';

  @override
  String get useInCaseOfEmergency => 'ਐਮਰਜੈਂਸੀ ਦੇ ਮਾਮਲੇ ਵਿਚ ਵਰਤੋਂ';

  @override
  String get userName => 'ਉਪਯੋਗਕਰਤਾ ਨਾਮ';

  @override
  String get vehicles => 'ਵਾਹਨ';

  @override
  String get verifyOTP => 'ਓ ਟੀ ਪੀ ਦੀ ਪੁਸ਼ਟੀ ਕਰੋ';

  @override
  String get verifyOTPHeading => 'ਓ ਟੀ ਪੀ ਸਿਰਲੇਖ ਦੀ ਪੁਸ਼ਟੀ ਕਰੋ';

  @override
  String get verifyPhone => 'ਫ਼ੋਨ ਦੀ ਪੁਸ਼ਟੀ ਕਰੋ';

  @override
  String get viewAll => 'ਸਭ ਵੇਖੋ';

  @override
  String get viewHistory => 'ਇਤਿਹਾਸ ਵੇਖੋ';

  @override
  String get waitingForDriverConformation => 'ਨਿਰਯਾਤ ਕਰਨ ਦੀ ਉਡੀਕ ਕਰ ਰਿਹਾ ਹੈ';

  @override
  String get waitTime => 'ਉਡੀਕ ਵਾਰ';

  @override
  String get wallet => 'ਬਟੂਆ';

  @override
  String get waysToPlanWithRooo => 'ਚੌਫਰ ਨਾਲ ਯੋਜਨਾ ਬਣਾਉਣ ਦੇ ਤਰੀਕੇ';

  @override
  String get weAreLookingForNearDriversAcceptsYourRide =>
      'ਅਸੀਂ ਆਸ ਪਾਸ ਦੇ ਡਰਾਈਵਰਾਂ ਦੀ ਭਾਲ ਕਰ ਰਹੇ ਹਾਂ';

  @override
  String get weHaveSentDigitCode => 'ਅਸੀਂ 4 ਅੰਕਾਂ ਦਾ ਕੋਡ ਭੇਜਿਆ ਹੈ';

  @override
  String get welcomeBack => 'ਵਾਪਸ ਸਵਾਗਤ !';

  @override
  String get whatCanYouDoWithYourRooo => 'ਤੁਸੀਂ ਆਪਣੇ ਚੌਫਰ ਨਾਲ ਕੀ ਕਰ ਸਕਦੇ ਹੋ';

  @override
  String get whatWouldYouLikeToGo => 'ਤੁਸੀਂ ਕੀ ਜਾਣਾ ਚਾਹੁੰਦੇ ਹੋ?';

  @override
  String get whereToGoText => 'ਕਿੱਥੇ ਜਾਣਾ ਹੈ?';

  @override
  String get wouldYouLikeToAddTip => 'ਕੀ ਤੁਸੀਂ ਟਿਪ ਜੋੜਨਾ ਚਾਹੋਗੇ?';

  @override
  String get writeDescription => 'ਵੇਰਵਾ ਲਿਖੋ ....';

  @override
  String get writeMessage => 'ਸੁਨੇਹਾ ਲਿਖੋ';

  @override
  String get writeMsg => 'ਸੁਨੇਹਾ ਲਿਖੋ';

  @override
  String get writeYourComments => 'ਆਪਣੇ ਸੁਰਜੀਤ ਲਿਖੋ ....';

  @override
  String get yes => 'ਹਾਂ';

  @override
  String get youAreOnTheWay => 'ਤੁਸੀਂ ਰਾਹ ਤੇ ਹੋ';

  @override
  String get youCannotChangePhoneNumber => 'ਤੁਸੀਂ ਫੋਨ ਨੰਬਰ ਨਹੀਂ ਬਦਲ ਸਕਦੇ';

  @override
  String get youRoooHasArrived => 'ਤੁਹਾਡਾ ਚੌਫਰ ਆ ਗਿਆ ਹੈ';

  @override
  String get yourArrivalTime => 'ਚੌਫਰ ਦਾ ਅੰਦਾਜ਼ਨ ਪਹੁੰਚਣ ਦਾ ਸਮਾਂ';

  @override
  String get yourRoooIsAlmostHere => 'ਤੁਹਾਡਾ ਚੌਫਰ ਲਗਭਗ ਆ ਗਿਆ ਹੈ';

  @override
  String get youReached => 'ਤੁਸੀਂ ਪਹੁੰਚ ਗਏ ਹੋ';

  @override
  String get yourInternetIsNotWorking => 'ਤੁਹਾਡਾ ਇੰਟਰਨੈਟ ਕੰਮ ਨਹੀਂ ਕਰ ਰਿਹਾ ਹੈ';

  @override
  String get yourScheduledRideRequestIsSaved =>
      'ਤੁਹਾਡੀ ਅਨੁਸੂਚਿਤ ਰਾਈਡ ਬੇਨਤੀ ਨੂੰ ਸੁਰੱਖਿਅਤ ਕੀਤਾ ਗਿਆ ਹੈ';

  @override
  String get iHaveNotReceivedACode => 'ਮੈਨੂੰ ਕੋਈ ਕੋਡ ਪ੍ਰਾਪਤ ਨਹੀਂ ਹੋਇਆ ਹੈ';

  @override
  String get invalidOTP => 'ਅਵੈਧ OTP';

  @override
  String get vehicleName => 'ਵਾਹਨ ਦਾ ਨਾਮ';

  @override
  String get previousDue => 'ਪਿਛਲਾ ਬਕਾਇਆ';

  @override
  String get serviceIsNotAvailableInYourArea =>
      'ਸੇਵਾ ਤੁਹਾਡੇ ਖੇਤਰ ਵਿੱਚ ਉਪਲਬਧ ਨਹੀਂ ਹੈ';

  @override
  String get totaAmount => 'ਕੁੱਲ ਮਾਤਰਾ';

  @override
  String get preAuthorizedPayment => 'ਪੂਰਵ-ਅਧਿਕਾਰਤ ਭੁਗਤਾਨ';

  @override
  String get darkModeAlwaysOff => "ਹਮੇਸ਼ਾ ਬੰਦ";

  @override
  String get darkModeAlwaysOn => "ਹਮੇਸ਼ਾ ਚਾਲੂ";

  @override
  String get darkModePhoneBased => "ਫ਼ੋਨ ਸੈਟਿੰਗਾਂ ਦੀ ਵਰਤੋਂ ਕਰੋ";

  @override
  String get darkModeTimeBased => "ਆਟੋਮੈਟਿਕ (ਦਿਨ ਦਾ ਸਮਾਂ)";

  @override
  String get wantToTipTheDriver => 'ਡਰਾਈਵਰ ਨੂੰ ਟਿਪ ਦੇਣਾ ਚਾਹੁੰਦੇ ਹੋ?';

  @override
  String get extraChargeRefundable => 'ਵਾਪਸੀਯੋਗ ਜਮ੍ਹਾਂ ਰਕਮ';

  @override
  String get finalAmount => 'ਕੁੱਲ ਰਕਮ';

  @override
  String get rideAmount => 'ਸਵਾਰੀ ਦੀ ਰਕਮ';

  @override
  String get registerAs => 'ਰਜਿਸਟਰ ਕਰੋ?';

  @override
  String get registerAsAAgency => 'ਏਜੰਸੀ';

  @override
  String get registerAsARider => 'ਰਾਈਡਰ';

  @override
  String get chargesAndTip => 'ਖਰਚੇ ਅਤੇ ਟਿਪ';

  @override
  String get rideCancelMsg =>
      'ਜੇਕਰ ਤੁਸੀਂ ਰਾਈਡ ਨੂੰ ਰੱਦ ਕਰਦੇ ਹੋ, ਤਾਂ ਤੁਹਾਡੀ ਪੂਰਵ-ਪ੍ਰਮਾਣਿਤ ਰਕਮ ਵਿੱਚੋਂ ਕੁਝ ਰਕਮ ਕੱਟੀ ਜਾ ਸਕਦੀ ਹੈ। ਬਕਾਇਆ ਰਕਮ ਵਾਪਸ ਕਰ ਦਿੱਤੀ ਜਾਵੇਗੀ।';

  @override
  String get rideIsCancelledByDriverButWaitingForAdmin =>
      'ਡਰਾਈਵਰ ਇਸ ਰਾਈਡ ਨੂੰ ਰੱਦ ਕਰਨਾ ਚਾਹੁੰਦਾ ਹੈ।\nਕਿਰਪਾ ਕਰਕੇ ਪ੍ਰਸ਼ਾਸਕ ਦੀ ਕਾਰਵਾਈ ਦੀ ਉਡੀਕ ਕਰੋ।';

  @override
  String get suggestedReview => 'ਸੁਝਾਈਆਂ ਗਈਆਂ ਸਮੀਖਿਆਵਾਂ';

  @override
  String get tax => 'ਟੈਕਸ';

  @override
  String get apply => 'ਲਾਗੂ ਕਰੋ';

  @override
  String get applied => 'ਲਾਗੂ ਕੀਤਾ';

  @override
  String get coupons => 'ਕੂਪਨ';

  @override
  String get removeCoupon => 'ਕੂਪਨ ਹਟਾਓ';

  @override
  String get scheduledRideDetails => 'ਨਿਯਤ ਸਵਾਰੀ ਵੇਰਵੇ';

  @override
  String get date => 'ਤਾਰੀਖ਼';

  @override
  String get time => 'ਸਮਾਂ';

  @override
  String get careInfo =>
      'ਜੇਕਰ ਤੁਹਾਡੀ ਕੋਈ ਹੋਰ ਪੁੱਛਗਿੱਛ ਹੈ ਜਾਂ ਕਿਸੇ ਸਹਾਇਤਾ ਦੀ ਲੋੜ ਹੈ ਤਾਂ ਤੁਸੀਂ ਸਾਨੂੰ ਈਮੇਲ ਕਰ ਸਕਦੇ ਹੋ';

  @override
  String get confirmAutoCancelRideRequest =>
      'ਵਰਤਮਾਨ ਵਿੱਚ, ਸਾਡੇ ਸਾਰੇ ਡਰਾਈਵਰ ਰੁੱਝੇ ਹੋਏ ਹਨ, ਤੁਸੀਂ ਜਾਂ ਤਾਂ ਹੋਰ ਉਡੀਕ ਕਰ ਸਕਦੇ ਹੋ ਜਾਂ ਇਸ ਬੇਨਤੀ ਨੂੰ ਰੱਦ ਕਰ ਸਕਦੇ ਹੋ ਅਤੇ ਬਾਅਦ ਵਿੱਚ ਦੁਬਾਰਾ ਕੋਸ਼ਿਸ਼ ਕਰ ਸਕਦੇ ਹੋ।';

  @override
  String get waitMore => 'ਉਡੀਕ ਕਰੋ';

  @override
  String get applyCoupons => 'ਕੂਪਨ ਲਾਗੂ ਕਰੋ';

  @override
  String get changeEmail => 'ਈਮੇਲ ਬਦਲੋ';

  @override
  String get changeMobile => 'ਮੋਬਾਈਲ ਬਦਲੋ';

  @override
  String get sendVerificationCode => 'ਪੁਸ਼ਟੀਕਰਨ ਕੋਡ ਭੇਜੋ';

  @override
  String get verify => 'ਪੁਸ਼ਟੀ ਕਰੋ';

  @override
  String get wantToChangeYourEmail => 'ਕੀ ਤੁਸੀਂ ਆਪਣਾ ਈਮੇਲ ਬਦਲਣਾ ਚਾਹੁੰਦੇ ਹੋ?';

  @override
  String get wantToChangeYourMobile => 'ਕੀ ਤੁਸੀਂ ਆਪਣਾ ਮੋਬਾਈਲ ਬਦਲਣਾ ਚਾਹੁੰਦੇ ਹੋ?';

  @override
  String get emailVerificationText =>
      'ਇੱਕ ਪੁਸ਼ਟੀਕਰਨ ਕੋਡ ਤੁਹਾਡੀ ਮੌਜੂਦਾ ਪ੍ਰਦਾਨ ਕੀਤੀ ਈਮੇਲ \'ਤੇ ਭੇਜਿਆ ਜਾਵੇਗਾ। ਤਸਦੀਕ ਕਰਨ ਲਈ ਤੁਹਾਨੂੰ ਅਗਲੀ ਸਕ੍ਰੀਨ ਵਿੱਚ ਉਹ ਕੋਡ ਦਾਖਲ ਕਰਨਾ ਹੋਵੇਗਾ।';

  @override
  String get mobileVerificationText =>
      'ਤੁਹਾਡੇ ਨਵੇਂ ਪ੍ਰਦਾਨ ਕੀਤੇ ਮੋਬਾਈਲ ਨੰਬਰ \'ਤੇ ਇੱਕ OTP ਕੋਡ ਭੇਜਿਆ ਜਾਵੇਗਾ। ਤਸਦੀਕ ਕਰਨ ਲਈ ਤੁਹਾਨੂੰ ਅਗਲੀ ਸਕ੍ਰੀਨ ਵਿੱਚ ਉਹ OTP ਦਾਖਲ ਕਰਨਾ ਹੋਵੇਗਾ।';

  @override
  String get sendCode => 'ਕੋਡ ਭੇਜੋ';

  @override
  String get areYouSureToProceed =>
      'ਕੀ ਤੁਸੀਂ ਯਕੀਨੀ ਤੌਰ \'ਤੇ ਅੱਗੇ ਵਧਣਾ ਚਾਹੁੰਦੇ ਹੋ?';

  @override
  String get enterVerificationCode => 'ਤਸਦੀਕ ਕੋਡ ਦਰਜ ਕਰੋ';

  @override
  String get newEmail => 'ਨਵੀਂ ਈਮੇਲ';

  @override
  String get passCode => 'ਪਾਸਕੋਡ';

  @override
  String get addressLine1 => 'ਪਤਾ ਲਾਈਨ 1';

  @override
  String get addressLine2 => 'ਪਤਾ ਲਾਈਨ 2 (ਵਿਕਲਪਿਕ)';

  @override
  String get businessAddressDetails => 'ਕਾਰੋਬਾਰੀ ਪਤੇ ਦੇ ਵੇਰਵੇ';

  @override
  String get city => 'ਸ਼ਹਿਰ';

  @override
  String get invalidLicenseNumber => 'ਅਵੈਧ ਲਾਇਸੰਸ ਨੰਬਰ';

  @override
  String get licenseNumber => 'ਲਾਇਸੰਸ ਨੰਬਰ';

  @override
  String get postalCode => 'ਡਾਕ ਕੋਡ';

  @override
  String get stateCharges => 'ਰਾਜ';

  @override
  String get enterYourNewEmail => 'ਕਿਰਪਾ ਕਰਕੇ ਆਪਣੀ ਨਵੀਂ ਈਮੇਲ ਦਾਖਲ ਕਰੋ';

  @override
  String get enterYourNewMobileNumber =>
      'ਕਿਰਪਾ ਕਰਕੇ ਆਪਣਾ ਨਵਾਂ ਮੋਬਾਈਲ ਨੰਬਰ ਦਾਖਲ ਕਰੋ';

  @override
  String get pleaseVerifyYourEmail => 'ਕਿਰਪਾ ਕਰਕੇ ਆਪਣੀ ਈਮੇਲ ਦੀ ਪੁਸ਼ਟੀ ਕਰੋ';

  @override
  String get pleaseVerifyYourEmail2 =>
      'ਕਿਰਪਾ ਕਰਕੇ ਇਸ ਈਮੇਲ ਦੀ ਪੁਸ਼ਟੀ ਕਰਨ ਲਈ ਆਪਣੀ ਈਮੇਲ ਦੀ ਜਾਂਚ ਕਰੋ, ਪੁਸ਼ਟੀਕਰਨ ਤੋਂ ਬਾਅਦ ਤੁਸੀਂ ਆਪਣੀ ਈਮੇਲ ਬਦਲ ਸਕਦੇ ਹੋ';

  @override
  String get sendVerificationEmail => 'ਇੱਕ ਈਮੇਲ ਪੁਸ਼ਟੀਕਰਨ ਲਿੰਕ ਭੇਜੋ';

  @override
  String get send => 'ਭੇਜੋ';

  @override
  String get chooseOnMapAdvice =>
      'ਕਿਰਪਾ ਕਰਕੇ ਨਕਸ਼ੇ' " 'ਤੇ " 'ਚੁਣਨ ਲਈ' ' "ਪਿਕਅੱਪ" ' 'ਜਾਂ' ' "ਮੰਜ਼ਿਲ" ' 'ਖੇਤਰ' " 'ਤੇ ਟੈਪ ਕਰੋ";

  @override
  String get locationDeniedText1 =>
      "ਐਪ ਦੀ ਕਾਰਜਕੁਸ਼ਲਤਾ ਲਈ ਸਥਾਨ ਅਨੁਮਤੀ ਦੀ ਲੋੜ ਹੈ। ਟਿਕਾਣੇ ਦੀ ਇਜਾਜ਼ਤ ਤੋਂ ਬਿਨਾਂ, ਨਵੀਂ ਰਾਈਡ ਬੁਕਿੰਗ ਅਤੇ ਮੌਜੂਦਾ ਰਾਈਡ ਕਾਰਜਕੁਸ਼ਲਤਾ ਕੰਮ ਨਹੀਂ ਕਰੇਗੀ।";

  @override
  String get locationDeniedText2 => "ਤੁਸੀਂ ਸੈਟਿੰਗਾਂ ਤੋਂ ਇਜਾਜ਼ਤ ਬਦਲ ਸਕਦੇ ਹੋ।";

  @override
  String get addNewCard => "ਨਵਾਂ ਕਾਰਡ ਸ਼ਾਮਲ ਕਰੋ";

  @override
  String get paymentCards => "ਭੁਗਤਾਨ ਕਾਰਡ";

  @override
  String get invalidCardError => "ਕਿਰਪਾ ਕਰਕੇ ਵੈਧ ਕਾਰਡ ਵੇਰਵੇ ਦਾਖਲ ਕਰੋ";

  @override
  String get noCards => "ਤੁਹਾਡੇ ਕੋਲ ਕੋਈ ਭੁਗਤਾਨ ਕਾਰਡ ਸੁਰੱਖਿਅਤ ਨਹੀਂ ਹੈ";

  @override
  String get selectPaymentCard => "ਭੁਗਤਾਨ ਕਾਰਡ ਚੁਣੋ";

  @override
  String get you => "ਤੁਸੀਂ";

  @override
  String get ROOOTxt => "ROOO";
  @override
  String get RiderTxt => "rider";
  @override
  String get MorewithROOO => "More With ROOO";

  @override
  String get rooomForReads => "ROOOm for Reads";

  @override
  String get driverArrivingText => "ਤੁਹਾਡਾ ਡਰਾਈਵਰ ਆ ਰਿਹਾ ਹੈ";

  @override
  String get driverArrivedText => "ਤੁਹਾਡਾ ਡਰਾਈਵਰ ਆ ਗਿਆ ਹੈ";
}
