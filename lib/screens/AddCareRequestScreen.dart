import 'package:rider/app_exports.dart';
import 'package:rider/screens/CareScreen.dart';

import 'package:url_launcher/url_launcher.dart';

class AddCareRequestScreen extends StatefulWidget {
  const AddCareRequestScreen({super.key});

  @override
  State<AddCareRequestScreen> createState() => _AddCareRequestScreenState();
}

class _AddCareRequestScreenState extends State<AddCareRequestScreen> {
  CareData request = CareData(
    id: -1,
    message: '',
    subject: '',
    status: Status.pending,
    isFromAdmin: null,
    createdAt: DateTime.now().toString(),
    updatedAt: null,
  );

  final GlobalKey<FormState> formKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
  }

  bool validate() {
    if (formKey.currentState!.validate()) {
      if (request.subject.isEmpty || request.subject.length < 3) {
        toast(Globals.language.invalidSubject);
        return false;
      } else if (request.message.isEmpty || request.message.length < 3) {
        toast(Globals.language.invalidMessage);
        return false;
      }
      return true;
    }
    return false;
  }

  void save() {
    if (validate()) {
      showAppActivity();
      saveCareRequest(request).then((value) {
        if (value == true) {
          Navigator.of(context).pop();
          Navigator.of(context).pop();
          launchScreen(
            const CareScreen(),
          );
        } else {
          showErrorToast();
          hideAppActivity();
        }
      });
    }
  }

  @override
  dispose() {
    hideAppActivity();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).requestFocus(FocusNode());
      },
      child: Scaffold(
        appBar: RoooAppbar(title: Globals.language.addCareRequest),
        bottomNavigationBar: BottomButton(
          text: Globals.language.send,
          // color: Colors.black,
          onPressed: save,
        ),
        body: Stack(
          children: [
            SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(10),
                child: Form(
                  key: formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      AppTextField(
                        label: Globals.language.subject,
                        maxLength: 100,
                        autoFocus: false,
                        textFieldType: TextFieldType.OTHER,
                        textCapitalization: TextCapitalization.words,
                        errorThisFieldRequired: 'required',
                        onChanged: (p0) {
                          request.subject = p0.trim();
                        },
                      ),
                      const SizedBox(height: 20),
                      AppTextField(
                        label: Globals.language.message,
                        autoFocus: false,
                        maxLines: 8,
                        minLines: 8,
                        textFieldType: TextFieldType.OTHER,
                        errorThisFieldRequired: 'required',
                        onChanged: (p0) {
                          request.message = p0.trim();
                        },
                      ),
                      const SizedBox(height: 20),
                      RichText(
                        text: TextSpan(
                            text: Globals.language.careInfo,
                            style: TextStyle(
                                color: Theme.of(context).brightness ==
                                        Brightness.dark
                                    ? Colors.white
                                    : Colors.black),
                            children: [
                              TextSpan(
                                text: ' ${Globals.companyEmail}',
                                recognizer: TapGestureRecognizer()
                                  ..onTap = () {
                                    launchUrl(
                                        Uri.parse(
                                            'mailto:${Globals.companyEmail}'),
                                        mode: LaunchMode.externalApplication);
                                  },
                                style: const TextStyle(color: Colors.blue),
                              )
                            ]),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            const ActivityIndicator(),
          ],
        ),
      ),
    );
  }
}
