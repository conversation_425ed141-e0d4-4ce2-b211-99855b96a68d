import 'package:rider/app_exports.dart';
import 'package:rider/model/PaginationModel.dart';
import 'package:rider/screens/SimpleURLWebViewScreen.dart';

import 'package:flutter/services.dart';

class InboxScreen extends StatefulWidget {
  const InboxScreen({super.key});

  @override
  State<InboxScreen> createState() => _InboxScreenState();
}

class _InboxScreenState extends State<InboxScreen> {
  Inbox inboxData = Inbox(
    data: [],
    pagination: PaginationModel(),
  );

  ScrollController scrollController = ScrollController();

  int nextPage = 1;
  int totalPage = 1;
  int riderId = 0;
  bool isMoreData = true;
  bool isNoData = false;
  String emptyDataMsg = '';

  @override
  void initState() {
    riderId = Globals.user.id;

    scrollController.addListener(() {
      if (scrollController.position.pixels ==
          scrollController.position.maxScrollExtent) {
        if (isMoreData) {
          log('nct->loading more');
          getData();
        } else {
          log('nct->no more data');
        }
      }
    });

    getData();
    super.initState();
  }

  @override
  void dispose() {
    HelperMethods.getAndApplyCounters();
    hideAppActivity();
    super.dispose();
    if (Platform.isIOS) {
      SystemChrome.setSystemUIOverlayStyle(
          Theme.of(context).brightness == Brightness.dark
              ? SystemUiOverlayStyle.dark
              : SystemUiOverlayStyle.light);
    }
  }

  Future<void> getData() async {
    showAppActivity();
    getInboxData(
      nextPage,
      riderId,
    ).then((value) {
      if (value != null) {
        totalPage = value.pagination.totalPages!;

        if (nextPage == 1) {
          inboxData = value;
        } else {
          inboxData.data.addAll(
            value.data,
          );
        }
        nextPage = nextPage + 1;

        emptyDataMsg = inboxData.message ?? '';
        setState(() {
          isMoreData = nextPage <= totalPage;
          isNoData = inboxData.data.isEmpty;
        });
        hideAppActivity();
      } else {
        showErrorToast();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: RoooAppbar(
        title: Globals.language.inboxText,
        isDarkOverlay: false,
      ),
      body: Stack(
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Padding(
              //   padding: EdgeInsets.only(top: 20, left: 20),
              //   child: Text(
              //     'All Messages',
              //     style: TextStyle(color: Colors.grey),
              //   ),
              // ),

              Expanded(
                child: RefreshIndicator(
                  onRefresh: () async {
                    await getData();
                  },
                  child: ListView.separated(
                    physics: const AlwaysScrollableScrollPhysics(),
                    controller: scrollController,
                    padding: const EdgeInsets.all(
                      Layout.scaffoldBodyPadding,
                    ),
                    itemBuilder: (context, index) {
                      // if (!isMoreData && index == inboxData.data.length - 1) {
                      //   // return Padding(
                      //   //   padding: const EdgeInsets.only(bottom: 8.0),
                      //   //   child: Center(
                      //   //     child: Text(
                      //   //       Globals.language.noMoreData,
                      //   //       style: TextStyle(
                      //   //
                      //   //       ),
                      //   //     ),
                      //   //   ),
                      //   // );
                      // } else if (inboxData.data.isNotEmpty) {
                      return InboxItem(
                        item: inboxData.data[index],
                        onTap: () {
                          openDetails(inboxData.data[index], index);
                        },
                        onDelete: () {
                          onDelete(inboxData.data[index], index);
                        },
                      );
                      // }
                    },
                    separatorBuilder: (context, index) => const Divider(),
                    itemCount: inboxData.data.length,
                  ),
                ),
              ),
            ],
          ),
          const ActivityIndicator(),
          if (isNoData) emptyWidget(emptyDataMsg: emptyDataMsg)
        ],
      ),
    );
  }

  void openDetails(InboxData data, int index) {
    markReadInboxMsg(data.id);
    launchScreen(SimpleWebViewURLScreen(
        title: data.title,
        dataFetcher: getInboxMsgDetails(data.id),
        htmlDataKey: null));

    setState(() {
      inboxData.data[index].is_read = true;
    });
  }

  void onDelete(InboxData data, int index) {
    showAppDialog(
      dialogType: AppDialogType.confirmation,
      title: Globals.language.areYouSureYouWantToDelete,
      onAccept: () async {
        Navigator.of(context).pop();
        showAppActivity();
        await deleteInboxMsg(data.id).then((value) {
          if (value == true) {
            inboxData.data.removeAt(index);
            setState(() {
              isNoData = inboxData.data.isEmpty;
            });
            toast(Globals.language.done);
          } else {
            showErrorToast();
          }
          hideAppActivity();
        });
      },
    );
  }
}

class InboxItem extends StatelessWidget {
  final InboxData item;
  final void Function() onTap;
  final void Function() onDelete;
  const InboxItem(
      {super.key,
      required this.item,
      required this.onTap,
      required this.onDelete});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Card(
        margin: EdgeInsets.zero,
        child: Container(
          height: 100,
          color: item.is_read
              ? Theme.of(context).primaryColor.withValues(alpha: 0.2)
              : Theme.of(context).primaryColor,
          padding: const EdgeInsets.all(10),
          child: Row(
            children: [
              Icon(Icons.message,
                  color: item.is_read
                      ? Theme.of(context).primaryColor
                      : Colors.white),

              // CachedNetworkImage(
              //   imageUrl: item.imageURL,
              //   placeholder: (context, url) => Container(
              //     height: 40,
              //     width: 40,
              //     decoration: const BoxDecoration(
              //       shape: BoxShape.circle,
              //     ),
              //     child: const Center(
              //       child: CircularProgressIndicator(),
              //     ),
              //   ),
              //   imageBuilder: (context, imageProvider) {
              //     return Container(
              //       height: 40,
              //       width: 40,
              //       decoration: BoxDecoration(
              //         shape: BoxShape.circle,
              //         image: DecorationImage(
              //           image: imageProvider,
              //           fit: BoxFit.cover,
              //         ),
              //       ),
              //     );
              //   },
              //   errorWidget: (context, url, error) => Container(
              //     height: 40,
              //     width: 40,
              //     decoration: const BoxDecoration(
              //       shape: BoxShape.circle,
              //     ),
              //     child: const Center(
              //       child: Icon(
              //         Icons.error,
              //       ),
              //     ),
              //   ),
              // ),
              const SizedBox(
                width: 20,
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: Text(
                        item.title,
                        overflow: TextOverflow.ellipsis,
                        maxLines: 3,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    Text(
                      item.created_at,
                      style: const TextStyle(),
                    ),
                  ],
                ),
              ),
              InkWell(
                onTap: onDelete,
                child: const Card(
                  color: Colors.white,
                  child: Padding(
                    padding: EdgeInsets.all(2.0),
                    child: Icon(
                      Icons.delete,
                      color: Colors.red,
                    ),
                  ),
                ),
              )
            ],
          ),
        ),
      ),
    );
  }
}
