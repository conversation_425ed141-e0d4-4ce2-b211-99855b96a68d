import 'package:rider/app_exports.dart';

import 'package:flutter/services.dart';

class ChangeEmailScreen extends StatefulWidget {
  final String email;
  const ChangeEmailScreen({super.key, required this.email});

  @override
  ChangeEmailScreenState createState() => ChangeEmailScreenState();
}

class ChangeEmailScreenState extends State<ChangeEmailScreen> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  TextEditingController codeController = TextEditingController();
  TextEditingController emailController = TextEditingController();

  FocusNode codeFocus = FocusNode();
  FocusNode emailFocus = FocusNode();

  @override
  void initState() {
    super.initState();
    //
    // init();
  }

  @override
  void dispose() {
    if (Platform.isIOS) {
      SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle.dark);
    }
    super.dispose();
  }

  void init() async {
    //send verification code
    sendEmailVerificationCode(
      email: widget.email,
    ).then((value) {
      if (value == null) {
        toast(Globals.language.errorMsg);
      } else if (value['status'] == false) {
        toast(value['message']);
      } else if (value['status'] == true) {
        setState(() {});
        // toast(Globals.language.verification code)
      }
    }).onError((error, stackTrace) {
      toast(
        Globals.language.errorMsg,
      );
      handleError(
        error,
        stackTrace,
      );
    });
  }

  Future<void> changeEmail() async {
    hideKeyboard();
    if (_formKey.currentState!.validate()) {
      if (emailController.text.trim() == widget.email) {
        toast(Globals.language.enterYourNewEmail);
        return;
      }
      _formKey.currentState!.save();
      // if (codeController.text.trim().length != 6) {
      //   toast(Globals.language.enterVerificationCode);
      // } else {
      setState(() {});
      await changeRiderEmail(
        verificationCode: codeController.text.trim(),
        email: emailController.text.trim(),
      ).then((value) {
        setState(() {});
        if (value == null) {
          toast(Globals.language.errorMsg);
        } else if (value['status'] == true) {
          //show message
          showAppDialog(
            title: value['message'] ?? '',
            barrierDismissible: false,
            dialogType: AppDialogType.info,
            onAccept: () {
              Navigator.of(context).pop();
              Navigator.of(context).pop();
              Navigator.of(context).pop();
            },
          );
        } else if (value['status'] == false) {
          toast(value['message']);
        } else {
          toast(
            Globals.language.errorMsg,
          );
        }
      }).onError((error, stackTrace) {
        setState(() {});
        toast(Globals.language.errorMsg);
        handleError(error, stackTrace);
      });
      // }
    }
  }

  @override
  void setState(fn) {
    if (mounted) super.setState(fn);
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        hideKeyboard();
      },
      child: Scaffold(
        appBar: RoooAppbar(
          title: Globals.language.changeEmail,
          isDarkOverlay: false,
        ),
        body: Padding(
          padding: const EdgeInsets.all(10),
          child: Stack(
            children: [
              Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    AppTextField(
                      controller: emailController,
                      textFieldType: TextFieldType.EMAIL,
                      focus: emailFocus,
                      nextFocus: codeFocus,
                      errorThisFieldRequired: "required",
                      decoration: inputDecoration(context,
                          label: Globals.language.newEmail),
                    ),
                    // SizedBox(height: 20),
                    // Text(
                    //   Globals.language.verifyOTPHeading,
                    //   style: TextStyle(
                    //     color: getPrimaryTextColor(),
                    //   ),
                    // ),
                    // height10,
                    // Pinput(
                    //   controller: codeController,
                    //   length: 6,
                    //   onCompleted: (pin) {
                    //     // _otp = pin;
                    //   },
                    // ),
                  ],
                ),
              ),
              const ActivityIndicator(),
            ],
          ),
        ),
        bottomNavigationBar: Padding(
          padding: EdgeInsets.fromLTRB(
            10,
            10,
            10,
            Platform.isIOS ? 10 * 2 : 10,
          ),
          child: AppButtonWidget(
            text: Globals.language.changeEmail,
            textStyle: boldTextStyle(color: Colors.white),
            onTap: isAppActivityRunning.value ? () {} : changeEmail,
          ),
        ),
      ),
    );
  }
}
