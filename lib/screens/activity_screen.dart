// import 'package:flutter/material.dart';
// import 'package:taxibooking/components/custom_text.dart';
// import 'package:taxibooking/screens/CareScreen.dart';
// import 'package:taxibooking/screens/EditProfileScreen.dart';
// import 'package:taxibooking/screens/account_screen.dart';
// import 'package:taxibooking/utils/Colors.dart';
// import 'package:taxibooking/utils/Constants.dart';

// class ActivityScreen extends StatefulWidget {
//   const ActivityScreen({super.key});

//   @override
//   State<ActivityScreen> createState() => _ActivityScreenState();
// }

// class _ActivityScreenState extends State<ActivityScreen> {


//   @override
//   Widget build(BuildContext context) {
//     return DefaultTabController(
//       animationDuration: Duration.zero,
//       length: 3,
//       child: Scaffold(
//           bottomNavigationBar: bottoNavigationBarMenu(),
    
//           body: Tab<PERSON>ar<PERSON>iew(physics: NeverScrollableScrollPhysics(), children: [
//             EditProfileScreen(),
//             CareScreen(),
//             AccountScreen()
//           ])

      
//           ),
//     );
//   }

//   bottoNavigationBarMenu() {
//     return TabBar(indicator: BoxDecoration(), tabs: [
//       bottomTabBar(title: "Home", icon: Icons.home),
//       bottomTabBar(title: "Activity", icon: Icons.menu_book),
//       bottomTabBar(title: "Account", icon: Icons.person),
      
//     ]);
//   }

//   Tab bottomTabBar({required IconData icon, required String title}) {
//     return Tab(
//       height: 50,
      
//       child: Column(
        
//         children: [
//           height5,
          
//           Icon(icon, color: getBottomNavigationBarColor()),
//           CustomText(data: title, color: getBottomNavigationBarColor())
//         ],
//       ),
//     );
//   }
// }
