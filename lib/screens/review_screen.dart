import 'package:rider/app_exports.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:rider/services/ads_service.dart';

import '../global/models/ride_model.dart';

class ReviewScreen extends StatefulWidget {
  final Driver? driverData;
  final OnRideRequest rideRequest;

  const ReviewScreen({super.key, this.driverData, required this.rideRequest});

  @override
  ReviewScreenState createState() => ReviewScreenState();
}

class ReviewScreenState extends State<ReviewScreen> {
  GlobalKey<FormState> formKey = GlobalKey<FormState>();

  TextEditingController reviewController = TextEditingController();

  num rattingData = 5;
  int currentIndex = -1;

  OnRideRequest? servicesListData;
  List<dynamic> allMsgs = [];
  dynamic filteredMsgs;
  bool isLoadingMsgs = true;

  @override
  void initState() {
    Globals.isRideReviewScreenOpened = true;
    super.initState();
    init();
  }

  @override
  void dispose() {
    Globals.isRideReviewScreenOpened = false;
    hideAppActivity();
    super.dispose();
  }

  void init() async {
    getSuggestedMsgs();
  }

  void getFilteredMsgs() {
    filteredMsgs = allMsgs
        .where(
          (element) => element['rating'] == rattingData,
        )
        .first;
    setState(() {
      hideAppActivity();
    });
  }

  Future<void> getSuggestedMsgs() async {
    showAppActivity();
    getSuggestedReviewMsgs().then((value) {
      if (value == null) {
        toast(Globals.language.errorMsg);
      } else {
        if (value['status'] == true) {
          allMsgs = value['data'];
          getFilteredMsgs();
          setState(() {
            hideAppActivity();
          });
        } else {
          toast(value['message'] ?? Globals.language.errorMsg);
        }
      }
    }).onError((error, stackTrace) {
      toast(Globals.language.errorMsg);
      handleError(error, stackTrace);
    });
  }

  Future<void> userReviewData() async {
    if (formKey.currentState!.validate()) {
      formKey.currentState!.save();

      showAppActivity();

      Map req = {
        "ride_request_id": widget.rideRequest.id,
        "rating": rattingData,
        "comment": reviewController.text.trim(),
      };

      await ratingReview(request: req).then((value) {
        launchScreen(
            RideDetailScreen(
              orderId: widget.rideRequest.id!,
            ),
            isNewTask: true,
            pageRouteAnimation: PageRouteAnimation.SlideBottomTop);
      }).onError((error, stackTrace) {
        hideAppActivity();
        showErrorToast();
        handleError(error, stackTrace);
      });
    }
  }

  void onConnected() {
    log('Connected');
  }

  void onSubscribed(String topic) {
    log('Subscription confirmed for topic $topic');
  }

  @override
  void setState(fn) {
    if (mounted) super.setState(fn);
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        hideKeyboard();
      },
      child: Scaffold(
        appBar: RoooAppbar(
          title: Globals.language.howWasYourRide,
          isDarkOverlay: false,
        ),
        body: Stack(
          children: [
            SingleChildScrollView(
              child: Form(
                key: formKey,
                child: Padding(
                  padding: const EdgeInsets.only(
                      top: 20, left: 16, right: 16, bottom: 16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        // crossAxisAlignment: CrossAxisAlignment.c,
                        children: [
                          ClipRRect(
                            borderRadius: BorderRadius.circular(35),
                            child: commonCachedNetworkImage(
                              (widget.driverData?.profileImage ?? '')
                                  .validate(),
                              height: 80,
                              width: 80,
                              fit: BoxFit.cover,
                            ),
                          ),
                          const SizedBox(width: 16),
                          Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            // crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // SizedBox(height: 8),
                              Text(
                                '${widget.driverData?.firstName ?? ''.validate()} ${widget.driverData?.lastName ?? ''.validate()}',
                                style: boldTextStyle(
                                  size: 17,
                                ),
                              ),
                              // SizedBox(height: 4),
                              // Text('${widget.driverData?.email ?? ''.validate()}',
                              //     style: primaryTextStyle()),
                            ],
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      RatingBar.builder(
                        initialRating: rattingData * 1,
                        direction: Axis.horizontal,
                        glow: false,
                        allowHalfRating: false,
                        wrapAlignment: WrapAlignment.spaceBetween,
                        itemCount: 5,
                        itemPadding: const EdgeInsets.symmetric(horizontal: 8),
                        itemBuilder: (context, _) =>
                            const Icon(Icons.star, color: Colors.amber),
                        onRatingUpdate: (rating) {
                          formKey.currentState!.reset();
                          rattingData = rating;
                          reviewController.text = '';
                          getFilteredMsgs();
                        },
                      ),
                      const SizedBox(height: 16),
                      Text("Add Review", style: boldTextStyle()),
                      const SizedBox(height: 16),
                      AppTextField(
                        isValidationRequired: rattingData < 2,
                        controller: reviewController,
                        decoration: inputDecoration(context,
                            label: "Your review..."),
                        textFieldType: TextFieldType.OTHER,
                        minLines: 2,
                        maxLines: 5,
                        errorThisFieldRequired:
                            Globals.language.thisFieldRequired,
                      ),
                      const SizedBox(height: 16),
                      (filteredMsgs?['messages'] ?? []).isEmpty
                          ? const SizedBox()
                          : Text(Globals.language.suggestedReview,
                              style: boldTextStyle()),
                      const SizedBox(height: 16),
                      ...(filteredMsgs?['messages'] ?? [])
                          .map(
                            (e) => InkWell(
                              onTap: () {
                                reviewController.text = e;
                              },
                              child: Chip(
                                label: Text(
                                  e,
                                ),
                              ),
                            ),
                          )
                          .toList(),
                      const SizedBox(height: 10),
                      const Center(
                        child: AppAdWidget(adType: AdType.rideReview),
                      ),
                      const SizedBox(height: 20),
                      Padding(
                        padding: EdgeInsets.only(
                          bottom: Platform.isIOS ? 25 : 10,
                        ),
                        child: AppButton(
                          text: Globals.language.continueD,
                          width: MediaQuery.sizeOf(context).width,

                          // textStyle: boldTextStyle(color: Colors.white),
                          onPressed: () {
                            userReviewData();
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            const ActivityIndicator(),
          ],
        ),
      ),
    );
  }
}
