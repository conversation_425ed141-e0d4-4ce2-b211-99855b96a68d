import 'package:rider/app_exports.dart';

class DeleteAccountScreen extends StatefulWidget {
  const DeleteAccountScreen({super.key});

  @override
  DeleteAccountScreenState createState() => DeleteAccountScreenState();
}

class DeleteAccountScreenState extends State<DeleteAccountScreen> {

    AccountDeletionStatus? accountStatus;

  @override
  void initState() {
    super.initState();
    init();
  }

  void init() async {
    showAppActivity();
     var response = await checkAccountDeletionStatus();
    if (!response.status) {
      toast(response.message);
    } else {
      accountStatus = response.data!;
    setState((){

     hideAppActivity();
    });

    }
  }

  @override
  void setState(fn) {
    if (mounted) super.setState(fn);
  }

  @override
  void dispose() {
    hideAppActivity();
    super.dispose();
  }

  Future deleteAccount() async {
    showAppActivity();
    await deleteUser().then((value) async {
      if (value.status == true) {
        showAppDialog(
          dialogType: AppDialogType.info,
          barrierDismissible: false,
          title: value.message ??
              'Your account will be deleted only after being approved by the admin, but you can use your account until then',
          onAccept: () {
            Navigator.of(context).pop();
            Navigator.of(context).pop();
          },
        );
      } else if (value.status == null) {
        toast(value.message ?? '');
      } else {
        toast(
          Globals.language.errorMsg,
        );
      }
    }).catchError((error) {
      toast(error.toString());
    });
    hideAppActivity();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: RoooAppbar(
        title: Globals.language.deleteAccount,
        isDarkOverlay: false,
      ),
      body: Stack(
        children: [
          SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: _getChild(),
          ),
          const ActivityIndicator(),
        ],
      ),
    );
  }


   Widget _getChild() {
    if (accountStatus == null) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    } else if (accountStatus!.isAlreadyRequested || accountStatus!.isRejected) {
      Color cardColor;
      if (accountStatus!.isAlreadyRequested) {
        cardColor = Colors.yellow[100]!;
      } else {
        cardColor = Colors.red[100]!;
      }
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Card(
            color: cardColor,
            elevation: 2,
            margin: const EdgeInsets.symmetric(vertical: 8.0),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Text(accountStatus!.message,
                  style: const TextStyle(
                      color: Colors.black)), // Ensuring text is readable
            ),
          ),
          if (accountStatus!.isRejected) ...[
            const SizedBox(height: 24),
            const Text("Want to request agian?", style: TextStyle()),
            const SizedBox(height: 10),
            Center(
              child: AppButton(
                text: "Delete Account",
                onPressed: () async {
                  await deleteAccount();
                },
              ),
            ),
          ]
        ],
      );
    } else {
      return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(Globals.language.areYouSureYouWantPleaseReadAffect,
                    style: primaryTextStyle()),
                const SizedBox(height: 8),
                Text(Globals.deleteAccountText, style: primaryTextStyle()),
                const SizedBox(height: 24),
                Center(
                  child: AppButtonWidget(
                    text: Globals.language.deleteAccount,
                    onTap: () async {
                      await showAppDialog(
                        title: Globals.language.areYouSureYouWantDeleteAccount,
                        dialogType: AppDialogType.confirmation,
                        onAccept: () async {
                          Navigator.of(context).pop();
                          deleteAccount();
                        },
                      );
                    },
                  ),
                )
              ],
            );
    }
  }
}



class AccountDeletionStatus {
  bool isAlreadyRequested;
  bool isRejected;
  String message;
  AccountDeletionStatus(
      {required this.isAlreadyRequested,
      required this.isRejected,
      required this.message});
  factory AccountDeletionStatus.fromMap(Map<String, dynamic> json) {
    return AccountDeletionStatus(
      isAlreadyRequested: json['is_already_requested'],
      isRejected: json['is_rejected'],
      message: json['message'],
    );
  }
}

class AccountDeletionResponse {
  bool status;
  String message;
  AccountDeletionStatus? data;
  AccountDeletionResponse({
    required this.status,
    required this.message,
    required this.data,
  });
  factory AccountDeletionResponse.fromMap(Map<String, dynamic> json) =>
      AccountDeletionResponse(
        status: json["status"],
        message: json["message"],
        data: json["data"] == null
            ? null
            : AccountDeletionStatus.fromMap(json["data"]),
      );
}
