import 'package:rider/app_counters.dart';
import 'package:rider/app_exports.dart';
import 'package:rider/components/custom_text.dart';
import 'package:rider/features/push_notification_prefs/screens/push_notification_prefs_screen.dart';
import 'package:rider/features/ride-settings/screens/ride_related_settings_screen.dart';
import 'package:rider/screens/CareScreen.dart';
import 'package:rider/screens/EmergencyContactScreen.dart';
import 'package:rider/screens/wallet_screen.dart';

import 'package:rider/screens/HelpScreen.dart';
import 'package:rider/screens/NotificationScreen.dart';
import 'package:rider/screens/settings_screen.dart';
import 'package:rider/screens/inbox_screen.dart';

import 'package:state_beacon/state_beacon.dart';

import 'payment_cards_screen.dart';

class AppSettingScreen extends StatefulWidget {
  const AppSettingScreen({super.key});

  @override
  State<AppSettingScreen> createState() => _AppSettingScreenState();
}

class _AppSettingScreenState extends State<AppSettingScreen> {
  // String? _userRating = '';

  @override
  void initState() {
    // _userRating = Globals.sharedPrefs.getString(USER_RATING);

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    // final showInboxCount = inboxCount.watch(context);
    final showNotiCount = notificationCount.watch(context);
    final showCareCount = careCount.watch(context);

    return SafeArea(
      child: Scaffold(
        appBar: const RoooAppbar(title: "Account settings"),
        body: Column(
          children: [
         
            Expanded(
              child: Stack(
                children: [
                  SingleChildScrollView(
                    padding: const EdgeInsets.all(10),
                    child: Column(
                      children: [
                        height20,


                 
              

                        drawerButton(
                          title: Globals.language.wallet,
                          screenName: const WalletScreen(),
                          checkForProfile: true,
                        ),
                        height10,
                        drawerButton(
                          title: Globals.language.paymentCards,
                          screenName: const PaymentCardsScreen(),
                          checkForProfile: false,
                        ),
                        height10,
                        Stack(
                          children: [
                            drawerButton(
                              onTap: () {
                                careCount.reset();
                              },
                              title: Globals.language.roooCare,
                              screenName: const CareScreen(),
                              checkForProfile: true,
                            ),
                            showCareCount <= 0
                                ? const SizedBox()
                                : Positioned(
                                    right: 10,
                                    top: 0,
                                    bottom: 0,
                                    child: Center(
                                      child: Container(
                                        height: 30,
                                        width: 30,
                                        decoration: BoxDecoration(
                                          color: Colors.red,
                                          borderRadius:
                                              BorderRadius.circular(15),
                                        ),
                                        child: Center(
                                          child: Text(
                                            showCareCount.toString(),
                                            style: const TextStyle(
                                              color: Colors.white,
                                              fontSize: 11,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                          ],
                        ),
                        height10,
                        Stack(
                          children: [
                            drawerButton(
                                title: Globals.language.notification,
                                screenName: const NotificationScreen(),
                                checkForProfile: false,
                                onTap: () {
                                  notificationCount.value =0;
                                }),
                            showNotiCount <= 0
                                ? const SizedBox()
                                : Positioned(
                                    right: 10,
                                    top: 0,
                                    bottom: 0,
                                    child: Center(
                                      child: Container(
                                        height: 30,
                                        width: 30,
                                        decoration: BoxDecoration(
                                          color: Colors.red,
                                          borderRadius:
                                              BorderRadius.circular(15),
                                        ),
                                        child: Center(
                                          child: Text(
                                            showNotiCount.toString(),
                                            style: const TextStyle(
                                              color: Colors.white,
                                              fontSize: 11,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                          ],
                        ),
                        height10,
                        drawerButton(
                          title: "Account Settings",
                          screenName: const AccountSettingScreen(),
                          checkForProfile: false,
                        ),
                        height10,
                        drawerButton(
                          title: "Ride Settings",
                          screenName: const RideRelatedSettingsScreen(),
                          checkForProfile: false,
                        ),
                        height10,
                        drawerButton(
                          title: "Push Notification Preferences",
                          screenName: const PushNotificationPrefsScreen(),
                          checkForProfile: false,
                        ),
                        // if (Constants.isMyTestingApp)
                        //   drawerButton(
                        //     title: "Test",
                        //     screenName: const TestScreen(),
                        //     checkForProfile: false,
                        //   ),
                        height20,
                        AppButtonWidget(
                          text: "Logout",
                          onTap: () async {
                            await showAppDialog(
                              title: Globals
                                  .language.areYouSureYouWantToLogoutThisApp,
                              dialogType: AppDialogType.confirmation,
                              onAccept: () async {
                                _logOut();
                              },
                            );
                          },
                        ),
                        // AppButtonWidget(
                        //   text: Globals.language.logOut,

                        //   // color: Colors.white,
                        //   // child: Text(
                        //   //   Globals.language.logOut,
                        //   //   style: TextStyle(fontWeight: FontWeight.bold),
                        //   // ),
                        //   onTap: () async {
                        //     await showAppDialog(
                        //       context,
                        //       title: Globals.language.areYouSureYouWantToLogoutThisApp,
                        //       dialogType: DialogType.DELETE,
                        //       positiveText: Globals.language.yes,
                        //       negativeText: Globals.language.no,
                        //       onAccept: (c) async {
                        //
                        //         await logout();
                        //
                        //       },
                        //     );
                        //   },
                        // ),
                        // kDebugMode
                        //     ? ElevatedButton(

                        //         onPressed: () {
                        //           launchScreen(TestWidget());
                        //         },
                        //         child: Text(
                        //           'Test Task',
                        //           style: TextStyle(
                        //             color: Colors.black,
                        //           ),
                        //         ),
                        //       )
                        //     : const SizedBox()
                      ],
                    ),
                  ),
                  const ActivityIndicator(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _logOut() async {
    Navigator.of(context).pop();
    showAppActivity();
    var response = await logoutApi();
    if (!response.status) {
      toast(response.message);
    } else {
      await logOutSuccess();
    }
    hideAppActivity();
  }

  ListTile drawerButton(
      {required String title,
      dynamic screenName,
      required bool checkForProfile,
      void Function()? onTap}) {
    return ListTile(
      onTap: () {
        if (checkForProfile) {
          if (!Globals.user.isProfileComplete) {
            _handleIncompleteProfile();
            return;
          }
        }
        launchScreen(screenName);

        if (onTap != null) {
          onTap();
        }
      },
      title: Text(
        title,
        style: const TextStyle(
          color: Colors.black,
        ),
      ),
      tileColor: Colors.grey.shade200,
    );
  }

  custom_box(
      {required IconData icon,
      required String title,
      required double size,
      required void Function() onTap}) {
    return InkWell(
      onTap: () {
        onTap();
      },
      child: Container(

        height: size,
        padding: const EdgeInsets.symmetric(
          horizontal: 20,
        ),
        decoration: BoxDecoration(
            color: Colors.white, borderRadius: BorderRadius.circular(10)),
        child: SizedBox(
          width: double.infinity,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                color: Colors.black,
                size: 40,
              ),
              CustomText(
                data: title,
                color: Colors.black,
              )
            ],
          ),
        ),
      ),
    );
  }

  void _handleIncompleteProfile() {
    showAppDialog(
      dialogType: AppDialogType.error,
      title: "Your profile is incomplete. Please complete your profile",
      onAccept: () {
        Navigator.of(context).pop();
        launchScreen(
          RegisterScreen(
            countryDialCode: "+61",
            countryISOCode: "AU",
            phone: "",
            isSocialLogin: true,
            oldUser: Globals.user,
          ),
        );
      },
    );
  }
}

class CollapseableWidgets<T> extends StatefulWidget {
  final List<Widget> widgets;
  final List<T> data;
  final void Function(T obj) onWidgetTap;
  const CollapseableWidgets({
    super.key,
    required this.widgets,
    required this.onWidgetTap,
    required this.data,
  });

  @override
  State<CollapseableWidgets<T>> createState() => _CollapseableWidgetsState<T>();
}

class _CollapseableWidgetsState<T> extends State<CollapseableWidgets<T>> {
  bool _showList = false;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return _showList || widget.widgets.length == 1
        ? ListView.separated(
            separatorBuilder: (context, index) => height10,
            itemBuilder: (context, index) {
              return AnimationConfiguration.staggeredList(
                delay: const Duration(milliseconds: 70),
                position: index,
                duration: const Duration(seconds: 1),
                child: SlideAnimation(
                  child: InkWell(
                    onTap: () {
                      widget.onWidgetTap(widget.data[index]);
                    },
                    child: widget.widgets[index],
                  ),
                ),
              );
            },
            itemCount: widget.widgets.length,
          )
        : Stack(
            children: getStackedWidgets(),
          );
  }

  List<Widget> getStackedWidgets() {
    List<Widget> widgets = [];

    for (int i = 0; i < widget.widgets.length; i++) {
      widgets.add(
        Positioned(
          top: i * 40,
          left: 0,
          right: 0,
          child: InkWell(
            onTap: () {
              setState(() {
                _showList = true;
              });
            },
            child: AnimationConfiguration.staggeredList(
              delay: const Duration(milliseconds: 50),
              position: i * 4,
              duration: const Duration(seconds: 1),
              child: SlideAnimation(
                child: widget.widgets[i],
              ),
            ),
          ),
        ),
      );
    }

    return widgets;
  }
}
