import 'package:rider/app_exports.dart';
import 'package:rider/features/payment/screens/select_payment_for_additional_charges_screen.dart';

import '../global/models/ride_model.dart';

class WaitingChargesAndTipScreen extends StatefulWidget {
  final RideModel request;

  const WaitingChargesAndTipScreen({
    super.key,
    required this.request,
  });

  @override
  WaitingChargesAndTipScreenState createState() =>
      WaitingChargesAndTipScreenState();
}

class WaitingChargesAndTipScreenState
    extends State<WaitingChargesAndTipScreen> {
  GlobalKey<FormState> formKey = GlobalKey<FormState>();

  TextEditingController tipController = TextEditingController();

  num rattingData = 0;
  int currentIndex = -1;
  bool isMoreTip = false;
  List<PaymentModel> paymentList = [];
  String stripPaymentKey = '';
  String stripPaymentPublishKey = '';

  OnRideRequest? servicesListData;

  num? amount;
  String? holdPaymentId;
  String? paymentCardId;
  bool isTestType = true;

  @override
  void initState() {
    hideAppActivity();
    Globals.isWaitingAndTipChargesScreenOpened = true;
    super.initState();
  }

  @override
  dispose() {
    hideAppActivity();
    Globals.isWaitingAndTipChargesScreenOpened = false;
    super.dispose();
  }

  @override
  void setState(fn) {
    if (mounted) super.setState(fn);
  }

  Future<void> saveData() async {
    num refund = 0;
    if (holdPaymentId == null) {
      refund = (widget.request.charges?.refundableAmount ?? 0) -
          (num.tryParse(tipController.text.trim()) ?? 0);
      refund = num.parse(refund.toStringAsFixed(2));
      amount = 0;
    }

    await saveWaitingTimeAndTipPreAuth(
      walletAmount: null,
      holdPaymentId: holdPaymentId,
      paymentCardId: paymentCardId,
      rideRequestId: widget.request.onRideRequest!.id!,
      advanced_paid: widget.request.charges?.advancedPaid,
      due_amount: widget.request.charges?.dueAmount,
      refundable_amount: refund,
      tips: tipController.text.trim().isEmpty
          ? null
          : num.tryParse(tipController.text.trim()),
      waiting_charges: widget.request.charges?.waitingCharges,
      service_id: widget.request.onRideRequest!.serviceId!,
      pre_auth_amount: amount,
    ).then((value) async {
      if (value.status) {
        launchScreen(const DashboardWrapperScreen(), isNewTask: true);
      } else {
        setState(() {});
        toast(value.message);
      }
    }).onError((error, stackTrace) {
      setState(() {});
      toast(Globals.language.errorMsg);
      handleError(error, stackTrace);
    });
  }

  @override
  Widget build(BuildContext context) {

    return GestureDetector(
      onTap: () {
        hideKeyboard();
      },
      child: PopScope(
        canPop: false,
        child: Scaffold(
          appBar: RoooAppbar(
            title: Globals.language.chargesAndTip,
            hideBackButton: true,
            isDarkOverlay: false,
          ),
          body: Stack(
            children: [
              SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.all(10),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      (widget.request.charges?.items ?? []).isEmpty
                          ? const SizedBox()
                          : ListView.separated(
                              shrinkWrap: true,
                              physics: const NeverScrollableScrollPhysics(),
                              itemBuilder: (context, index) {
                                var data =
                                    widget.request.charges!.items![index];
                                return ListTile(tileColor: Colors.grey[300],
                                  title: Text(data.title!,style: const TextStyle(color: Colors.black),),
                                  trailing: Text(Constants.currencySymbol +
                                      data.amount.toString(),style: const TextStyle(color: Colors.black)),
                                );
                              },
                              separatorBuilder: (context, index) =>
                                  const SizedBox(height: 4,),
                              itemCount:
                                  (widget.request.charges?.items ?? []).length,
                            ),
                      widget.request.charges != null
                          ? ListTile(
                              tileColor: Colors.grey[300],
                              title: const Text("Due Amount",style: const TextStyle(color: Colors.black)),
                              trailing: Text(
                                  '${Constants.currencySymbol} ${widget.request.charges!.dueAmount}',style: const TextStyle(color: Colors.black)),
                            )
                          : const SizedBox(),
                      widget.request.charges != null
                          ? Padding(
                              padding: const EdgeInsets.symmetric(vertical: 10),
                              child: Container(
                                padding: const EdgeInsets.all(10),
                                color: Colors.grey.shade200,
                                child: const Row(
                                  children: [
                                    Icon(
                                      Icons.info,
                                      color: Colors.black,
                                    ),
                                    Expanded(
                                      child: Text(
                                        "You are required to pay these extra charges.",
                                        style: TextStyle(
                                          fontStyle: FontStyle.italic,
                                          color: Colors.black,
                                        ),
                                        softWrap: true,
                                        maxLines: 3,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            )
                          : const SizedBox(),
                      Form(
                        key: formKey,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Text(Globals.language.wouldYouLikeToAddTip,
                                    style: boldTextStyle()),
                                const SizedBox(width: 16),
                                // if (tipController.text.isNotEmpty)
                                //   inkWellWidget(
                                //     onTap: () {
                                //       currentIndex = -1;
                                //       tipController.clear();
                                //       setState(() {});
                                //     },
                                //     child: Icon(Icons.clear_all, size: 30),
                                //   )
                              ],
                            ),
                            const SizedBox(height: 10),
                            Wrap(
                              spacing: 10,
                              runSpacing: 16,
                              children: "10|20|30".split('|').map((e) {
                                return inkWellWidget(
                                  onTap: () {
                                    currentIndex = "10|20|30".indexOf(e);
                                    tipController.text = e;
                                    tipController.selection =
                                        TextSelection.fromPosition(TextPosition(
                                            offset: e.toString().length));
                                    setState(() {});
                                  },
                                  child: Container(
                                    padding: const EdgeInsets.symmetric(
                                        vertical: 6, horizontal: 10),
                                    decoration: BoxDecoration(
                                        color: Colors.grey.shade300,
                                        borderRadius:
                                            BorderRadius.circular(10)),
                                    child: Text(
                                        '${Constants.currencySymbol} $e',
                                        style: primaryTextStyle(size: 14,color: Colors.black,)),
                                  ),
                                );
                              }).toList(),
                            ),
                            const SizedBox(height: 20),
                            TextFormField(
                              controller: tipController,
                              decoration: inputDecoration(
                                context,
                                label: Globals.language.tip,
                              ),
                              keyboardType: TextInputType.number,
                              onChanged: (p0) {
                                setState(() {
                                  currentIndex = -1;
                                });
                              },
                            ),
                            const SizedBox(height: 20),
                            AppButtonWidget(
                              text: Globals.language.continueD,
                              width: MediaQuery.sizeOf(context).width,
                              textStyle: boldTextStyle(color: Colors.white),
                              onTap: () {
                                int tipAmount =
                                    (int.tryParse(tipController.text.trim()) ??
                                        0);
                                if (tipAmount > 0 && tipAmount < 10) {
                                  toast("Minimum tip is AUD 10");
                                  return;
                                }
                                if (widget.request.isPaymentCardUsed == false) {
                                  setState(() {});
                                  saveData();
                                } else {
                                  num payableAmount = (tipAmount +
                                          (widget.request.charges?.dueAmount ??
                                              0)) -
                                      (widget.request.charges
                                              ?.refundableAmount ??
                                          0);
                                  if (payableAmount <= 0) {
                                    setState(() {});
                                    saveData();
                                    return;
                                  }

                                  num refund = (widget.request.charges
                                              ?.refundableAmount ??
                                          0) -
                                      payableAmount;
                                  refund = refund < 0 ? 0 : refund;

                                  launchScreen(
                                    SelectPaymentForAdditionalCharges(
                                      payableAmount: payableAmount,
                                      rideId: widget.request.onRideRequest!.id!,
                                      chargesType: AdditionalChargesType.tip,
                                      tipAndWaitingData: TipAndWaitingData(
                                        advancedPaid: widget
                                            .request.charges?.advancedPaid,
                                        dueAmount:
                                            widget.request.charges?.dueAmount,
                                        refundableAmount: refund,
                                        waitingCharges: widget
                                            .request.charges?.waitingCharges,
                                        serviceId: widget
                                            .request.onRideRequest!.serviceId!,
                                        tips: tipAmount > 0 ? tipAmount : null,
                                      ),
                                    ),
                                  );
                                }
                              },
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const ActivityIndicator(),
            ],
          ),
        ),
      ),
    );
  }
}
