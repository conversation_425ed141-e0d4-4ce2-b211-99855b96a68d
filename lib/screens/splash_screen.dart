import 'package:rider/app_exports.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  SplashScreenState createState() => SplashScreenState();
}

class SplashScreenState extends State<SplashScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller =
        AnimationController(vsync: this, duration: const Duration(seconds: 2));
    _animation = CurvedAnimation(parent: _controller, curve: Curves.easeInOut);
    _controller.repeat(reverse: true);
    init();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void init() async {
    String? localUser = Globals.sharedPrefs.getString(SPKeys.user);
    if (localUser != null) {
      try {
        Globals.user = UserModel.fromJson(jsonDecode(localUser));

        Globals.isUserLoggedIn = true;
      } catch (e) {}
    }

    await Future.delayed(const Duration(seconds: 3));

    bool isFirstTime = Globals.sharedPrefs.getBool(SPKeys.isFirstTime) ?? true;

    if (isFirstTime) {
      launchScreen(const WalkThroughScreen(),
          pageRouteAnimation: PageRouteAnimation.Slide);
    } else {
      if (Globals.isUserLoggedIn) {
        launchScreen(
            const DashboardWrapperScreen(
              isRecentSignUp: false,
            ),
            pageRouteAnimation: PageRouteAnimation.Slide,
            isNewTask: true);
      } else {
        launchScreen(const LoginScreen(),
            pageRouteAnimation: PageRouteAnimation.Slide, isNewTask: true);
      }
    }
  }

  @override
  void setState(fn) {
    if (mounted) super.setState(fn);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ScaleTransition(
              scale: _animation,
              child: Image.asset(
                Theme.of(context).brightness == Brightness.dark
                    ? Assets.appLogoWhite
                    : Assets.appLogoBlack,
                    width: MediaQuery.sizeOf(context).width / 2,
                // fit: BoxFit.contain,
              ),
            ),
            Text(Globals.language.ROOOTxt,
                style: boldTextStyle( size: 22)),
            const SizedBox(height: 3),
            Text(
              "Rider",
              style: boldTextStyle( size: 22),
            ),
            const SizedBox(height: 5),
            const Text(
              '"Arrive in style and comfort"',
              style: TextStyle(
                fontSize: 15,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
