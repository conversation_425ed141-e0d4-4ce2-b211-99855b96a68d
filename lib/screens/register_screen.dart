import 'package:flutter/services.dart';
import 'package:rider/app_exports.dart';

class RegisterScreen extends StatefulWidget {
  final String countryISOCode;
  final String countryDialCode;
  final String phone;
  final bool isSocialLogin;
  final UserModel? oldUser;

  const RegisterScreen({
    super.key,
    required this.countryDialCode,
    required this.countryISOCode,
    required this.phone,
    required this.isSocialLogin,
    required this.oldUser,
  });

  @override
  RegisterScreenState createState() => RegisterScreenState();
}

class RegisterScreenState extends State<RegisterScreen> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  final TextEditingController _firstController = TextEditingController();
  final TextEditingController _lastNameController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();

  final TextEditingController _passwordController = TextEditingController();

  // final TextEditingController _userNameController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  final FocusNode _firstNameFocus = FocusNode();
  final FocusNode _lastNameFocus = FocusNode();
  // final FocusNode _userNameFocus = FocusNode();
  final FocusNode _emailFocus = FocusNode();
  final FocusNode _passwordFocus = FocusNode();

  final FocusNode _phoneFocus = FocusNode();

  bool _isPhoneVerified = false;
  bool _isAcceptedTc = false;

  String _selectedGender = Gender.select;
  final List<String> _genders = [
    Gender.select,
    Gender.male,
    Gender.female,
    Gender.preferNotToSay,
    Gender.other
  ];
  String _otherGender = "";

  String _dialCode = '';
  final List<String> _oldVerifiedPhone = [];

  bool _isLoadingProvinces = true;

  bool _isEmailOTPSent = false;
  bool _isEmailOTPVerified = false;
  final TextEditingController _emailOTPController = TextEditingController();
  Timer? _emailVerificationTimer;
  int _emailOTPCountdownTimer = 60;
  bool _isNotifiedForTheEmailVerification = false;

  ProvinceModel? _selectedProvince;
  List<ProvinceModel> _provinceData = [];
  List<ProvinceModel> _filtereedProvinceData = [];
  final TextEditingController _provinceInputController =
      TextEditingController();
  final FocusNode _provinceInputFocusNode = FocusNode();

  @override
  void dispose() {
    hideAppActivity();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    _dialCode = widget.countryDialCode;
    _phoneController.text = widget.phone;
    _isPhoneVerified = !widget.isSocialLogin;
    _isEmailOTPVerified = widget.oldUser?.email != null;
    _oldVerifiedPhone.add("${widget.countryDialCode}${widget.phone}");

    init();
  }

  void init() async {
    if (widget.oldUser != null) {
      _firstController.text = widget.oldUser!.firstName ?? "";
      _lastNameController.text = widget.oldUser!.lastName ?? "";
      // _userNameController.text = widget.oldUser!.username!;
      _emailController.text = widget.oldUser!.email ?? "";

      _selectedGender = widget.oldUser!.gender ?? _selectedGender;
      _otherGender = widget.oldUser!.otherGenderText ?? "";
    }

    _provinceInputFocusNode.addListener(() {
      if (!_provinceInputFocusNode.hasFocus) {
        setState(() {
          _filtereedProvinceData = [];
        });
      } else {
        Scrollable.ensureVisible(
          _provinceInputFocusNode.context!,
          alignment: 0.1,
          duration: const Duration(milliseconds: 300),
        );

        if (_selectedProvince == null) {
          _filtereedProvinceData = _getFilteredProvinceData("");

          setState(() {
            _selectedProvince = null;
          });
        }
      }
    });

    _getProvinces();
  }

  Future<void> _getProvinces() async {
    showAppActivity();
    _isLoadingProvinces = true;
    var result = await getProvincesData();
    if (result == null) {
      toast(Globals.language.errorMsg);
    } else if (!result.status) {
      toast(result.message);
    } else {
      _provinceData = result.data!;
      _selectedProvince = _provinceData[0];
      _isLoadingProvinces = false;
      _updateState();
    }
  }

  void _updateState() {
    if (!_isLoadingProvinces) {
      setState(() {
        hideAppActivity();
      });
    }
  }

  @override
  void setState(fn) {
    if (mounted) super.setState(fn);
  }

  Future<void> register() async {
    hideKeyboard();
    if (!_isEmailOTPVerified) {
      toast("Please verify your email address");
      return;
    }

    if (_formKey.currentState!.validate()) {
      _formKey.currentState!.save();
      if (_phoneController.text.isEmpty) {
        toast("Please enter mobile number");
        return;
      }
      if (!_isPhoneVerified) {
        toast("Please verify mobile number");
        return;
      }

      if (_selectedGender == Gender.select) {
        toast("Please select gender");
        return;
      } else if (_selectedGender == Gender.other &&
          _otherGender.trim().isEmpty) {
        toast("Please enter gender");
        return;
      }
      if (_isAcceptedTc) {
        showAppActivity();
        Map<String, dynamic> req = {
          "uid": widget.oldUser?.uid,
          "id": widget.oldUser?.id,
          "user_type": UserType.rider,
          'first_name': _firstController.text.trim(),
          'last_name': _lastNameController.text.trim(),
          // 'username': _userNameController.text.trim(),
          'email': _emailController.text.trim(),
          "contact_number": _dialCode + _phoneController.text,
          "gender": _selectedGender,
          "other_gender_text": _selectedGender == "other" ? _otherGender : null,
          "region_id": _selectedProvince!.regionId,
          "province_id": _selectedProvince!.id,
          "password": _passwordController.text,
        };

        RegisterResponse response;

        try {
          if (widget.isSocialLogin) {
            response = await updateUser(
              user: req,
            );
          } else {
            response = await signUpApi(
              req,
            );
          }

          hideAppActivity();

          if (response.status == false) {
            toast(response.message);
            return;
          }

          var user = response.data!;

          bool result = await Globals.sharedPrefs.setString(
            SPKeys.user,
            jsonEncode(user.toJson()),
          );
          if (!result) {
            toast(Globals.language.errorMsg);
          } else {
            Globals.user = user;
            Globals.isUserLoggedIn = true;
            if (user?.profileImage != null) {
              Globals.sharedPrefs.setString("USER_PROFILE", user.profileImage!);
            }

            launchScreen(
              const DashboardWrapperScreen(
                isRecentSignUp: true,
              ),
              isNewTask: true,
              pageRouteAnimation: PageRouteAnimation.Slide,
            );
          }
        } catch (e) {
          toast(Globals.language.errorMsg);
        }
      } else {
        toast("Please accept T&C and Privacy Policy");
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        hideKeyboard();
      },
      child: Scaffold(
        appBar: RoooAppbar(
          title: widget.oldUser == null
              ? 'Create an account'
              : 'Update your details',
          isDarkOverlay: false,
        ),
        body: Stack(
          children: [
            SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 16),
                    Text(Globals.language.createAccount,
                        style: boldTextStyle(size: 22)),
                    const SizedBox(height: 8),
                    Text(Globals.language.createYourAccountToContinue,
                        style: primaryTextStyle()),
                    const SizedBox(height: 32),
                    AppTextField(
                      controller: _firstController,
                      focus: _firstNameFocus,
                      nextFocus: _lastNameFocus,
                      autoFocus: false,
                      textFieldType: TextFieldType.NAME,
                      errorThisFieldRequired: "Required",
                      label: Globals.language.firstName,
                    ),
                    const SizedBox(height: 20),
                    AppTextField(
                      controller: _lastNameController,
                      focus: _lastNameFocus,
                      nextFocus: _emailFocus,
                      autoFocus: false,
                      textFieldType: TextFieldType.NAME,
                      isValidationRequired: true,
                      errorThisFieldRequired: "Required",
                      label: Globals.language.lastName,
                    ),
                    const SizedBox(height: 20),
                    AppTextField(
                      controller: _emailController,
                      focus: _emailFocus,
                      nextFocus: _passwordFocus,
                      autoFocus: false,
                      textFieldType: TextFieldType.EMAIL,
                      keyboardType: TextInputType.emailAddress,
                      label: Globals.language.email,
                      readOnly: _isEmailOTPVerified,
                      decoration: InputDecoration(
                          suffixIcon: _isEmailOTPVerified
                              ? const Icon(Icons.email_outlined)
                              : null,
                          labelText: "Email"),
                    ),
                    const SizedBox(height: 20),
                    !_isEmailOTPSent || _isEmailOTPVerified
                        ? const SizedBox()
                        : Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                "Please enter the verification code",
                                style: boldTextStyle(size: 16),
                              ),
                              height20,
                              Center(
                                child: Pinput(
                                  controller: _emailOTPController,
                                  defaultPinTheme: PinTheme(
                                    width: 45,
                                    height: 45,
                                    textStyle: const TextStyle(
                                      fontSize: 20,
                                      fontWeight: FontWeight.bold,
                                    ),
                                    decoration: BoxDecoration(
                                      border: Border.all(
                                        width: 1,
                                        color: Theme.of(context)
                                            .colorScheme
                                            .primary,
                                      ),
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                  ),
                                  focusedPinTheme: PinTheme(
                                    width: 45,
                                    height: 45,
                                    textStyle: const TextStyle(
                                      fontSize: 20,
                                      fontWeight: FontWeight.bold,
                                    ),
                                    decoration: BoxDecoration(
                                      border: Border.all(
                                        width: 2,
                                        color: Theme.of(context)
                                            .colorScheme
                                            .primary,
                                      ),
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                  ),
                                  length: 6,
                                  onCompleted: (pin) {
                                    // _otp = pin;
                                  },
                                ),
                              ),
                              height20,
                              Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Text(
                                    "Code not received?",
                                    style: primaryTextStyle(),
                                  ),
                                  width10,
                                  _emailOTPCountdownTimer == 0
                                      ? TextButton(
                                          onPressed: () {
                                            _sendEmailOtp(
                                                email: _emailController.text
                                                    .trim(),
                                                resendOTP: true);
                                          },
                                          child: Text(
                                            "Resend",
                                            style: boldTextStyle(
                                                color: Theme.of(context)
                                                    .colorScheme
                                                    .primary),
                                          ),
                                        )
                                      : Text(
                                          "Resend in $_emailOTPCountdownTimer s",
                                          style: primaryTextStyle(),
                                        ),
                                ],
                              ),
                              height20,
                            ],
                          ),
                    height10,
                    _isEmailOTPVerified
                        ? const SizedBox()
                        : AppButton(
                            text: _isEmailOTPSent
                                ? "Verify code"
                                : "Send email verification code",
                            onPressed: () {
                              if (!_isEmailOTPSent) {
                                if (_emailController.text.isEmpty ||
                                    !_emailController.text.validateEmail()) {
                                  toast("Please enter a valid email");
                                  return;
                                }
                                _showEmailVerificationDialog();
                              } else {
                                _verifyEmailOtp(
                                    email: _emailController.text.trim(),
                                    otp: _emailOTPController.text.trim());
                              }
                            },
                            width: double.infinity,
                          ),
                    AppTextField(
                      controller: _passwordController,
                      focus: _passwordFocus,
                      nextFocus: _phoneFocus,
                      autoFocus: false,
                      textFieldType: TextFieldType.PASSWORD,
                      label: Globals.language.password,
                      validator: (v) {
                        if (v!.length < 8) {
                          return "Please enter atleast 8 characters";
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 20),
                    AppTextField(
                      controller: _phoneController,
                      focus: _phoneFocus,
                      autoFocus: false,
                      textFieldType: TextFieldType.PHONE,
                      keyboardType: TextInputType.phone,
                      maxLength: 10,
                      readOnly: _isPhoneVerified,
                      inputFormatters: [
                        FilteringTextInputFormatter.digitsOnly,
                      ],
                      decoration: InputDecoration(
                        counterText: "",
                        labelText: "Mobile number",
                        floatingLabelBehavior: FloatingLabelBehavior.always,
                        prefix: Padding(
                          padding: const EdgeInsets.only(right: 8),
                          child: Text(
                            "+61",
                            style: AppTextStyles.title,
                          ),
                        ),
                      ),
                      validator: (value) {
                        if ((value?.length ?? 0) < 9) {
                          return "Please enter a valid mobile number";
                        }
                        return null;
                      },
                      onChanged: (value) {
                        setState(() {
                          _isPhoneVerified = (_oldVerifiedPhone
                              .contains("$_dialCode${_phoneController.text}"));
                        });
                      },
                    ),
                    _getPhoneVerificationWidget(),
                    const SizedBox(height: 20),
                    TextFormField(
                      focusNode: _provinceInputFocusNode,
                      decoration: const InputDecoration(
                        label: Text("State"),
                        hintText: "Type to search...",
                        suffixIcon: Icon(
                          Icons.arrow_drop_down,
                        ),
                      ),
                      controller: _provinceInputController,
                      validator: (value) {
                        if (_selectedProvince == null) {
                          return "Please select a valid state";
                        }
                        return null;
                      },
                      onTap: () {
                        setState(() {
                          _filtereedProvinceData = _provinceData;
                        });
                      },
                      onChanged: (value) {
                        _filtereedProvinceData =
                            _getFilteredProvinceData(value);
                        if (_filtereedProvinceData.isEmpty) {
                          _filtereedProvinceData.add(
                            ProvinceModel(
                              id: -1,
                              regionId: -1,
                              regionName: "",
                              provinceName: "No state found",
                            ),
                          );
                        }
                        setState(() {
                          _selectedProvince = null;
                        });
                      },
                    ),
                    Card(
                      child: Container(
                        color: Theme.of(context).brightness == Brightness.dark
                            ? Colors.grey[900]
                            : Colors.grey.shade100,
                        height: _filtereedProvinceData.isNotEmpty ? 200 : 0,
                        child: ListView.separated(
                          itemBuilder: (context, index) {
                            return ListTile(
                              title: Text(
                                _filtereedProvinceData[index].provinceName,
                                style: TextStyle(
                                  color:
                                      Theme.of(context).colorScheme.onTertiary,
                                ),
                              ),
                              subtitle: Text(
                                _filtereedProvinceData[index].regionName,
                                style: TextStyle(
                                  color:
                                      Theme.of(context).colorScheme.onTertiary,
                                ),
                              ),
                              onTap: () {
                                if (_filtereedProvinceData[index].id == -1) {
                                  return;
                                }
                                _selectedProvince =
                                    _filtereedProvinceData[index];
                                _provinceInputController.text =
                                    _selectedProvince!.provinceName;
                                setState(() {
                                  _filtereedProvinceData = [];
                                });
                                _provinceInputFocusNode.unfocus();
                              },
                            );
                          },
                          separatorBuilder: (context, index) => Divider(
                            color:
                                Theme.of(context).brightness == Brightness.dark
                                    ? Colors.grey[700]
                                    : null,
                          ),
                          itemCount: _filtereedProvinceData.length,
                        ),
                      ),
                    ),
                    height20,
                    Text(Globals.language.gender, style: boldTextStyle()),
                    const SizedBox(height: 10),
                    DropdownButtonFormField(
                      // decoration: inputDecoration(context, label: ""),
                      value: _selectedGender,
                      onChanged: (String? value) {
                        setState(() {
                          _selectedGender = value!;
                        });
                      },
                      items: _genders
                          .map(
                            (value) => DropdownMenuItem(
                              value: value,
                              child: Text(
                                value.capitalizeFirstLetter(),
                              ),
                            ),
                          )
                          .toList(),
                    ),
                    _selectedGender != Gender.other
                        ? const SizedBox()
                        : Padding(
                            padding: const EdgeInsets.only(top: 16.0),
                            child: AppTextField(
                              textFieldType: TextFieldType.OTHER,
                              label: "Please enter",
                              initialValue: _otherGender,
                              onChanged: (value) {
                                _otherGender = value;
                              },
                            ),
                          ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        SizedBox(
                          height: 18,
                          width: 18,
                          child: Checkbox(
                            materialTapTargetSize:
                                MaterialTapTargetSize.shrinkWrap,
                            value: _isAcceptedTc,
                            shape:
                                RoundedRectangleBorder(borderRadius: radius(2)),
                            onChanged: (v) async {
                              _isAcceptedTc = v!;
                              setState(() {});
                            },
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: RichText(
                            text: TextSpan(children: [
                              TextSpan(
                                text: Globals.language.iAgreeToThe + " ",
                                style: primaryTextStyle(
                                  color: AppColors.whiteColor(context),
                                  size: 14,
                                  weight: FontWeight.bold,
                                ),
                              ),
                              TextSpan(
                                text: Globals.language.termsConditions,
                                style: boldTextStyle(
                                    size: 14,
                                    color: AppColors.lightThemePrimaryColor,
                                    weight: FontWeight.bold),
                                recognizer: TapGestureRecognizer()
                                  ..onTap = () {
                                    launchScreen(
                                        const TermsAndConditionsScreen(),
                                        pageRouteAnimation:
                                            PageRouteAnimation.Slide);
                                  },
                              ),
                              TextSpan(
                                  text: ' & ',
                                  style: primaryTextStyle(
                                      color: AppColors.whiteColor(context),
                                      weight: FontWeight.bold,
                                      size: 14)),
                              TextSpan(
                                text: Globals.language.privacyPolicy,
                                style: boldTextStyle(
                                    size: 14,
                                    color: AppColors.lightThemePrimaryColor),
                                recognizer: TapGestureRecognizer()
                                  ..onTap = () {
                                    launchScreen(const PrivacyPolicyScreen(),
                                        pageRouteAnimation:
                                            PageRouteAnimation.Slide);
                                  },
                              ),
                            ]),
                            textAlign: TextAlign.left,
                          ),
                        )
                      ],
                    ),
                    const SizedBox(height: 16),
                    AppButton(
                      width: double.infinity,
                      text: widget.oldUser == null
                          ? Globals.language.signUp
                          : "Update Details",
                      onPressed: () async {
                        register();
                      },
                    ),
                    widget.isSocialLogin
                        ? const SizedBox()
                        : const SizedBox(height: 20),
                    widget.isSocialLogin
                        ? const SizedBox()
                        : Align(
                            alignment: Alignment.center,
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(Globals.language.alreadyHaveAnAccount,
                                    style: primaryTextStyle()),
                                const SizedBox(width: 8),
                                inkWellWidget(
                                  onTap: () {
                                    launchScreen(const LoginScreen(),
                                        isNewTask: true,
                                        pageRouteAnimation:
                                            PageRouteAnimation.Slide);
                                  },
                                  child: Text(Globals.language.logIn,
                                      style: boldTextStyle()),
                                ),
                              ],
                            ),
                          ),
                    if (Platform.isIOS)
                      const SizedBox(
                        height: 30,
                      )
                  ],
                ),
              ),
            ),
            const ActivityIndicator(),
          ],
        ),
      ),
    );
  }

  Widget _getPhoneVerificationWidget() {
    if (_isPhoneVerified) {
      return const SizedBox();
    }
    return Center(
      child: AppButton(text: "Verify", onPressed: _sendOTP),
    );
  }

  Future<void> _sendOTP() async {
    if (_formKey.currentState!.validate()) {
      if (_phoneController.text.isEmpty) {
        toast("Please enter mobile number");
        return;
      }
      showAppActivity();
      var response = await sendOTP(_dialCode + _phoneController.text);
      hideAppActivity();
      if (response.status == false) {
        toast(response.message);
      } else if (response.status == true) {
        _verifyOTP(response.data!.key);
      }
    }
  }

  Future<void> _verifyOTP(String key) async {
    bool? result = await launchScreen(
      VerifyOTPScreen(
        countryDialCode: _dialCode,
        phone: _phoneController.text,
        otpVerificationKey: key,
        countryISOCode: '',
        isSocialLogin: true,
      ),
    );
    if (result == true) {
      _oldVerifiedPhone.add("$_dialCode${_phoneController.text}");
      setState(() {
        _isPhoneVerified = true;
      });
    }
  }

  List<ProvinceModel> _getFilteredProvinceData(String text) {
    return _provinceData
        .where((o) => o.provinceName.toLowerCase().contains(text.toLowerCase()))
        .toList();
  }

  void _showEmailVerificationDialog() {
    if (_isNotifiedForTheEmailVerification) {
      _sendEmailOtp(email: _emailController.text.trim(), resendOTP: true);
      return;
    }
    showAppDialog(
      dialogType: AppDialogType.info,
      onAccept: () {
        _isNotifiedForTheEmailVerification = true;
        _sendEmailOtp(email: _emailController.text.trim(), resendOTP: false);
      },
      title:
          "A verification code will be sent to your email. Please verify your email address to continue",
    );
  }

  Future<void> _sendEmailOtp({
    required String email,
    required bool resendOTP,
  }) async {
    if (!resendOTP) {
      Navigator.of(context).pop();
    }
    _showSendingEmailOTP();
    var response = await sendOTPToEmail(email: email);
    Navigator.of(context).pop();
    hideKeyboard();
    if (!response.status) {
      toast(response.message);
      return;
    }
    setState(() {
      _isEmailOTPSent = true;
    });
    _emailOTPCountdownTimer = 60;
    _emailVerificationTimer = Timer.periodic(const Duration(seconds: 1), (v) {
      if (_emailOTPCountdownTimer == 0) {
        _emailVerificationTimer?.cancel();
      } else {
        setState(() {
          _emailOTPCountdownTimer = _emailOTPCountdownTimer - 1;
        });
      }
    });
  }

  void _showSendingEmailOTP() {
    showAppActivityDialog(
      context: context,
      title: "Sending email verification code. Please wait...",
    );
  }

  void _showVerifyingEmail() {
    showAppActivityDialog(
      context: context,
      title:
          "We are verifying your email. Please wait while we verify your email address.",
    );
  }

  Future<void> _verifyEmailOtp({
    required String email,
    required String otp,
  }) async {
    _showVerifyingEmail();
    var response = await verifyEmailOTP(
      email: email,
      otp: _emailOTPController.text.trim(),
    );
    Navigator.of(context).pop();
    if (!response.status) {
      toast(response.message);
      return;
    }
    setState(() {
      _isEmailOTPVerified = true;
    });
  }
}

class ProvinceModel {
  int id;
  String provinceName;
  int regionId;
  String regionName;

  ProvinceModel({
    required this.id,
    required this.provinceName,
    required this.regionId,
    required this.regionName,
  });
  factory ProvinceModel.fromJson(Map<String, dynamic> json) {
    return ProvinceModel(
      id: json['id'],
      provinceName: json['state'],
      regionId: json['region_id'],
      regionName: json['region_name'],
    );
  }
}

class ProvinceModelResponse {
  bool status;
  String message;
  List<ProvinceModel>? data;

  ProvinceModelResponse({
    required this.status,
    required this.message,
    required this.data,
  });

  factory ProvinceModelResponse.fromJson(Map<String, dynamic> json) {
    return ProvinceModelResponse(
      status: json['status'],
      message: json['message'],
      data: (json['data'] as List?)
          ?.map((e) => ProvinceModel.fromJson(e))
          .toList(),
    );
  }
}
