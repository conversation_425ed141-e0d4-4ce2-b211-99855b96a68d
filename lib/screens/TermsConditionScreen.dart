import 'package:rider/app_exports.dart';
import 'package:webview_flutter/webview_flutter.dart';

class TermsAndConditionsScreen extends StatefulWidget {
  const TermsAndConditionsScreen({super.key});

  @override
  TermsAndConditionsScreenState createState() =>
      TermsAndConditionsScreenState();
}

class TermsAndConditionsScreenState extends State<TermsAndConditionsScreen> {
  String? url;

  bool webPageLoaded = false;

  WebViewController controller = WebViewController()
    ..setJavaScriptMode(JavaScriptMode.unrestricted);

  @override
  void initState() {
    super.initState();
    init();
  
  }

  @override
  void dispose() {
    hideAppActivity();
    super.dispose();
  }

  void init() async {
    await getTAndC().then((value) {
      if (value == null) {
        toast(Globals.language.errorMsg);
        return;
      }
      if (value['data']['link'] != null) {
        setState(() {
          url = value['data']['link'];
        });
        controller.setNavigationDelegate(NavigationDelegate(
          onPageFinished: (url) {
            setState(() {
              webPageLoaded = true;
            });
     if (Theme.of(context).brightness == Brightness.dark) {
              controller.runJavaScript('''
                document.body.style.backgroundColor = '#121212';
                document.body.style.color = '#FFFFFF';
                document.querySelectorAll('*').forEach(element => {
                  // Set background color for all elements
                  element.style.backgroundColor = '#121212';
                  
                  if (element.tagName === 'A') {
                    element.style.color = '#90CAF9';
                  }
                  
                  // Style headings
                  if (element.tagName === 'H1' || element.tagName === 'H2' || element.tagName === 'H3' || 
                      element.tagName === 'H4' || element.tagName === 'H5' || element.tagName === 'H6') {
                    element.style.color = '#FFFFFF';
                  }
                  
                  // Style paragraphs and text containers
                  if (element.tagName === 'P' || element.tagName === 'SPAN' || element.tagName === 'DIV' ||
                      element.tagName === 'LI' || element.tagName === 'UL' || element.tagName === 'OL') {
                    element.style.color = '#E0E0E0';
                  }
                  
                  // Style tables
                  if (element.tagName === 'TABLE' || element.tagName === 'TR' || element.tagName === 'TD' ||
                      element.tagName === 'TH') {
                    element.style.backgroundColor = '#121212';
                    element.style.color = '#E0E0E0';
                    element.style.borderColor = '#333333';
                  }
                  
                  // Style code blocks and pre elements
                  if (element.tagName === 'CODE' || element.tagName === 'PRE') {
                    element.style.backgroundColor = '#1E1E1E';
                    element.style.color = '#E0E0E0';
                  }
                  
                  // Style blockquotes
                  if (element.tagName === 'BLOCKQUOTE') {
                    element.style.backgroundColor = '#1E1E1E';
                    element.style.color = '#E0E0E0';
                    element.style.borderLeftColor = '#90CAF9';
                  }
                });
              ''');
            }
          },
        ));
        controller.loadRequest(Uri.parse(url!));
      }
    }).catchError((error) {
      log(error.toString());
    });
  }

  @override
  void setState(fn) {
    if (mounted) super.setState(fn);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: RoooAppbar(
          title: Globals.language.termsConditions,
          isDarkOverlay: false,
        ),
        body: SizedBox(
          height: MediaQuery.sizeOf(context).height - 100,
          child: Stack(
            children: [
              (url == null)
                  ? const SizedBox()
                  : WebViewWidget(
                      controller: controller,
                    ),
              webPageLoaded ? const SizedBox() : loaderWidget(),
            ],
          ),
        ));
  }
}
