

import 'package:rider/app_exports.dart';
import 'package:rider/screens/CreateRideHelpTabScreen.dart';
import 'package:rider/screens/all_rides_screen.dart';

class RideHelpScreen extends StatefulWidget {
  final Function() indicatorUpdater;

  const RideHelpScreen({
    super.key,
    required this.indicatorUpdater,
  });
  @override
  CareScreenState createState() => CareScreenState();
}

class CareScreenState extends State<RideHelpScreen> {
  int currentPage = 1;
  int totalPage = 1;
  List<String> helpStatus = [
    "pending",
    "closed",
  ];

  @override
  void initState() {
    super.initState();
    // init();
  }

  void init() async {
    //
  }

  @override
  void dispose() {
    hideAppActivity();
    super.dispose();
  }

  @override
  void setState(fn) {
    if (mounted) super.setState(fn);
  }

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: helpStatus.length,
      child: Scaffold(
        appBar: const RoooAppbar(
          title: "Help",
          isDarkOverlay: false,
        ),
        floatingActionButton: FloatingActionButton(
                    backgroundColor: AppColors.lightThemeSecondaryColor,

          onPressed: () {
              launchScreen(const AllRidesScreen(isFromCare: true,),);


          },
          child: const Center(
            child: Icon(
              Icons.add,
            ),
          ),
        ),
        body: Column(children: [
          tabContainer(tabs: helpStatus),
          const SizedBox(
            height: 8,
          ),
          Expanded(
            child: TabBarView(
              children: helpStatus.map((e) {
                return CreateRideHelpTabScreen(
                  status: e,
                  indicatorUpdater: widget.indicatorUpdater,
                );
              }).toList(),
            ),
          ),
        ]),
      ),
    );
  }
}
