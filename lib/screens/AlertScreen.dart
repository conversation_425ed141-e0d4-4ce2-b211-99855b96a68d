import 'package:rider/app_exports.dart';
import 'package:url_launcher/url_launcher.dart';

class AlertScreen extends StatefulWidget {
  final int? rideId;
  final int? regionId;

  const AlertScreen({super.key, this.rideId, this.regionId});

  @override
  AlertScreenState createState() => AlertScreenState();
}

class AlertScreenState extends State<AlertScreen> {
  List<ContactModel> sosListData = [];

  @override
  void initState() {
    showAppActivity();
    super.initState();
    init();
  }

  @override
  dispose() {
    hideAppActivity();
    super.dispose();
  }

  void init() async {
    await getSosList(regionId: widget.regionId).then((value) {
      if (value == null) {
        toast(Globals.language.errorMsg);
        return;
      }
      sosListData.addAll(value.data!);
      setState(() {
        hideAppActivity();
      });
    }).onError((error, stackTrace) {
      showErrorToast();
      hideAppActivity();
    });
  }

  Future<void> adminSosNotify() async {
    showAppActivity();
    Map req = {
      "ride_request_id": widget.rideId,
      "latitude": "",
      "longitude": "",
    };
    await adminNotify(request: req).then((value) {
      setState(() {
        Globals.isAdminNotifiedForSOS = true;
      });
      hideAppActivity();
    }).onError((error, stackTrace) {
      showErrorToast();
      hideAppActivity();
    });
  }

  @override
  void setState(fn) {
    if (mounted) super.setState(fn);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Stack(
          alignment: Alignment.center,
          children: [
            Visibility(
              visible: !isAppActivityRunning.value,
              child: SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    children: [
                      const Icon(Icons.warning_amber,
                          color: Colors.red, size: 50),
                      const SizedBox(height: 8),
                      Text(Globals.language.useInCaseOfEmergency,
                          style: boldTextStyle(color: Colors.red)),
                      const SizedBox(height: 16),
                      InkWell(
                        onTap: Globals.isAdminNotifiedForSOS
                            ? null
                            : () {
                                adminSosNotify();
                              },
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(Globals.language.notifyAdmin,
                                    style: boldTextStyle()),
                                if (Globals.isAdminNotifiedForSOS)
                                  const SizedBox(height: 4),
                                if (Globals.isAdminNotifiedForSOS)
                                  Text(Globals.language.notifiedSuccessfully,
                                      style: const TextStyle(
                                        fontSize: 14,
                                        color: Colors.green,
                                      )),
                              ],
                            ),
                            const Icon(Icons.notification_add_outlined),
                          ],
                        ),
                      ),
                      const SizedBox(height: 16),
                      Padding(
                        padding: const EdgeInsets.only(top: 8, bottom: 8),
                        child: InkWell(
                          onTap: () {
                            launchUrl(Uri.parse('tel:911'),
                                mode: LaunchMode.externalApplication);
                          },
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    Globals.language.emergencyContact,
                                    style: boldTextStyle(),
                                  ),
                                  const SizedBox(height: 4),
                                  Text(
                                    '000',
                                    style: primaryTextStyle(),
                                  ),
                                ],
                              ),
                              const Icon(Icons.call),
                            ],
                          ),
                        ),
                      ),
                      sosListData.isEmpty
                          ? const SizedBox()
                          : const Divider(
                              thickness: 4,
                            ),
                      SizedBox(
                        height: sosListData.isEmpty
                            ? 0
                            : sosListData.length == 1
                                ? 60
                                : sosListData.length == 2
                                    ? 110
                                    : sosListData.length == 3
                                        ? 170
                                        : sosListData.length > 3
                                            ? 240
                                            : 150,
                        width: MediaQuery.sizeOf(context).width,
                        child: ListView.builder(
                            // physics: NeverScrollableScrollPhysics(),
                            shrinkWrap: true,
                            itemCount: sosListData.length,
                            itemBuilder: (_, index) {
                              return Padding(
                                padding:
                                    const EdgeInsets.only(top: 8, bottom: 8),
                                child: InkWell(
                                  onTap: () {
                                    launchUrl(
                                        Uri.parse(
                                            'tel:${sosListData[index].contactNumber}'),
                                        mode: LaunchMode.externalApplication);
                                  },
                                  child: Row(
                                    children: [
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                                sosListData[index]
                                                    .title
                                                    .validate(),
                                                maxLines: 1,
                                                overflow: TextOverflow.ellipsis,
                                                style: boldTextStyle()),
                                            const SizedBox(height: 4),
                                            Text(
                                                sosListData[index]
                                                    .contactNumber
                                                    .validate(),
                                                style: primaryTextStyle()),
                                          ],
                                        ),
                                      ),
                                      const SizedBox(width: 8),
                                      const Icon(Icons.call),
                                    ],
                                  ),
                                ),
                              );
                            }),
                      )
                    ],
                  ),
                ),
              ),
            ),
            const ActivityIndicator()
          ],
        ),
      ],
    );
  }
}
