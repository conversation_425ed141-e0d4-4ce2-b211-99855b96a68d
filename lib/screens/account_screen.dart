import 'package:flutter/services.dart';
import 'package:rider/app_counters.dart';
import 'package:rider/app_exports.dart';
import 'package:rider/components/custom_text.dart';
import 'package:rider/features/coupon-list/screens/coupon_list_screen.dart';
import 'package:rider/features/edit_profile/screens/new_edit_profile_screen.dart';
import 'package:rider/screens/account_setting_screen.dart';
import 'package:rider/screens/payment_setting_screen.dart';

import 'package:rider/screens/HelpScreen.dart';
import 'package:rider/screens/inbox_screen.dart';

import 'package:state_beacon/state_beacon.dart';

class AccountScreen extends StatefulWidget {
  const AccountScreen({super.key});

  @override
  State<AccountScreen> createState() => _AccountScreenState();
}

class _AccountScreenState extends State<AccountScreen> {
  // String? _userRating = '';

  @override
  void initState() {
    // _userRating = Globals.sharedPrefs.getString(USER_RATING);

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    // final showInboxCount = inboxCount.watch(context);
    final showNotiCount = notificationCount.watch(context);
    final showCareCount = careCount.watch(context);

    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: AppBar(
        title: Text(
          Globals.language.myProfile,
          style: const TextStyle(color: Colors.white),
        ),
        backgroundColor: Theme.of(context).primaryColor,
        systemOverlayStyle: SystemUiOverlayStyle(
          statusBarColor: Theme.of(context).primaryColor,
          statusBarIconBrightness:
              Theme.of(context).brightness == Brightness.dark
                  ? Brightness.dark
                  : Brightness.light,
          statusBarBrightness: Theme.of(context).brightness == Brightness.dark
              ? Brightness.dark
              : Brightness.light,
        ),
      ),
      body: Container(
        color: Theme.of(context).brightness == Brightness.dark
            ? Colors.black
            : Colors.grey[100],
        child: Column(
          children: [
            ValueListenableBuilder(
              valueListenable: Globals.isUserUpdated,
              builder: (context, value, child) {
                return Container(
                  color: Theme.of(context).primaryColor,
                  child: Padding(
                    padding: const EdgeInsets.all(10),
                    child: Row(
                      children: [
                        InkWell(
                          onTap: () async {
                            if (!Globals.user.isProfileComplete) {
                              _handleIncompleteProfile();
                              return;
                            }

                            bool? result = await Navigator.of(context).push(
                              MaterialPageRoute(
                                builder: (context) =>
                                    const NewEditProfileScreen(
                                  isFromDahboard: false,
                                ),
                              ),
                            );
                            if (result == true) {
                              setState(() {});
                            }
                          },
                          child: Stack(
                            alignment: Alignment.bottomRight,
                            children: [
                              CachedNetworkImage(
                                imageUrl: Globals.user.profileImage ?? '',
                                imageBuilder: (context, imageProvider) =>
                                    Container(
                                  width: 80,
                                  height: 80,
                                  decoration: BoxDecoration(
                                    border: Border.all(color: Colors.white),
                                    shape: BoxShape.circle,
                                    image: DecorationImage(
                                      image: imageProvider,
                                      fit: BoxFit.cover,
                                    ),
                                  ),
                                ),
                                placeholder: (context, url) => Container(
                                  width: 80,
                                  height: 80,
                                  decoration: const BoxDecoration(
                                    shape: BoxShape.circle,
                                  ),
                                  child: const Center(
                                    child: CircularProgressIndicator(
                                      color: Colors.black,
                                    ),
                                  ),
                                ),
                                errorWidget: (context, url, error) => Container(
                                  width: 80,
                                  height: 80,
                                  decoration: const BoxDecoration(
                                    shape: BoxShape.circle,
                                    image: DecorationImage(
                                      image: AssetImage(
                                        Assets.profilePlaceholder,
                                      ),
                                      fit: BoxFit.cover,
                                    ),
                                  ),
                                ),
                              ),
                              Container(
                                  padding: const EdgeInsets.all(5),
                                  decoration: BoxDecoration(
                                      color: Colors.white,
                                      shape: BoxShape.circle,
                                      border: Border.all(color: Colors.black)),
                                  child: const Icon(
                                    Icons.edit,
                                    size: 15,
                                    color: Colors.black,
                                  ))
                            ],
                          ),
                        ),
                        width20,
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              CustomText(
                                data:
                                    '${Globals.user.firstName!} ${Globals.user.lastName!}',
                                size: 24,
                                maxline: 2,
                                textOverflow: TextOverflow.ellipsis,
                                fontweight: FontWeight.bold,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
            Expanded(
              child: Stack(
                children: [
                  SingleChildScrollView(
                    padding: const EdgeInsets.all(10),
                    child: Column(
                      children: [
                        height20,
                        Row(
                          children: [
                            Expanded(
                              child: custom_box(
                                size: 150,
                                icon: Icons.hail_outlined,
                                title: Globals.language.help,
                                onTap: () {
                                  launchScreen(
                                    const HelpScreen(),
                                    pageRouteAnimation:
                                        PageRouteAnimation.SlideBottomTop,
                                  );
                                },
                              ),
                            ),
                            width20,
                            Expanded(
                              child: Stack(children: [
                                custom_box(
                                  size: 150,
                                  icon: Icons.email,
                                  title: Globals.language.inboxText,
                                  onTap: () {
                                    inboxCount.reset();
                                    launchScreen(
                                      const InboxScreen(),
                                      pageRouteAnimation:
                                          PageRouteAnimation.SlideBottomTop,
                                    );
                                  },
                                ),
                                inboxCount.value < 1
                                    ? const SizedBox()
                                    : Positioned(
                                        right: -20,
                                        top: -60,
                                        bottom: 0,
                                        left: 0,
                                        child: Center(
                                          child: Container(
                                            height: 30,
                                            width: 30,
                                            decoration: BoxDecoration(
                                              color: Colors.red,
                                              borderRadius:
                                                  BorderRadius.circular(15),
                                            ),
                                            child: Center(
                                              child: Text(
                                                inboxCount
                                                    .watch(context)
                                                    .toString(),
                                                style: const TextStyle(
                                                  color: Colors.white,
                                                  fontSize: 11,
                                                ),
                                              ),
                                            ),
                                          ),
                                        ),
                                      ),
                              ]),
                            ),
                          ],
                        ),
                        height20,
                        Row(
                          children: [
                            Expanded(
                              child: custom_box(
                                size: 150,
                                icon: Icons.local_offer,
                                title: "Promotions",
                                onTap: () {
                                  if (!Globals.user.isProfileComplete) {
                                    _handleIncompleteProfile();
                                    return;
                                  }
                                  launchScreen(
                                    const CouponListScreen(),
                                    pageRouteAnimation:
                                        PageRouteAnimation.SlideBottomTop,
                                  );
                                },
                              ),
                            ),
                            width20,
                            Expanded(
                              child: custom_box(
                                size: 150,
                                icon: Icons.payment,
                                title: "Payments",
                                onTap: () {
                                  if (!Globals.user.isProfileComplete) {
                                    _handleIncompleteProfile();
                                    return;
                                  }
                                  launchScreen(
                                    const PaymentSettingScreen(),
                                    pageRouteAnimation:
                                        PageRouteAnimation.SlideBottomTop,
                                  );
                                },
                              ),
                            ),
                          ],
                        ),
                        height20,
                        Row(
                          children: [
                            Expanded(
                              child: custom_box(
                                size: 150,
                                icon: Icons.settings,
                                title: Globals.language.settings,
                                onTap: () {
                                  launchScreen(
                                    const MainSettingScreen(),
                                    pageRouteAnimation:
                                        PageRouteAnimation.SlideBottomTop,
                                  );
                                },
                              ),
                            ),
                          ],
                        )
                      ],
                    ),
                  ),
                  const ActivityIndicator(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _logOut() async {
    Navigator.of(context).pop();
    showAppActivity();
    var response = await logoutApi();
    if (!response.status) {
      toast(response.message);
    } else {
      await logOutSuccess();
    }
    hideAppActivity();
  }

  ListTile drawerButton(
      {required String title,
      dynamic screenName,
      required bool checkForProfile,
      void Function()? onTap}) {
    return ListTile(
      onTap: () {
        if (checkForProfile) {
          if (!Globals.user.isProfileComplete) {
            _handleIncompleteProfile();
            return;
          }
        }
        launchScreen(screenName);

        if (onTap != null) {
          onTap();
        }
      },
      title: Text(
        title,
        style: const TextStyle(
          color: Colors.black,
        ),
      ),
      tileColor: Colors.grey.shade200,
    );
  }

  custom_box(
      {required IconData icon,
      required String title,
      required double size,
      required void Function() onTap}) {
    return InkWell(
      onTap: () {
        onTap();
      },
      child: Container(
        height: size,
        padding: const EdgeInsets.symmetric(
          horizontal: 20,
        ),
        decoration: BoxDecoration(
            color: Colors.white, borderRadius: BorderRadius.circular(10)),
        child: SizedBox(
          width: double.infinity,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                color: Colors.black,
                size: 40,
              ),
              CustomText(
                data: title,
                color: Colors.black,
              )
            ],
          ),
        ),
      ),
    );
  }

  void _handleIncompleteProfile() {
    showAppDialog(
      dialogType: AppDialogType.error,
      title: "Your profile is incomplete. Please complete your profile",
      onAccept: () {
        Navigator.of(context).pop();
        launchScreen(
          RegisterScreen(
            countryDialCode: "+61",
            countryISOCode: "AU",
            phone: "",
            isSocialLogin: true,
            oldUser: Globals.user,
          ),
        );
      },
    );
  }
}

class CollapseableWidgets<T> extends StatefulWidget {
  final List<Widget> widgets;
  final List<T> data;
  final void Function(T obj) onWidgetTap;
  const CollapseableWidgets({
    super.key,
    required this.widgets,
    required this.onWidgetTap,
    required this.data,
  });

  @override
  State<CollapseableWidgets<T>> createState() => _CollapseableWidgetsState<T>();
}

class _CollapseableWidgetsState<T> extends State<CollapseableWidgets<T>> {
  bool _showList = false;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return _showList || widget.widgets.length == 1
        ? ListView.separated(
            separatorBuilder: (context, index) => height10,
            itemBuilder: (context, index) {
              return AnimationConfiguration.staggeredList(
                delay: const Duration(milliseconds: 70),
                position: index,
                duration: const Duration(seconds: 1),
                child: SlideAnimation(
                  child: InkWell(
                    onTap: () {
                      widget.onWidgetTap(widget.data[index]);
                    },
                    child: widget.widgets[index],
                  ),
                ),
              );
            },
            itemCount: widget.widgets.length,
          )
        : Stack(
            children: getStackedWidgets(),
          );
  }

  List<Widget> getStackedWidgets() {
    List<Widget> widgets = [];

    for (int i = 0; i < widget.widgets.length; i++) {
      widgets.add(
        Positioned(
          top: i * 40,
          left: 0,
          right: 0,
          child: InkWell(
            onTap: () {
              setState(() {
                _showList = true;
              });
            },
            child: AnimationConfiguration.staggeredList(
              delay: const Duration(milliseconds: 50),
              position: i * 4,
              duration: const Duration(seconds: 1),
              child: SlideAnimation(
                child: widget.widgets[i],
              ),
            ),
          ),
        ),
      );
    }

    return widgets;
  }
}
