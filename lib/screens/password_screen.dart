import 'package:rider/app_exports.dart';

class PasswordScreen extends StatefulWidget {
  final String countryDialCode;
  final String email;

  final String countryISOCode;
  final String phone;
  final bool isSocialLogin;
  final String loginKey;

  const PasswordScreen({
    super.key,
    required this.countryDialCode,
    required this.countryISOCode,
    required this.phone,
    required this.isSocialLogin,
    required this.loginKey,
    required this.email,
  });
  @override
  PasswordScreenState createState() => PasswordScreenState();
}

class PasswordScreenState extends State<PasswordScreen> {
  final TextEditingController _passwordController = TextEditingController();
  bool _showPassword = false;

  @override
  void initState() {
    hideAppActivity();

    super.initState();
  }

  @override
  void dispose() {
    hideAppActivity();

    super.dispose();
  }

  Future<void> _forgotPassword() async {
    hideKeyboard();
    showAppActivity();
    var value = await forgotPassword(
      widget.email,
    );

    if (value == null) {
      toast(Globals.language.serverErrorMsg);
    } else if (!value['status']) {
      toast(value["message"]);
      hideAppActivity();

      return;
    } else {
      showAppDialog(
          onAccept: () {
            Navigator.of(context).pop();
          },
          dialogType: AppDialogType.info,
          title: value["message"]);

      // toast(value["message"]);
    }
    hideAppActivity();
  }

  Future<void> _verifyPassword() async {
    if (_passwordController.text.length < 8) {
      toast("Password must be at least 8 characters long");
    } else if (widget.loginKey.isEmpty) {
      toast(Globals.language.serverErrorMsg);
      return;
    } else {
      showAppActivity();
      var value = await verifyPassword(
        widget.loginKey,
        _passwordController.text,
      );

      if (value == null) {
        toast(Globals.language.serverErrorMsg);
        hideAppActivity();
      } else if (!value['status']) {
        // _pinputController.text = "";
        _passwordController.clear();
        toast(value['message']);
        hideAppActivity();
        return;
      } else {
        try {
          var user = UserModel.fromJson(value['data']['user']);

          bool result = await Globals.sharedPrefs.setString(
            SPKeys.user,
            jsonEncode(user.toJson()),
          );
          if (!result) {
            toast(Globals.language.errorMsg);
          } else {
            Globals.user = user;
            Globals.isUserLoggedIn = true;
            if (user?.profileImage != null) {
              Globals.sharedPrefs.setString("USER_PROFILE", user.profileImage!);
            }
            launchScreen(
              const DashboardWrapperScreen(),
              isNewTask: true,
              pageRouteAnimation: PageRouteAnimation.Slide,
            );
          }
        } catch (e) {
          toast(Globals.language.errorMsg);
        }
      }
      hideAppActivity();
    }
  }

  @override
  void setState(fn) {
    if (mounted) super.setState(fn);
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        hideKeyboard();
      },
      child: Scaffold(
        appBar: const RoooAppbar(
          title: 'Enter Password',
          isDarkOverlay: false,
        ),
        bottomNavigationBar: BottomButton(
          text: Globals.language.verifyOTP,
          onPressed: _verifyPassword,
        ),
        body: Stack(
          children: [
            // Column(
            //   crossAxisAlignment: CrossAxisAlignment.start,
            //   children: [
            // Flexible(
            //   flex: 0,
            //   child: Stack(
            //     children: [
            //       // ClipPath(
            //       //   clipper: MyCustomClipper(),
            //       //   child: Container(
            //       //     color: Colors.black,
            //       //   ),
            //       // ),
            //       SizedBox(
            //         height: Platform.isIOS
            //             ? MediaQuery.sizeOf(context).height / 2.5 + 10
            //             : MediaQuery.sizeOf(context).height / 2 + 10,
            //         // ,
            //         child: Center(
            //             child: Image.asset(
            //           Assets.appLogo,
            //           width: MediaQuery.sizeOf(context).width - 100,
            //         )),
            //       ),
            //     ],
            //   ),
            // ),
            Padding(
              padding: const EdgeInsets.all(
                Layout.scaffoldBodyPadding,
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Text(
                  //   '${Globals.language.welcomeBack}, ',
                  //   style: const TextStyle(
                  //     fontWeight: FontWeight.bold,
                  //     fontSize: 20,
                  //   ),
                  // ),
                  const SizedBox(
                    height: 10,
                  ),
                  Text(
                    "Password",
                    style: AppTextStyles.title,
                  ),
                  height10,
                  TextFormField(
                    obscureText: !_showPassword,
                    controller: _passwordController,
                    decoration: InputDecoration(
                        suffix: IconButton(
                            onPressed: () {
                              setState(() {
                                if (_showPassword) {
                                  _showPassword = false;
                                } else {
                                  _showPassword = true;
                                }
                              });
                            },
                            icon: Icon(_showPassword
                                ? Icons.remove_red_eye_outlined
                                : Icons.remove_red_eye))),
                  ),
                  const SizedBox(
                    height: 15,
                  ),

                  InkWell(
                      onTap: () {
                        _forgotPassword();
                      },
                      child: Text(
                        "Forgot your password?",
                        style: TextStyle(color: Theme.of(context).primaryColor),
                      )),
                  // Center(
                  //   child: InkWell(
                  //     onTap: _timerDuration == 0 ? _sendOTP : null,
                  //     child: Container(
                  //       padding: const EdgeInsets.all(
                  //         10 / 1.5,
                  //       ),
                  //       decoration: BoxDecoration(
                  //         color: Colors.grey[_timerDuration == 0 ? 200 : 100],
                  //         borderRadius: BorderRadius.circular(
                  //           20,
                  //         ),
                  //       ),
                  //       child: _timerDuration == 0
                  //           ? Text(
                  //               Globals.language.iHaveNotReceivedACode,
                  //               style: const TextStyle(
                  //                 fontSize: 12,
                  //                 color: Colors.black,
                  //               ),
                  //             )
                  //           : Text(
                  //               '${Globals.language.iHaveNotReceivedACode} ($_timerDuration)',
                  //               style: const TextStyle(
                  //                 fontSize: 12,
                  //                 color: Colors.black,
                  //               ),
                  //             ),
                  //     ),
                  //   ),
                  // )
                ],
              ),
            ),
            // ],
            // ),
            const ActivityIndicator(),
          ],
        ),
      ),
    );
  }
}
