import 'package:rider/app_exports.dart';

class NotificationDeniedScreen extends StatefulWidget {
  const NotificationDeniedScreen({super.key});

  @override
  NotificationDeniedScreenState createState() =>
      NotificationDeniedScreenState();
}

class NotificationDeniedScreenState extends State<NotificationDeniedScreen>
    with WidgetsBindingObserver {
  @override
  void setState(fn) {
    if (mounted) super.setState(fn);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        automaticallyImplyLeading: false,
        backgroundColor: Colors.black,
      ),
      body: Padding(
        padding: const EdgeInsets.all(
          Layout.scaffoldBodyPadding,
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Image.asset(
              'images/location_permission.gif',
              width: 130,
            ),
            const SizedBox(
              height: 20,
            ),
            RichText(
              textAlign: TextAlign.center,
              text: const TextSpan(
                text:
                    "Notification permission is required for the app's functionality.\nWithout this calling feature will not work.\nPlease allow notification permission. \"",
                style: TextStyle(
                  color: Colors.black,
                  fontSize: 18,
                  fontWeight: FontWeight.w400,
                ),
                // children: [
                //   TextSpan(
                //     text: "Always",
                //     style: TextStyle(fontWeight: FontWeight.w700),
                //   ),
                //   TextSpan(
                //     text: "\" or \"",
                //     style: TextStyle(fontWeight: FontWeight.w400),
                //   ),
                //   TextSpan(
                //     text: "While Using the App",
                //     style: TextStyle(fontWeight: FontWeight.w700),
                //   ),
                //   TextSpan(
                //     text: "\" based on your device OS.",
                //   ),
                // ]
              ),
            ),
            ElevatedButton(
              onPressed: () {
                openAppSettings();
              },
              child: const Text("Open settings"),
            ),
          ],
        ),
      ),
    );
  }
}
