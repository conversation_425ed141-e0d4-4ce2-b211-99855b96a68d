import 'package:flutter/services.dart';
import 'package:rider/app_exports.dart';
import '../screens/ComplaintListScreen.dart';

class NotificationScreen extends StatefulWidget {
  const NotificationScreen({super.key});

  @override
  NotificationScreenState createState() => NotificationScreenState();
}

class NotificationScreenState extends State<NotificationScreen>
    with TickerProviderStateMixin {
  ScrollController scrollController = ScrollController();
  int currentPage = 1;

  bool mIsLastPage = false;
  List<NotificationData> notificationData = [];
  String emptyDataMsg = '';

  @override
  void initState() {
    super.initState();
    init();

    scrollController.addListener(() {
      if (scrollController.position.pixels ==
          scrollController.position.maxScrollExtent) {
        if (!mIsLastPage) {
          currentPage++;
          setState(() {});

          init();
        }
      }
    });
    afterBuildCreated(() {
      // final brightness = MediaQuery.of(context).platformBrightness;
      // Globals.isDarkModeOn = brightness == Brightness.dark;
      // setState(() {

      // });
    });
  }

  Future<void> init() async {
    showAppActivity();
    getNotification(page: currentPage).then((value) {
      if (value == null) {
        toast(Globals.language.errorMsg);
        return;
      }
      //
      mIsLastPage = value.notificationData!.length < currentPage;
      if (currentPage == 1) {
        notificationData.clear();
      }
      notificationData.addAll(value.notificationData!);
      emptyDataMsg = value.message ?? '';
      setState(() {
        hideAppActivity();
      });
    }).onError((error, stackTrace) {
      showErrorToast();
    });
  }

  @override
  void setState(fn) {
    if (mounted) super.setState(fn);
  }

  @override
  void dispose() {
    HelperMethods.getAndApplyCounters();
    hideAppActivity();
    super.dispose();
    if (Platform.isIOS) {
      SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle.dark);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: RoooAppbar(
        title: Globals.language.notification,
        isDarkOverlay: false,
      ),
      body: Stack(
        children: [
          RefreshIndicator(
            onRefresh: () async {
              return init();
            },
            child: ListView.separated(
              physics: const AlwaysScrollableScrollPhysics(),
              controller: scrollController,
              padding: EdgeInsets.zero,
              itemCount: notificationData.length,
              itemBuilder: (_, index) {
                NotificationData data = notificationData[index];
                return Column(
                  children: [
                    inkWellWidget(
                      onTap: () {
                        if (data.data!.type == "COMPLAIN_COMMENT") {
                          launchScreen(ComplaintListScreen(
                              complaint: data.data!.complaintId!));
                        } else if (data.data!.subject!.toLowerCase() ==
                            RideStatus.completed) {
                          launchScreen(RideDetailScreen(
                            orderId: data.data!.id!,
                            isFromNotifications: true,
                          ));
                        }
                      },
                      child: Container(
                        padding: const EdgeInsets.all(12),
                        child: Row(
                          children: [
                            Container(
                              height: 50,
                              width: 50,
                              alignment: Alignment.center,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                color: Colors.grey.shade200,
                              ),
                              child: Center(
                                child: Image.asset(
                                  Assets.appLogoWhite,
                                  // height: 100,
                                  width: 50,
                                  fit: BoxFit.contain,
                                ),
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Expanded(
                                          child: Text('${data.data!.subject}',
                                              style: boldTextStyle())),
                                      const SizedBox(width: 8),
                                      Text(data.createdAt.validate(),
                                          style: secondaryTextStyle()),
                                    ],
                                  ),
                                  const SizedBox(height: 6),
                                  Text('${data.data!.message}',
                                      style: primaryTextStyle(size: 14)),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    // if (index == notificationData.length - 1 && mIsLastPage)
                    //   Padding(
                    //     padding: const EdgeInsets.only(bottom: 10.0),
                    //     child: Text(Globals.language.noMoreData),
                    //   )
                  ],
                );
              },
              separatorBuilder: (context, index) {
                return const Divider(
                  height: 8,
                );
              },
            ),
          ),
          Visibility(
              visible: isAppActivityRunning.value, child: loaderWidget()),
          if (notificationData.isEmpty && !isAppActivityRunning.value)
            emptyWidget(emptyDataMsg: emptyDataMsg),
        ],
      ),
    );
  }
}
