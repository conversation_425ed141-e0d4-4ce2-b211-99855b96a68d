import 'package:rider/app_exports.dart';
import 'package:rider/components/custom_text.dart';
import 'package:rider/features/edit_profile/screens/new_edit_profile_screen.dart';

import 'package:rider/model/app_setting_model.dart';

import 'package:rider/screens/HelpScreen.dart';

import 'package:flutter/services.dart';

import 'AboutScreen.dart';
import 'delete_account_screen.dart';

class AccountSettingScreen extends StatefulWidget {
  const AccountSettingScreen({super.key});

  @override
  AccountSettingScreenState createState() => AccountSettingScreenState();
}

class AccountSettingScreenState extends State<AccountSettingScreen> {
  AppSettingModel settingModel = AppSettingModel();
  String? privacyPolicy;
  String? termsCondition;
  String? mHelpAndSupport;

  @override
  void initState() {
    showAppActivity();
    super.initState();
    init();
  }

  void init() async {
    await getAppSetting().then((value) {
      if (value == null) {
        toast(Globals.language.errorMsg);
        return;
      }
      if (value.settingModel!.helpSupportUrl != null) {
        mHelpAndSupport = value.settingModel!.helpSupportUrl!;
      }
      settingModel = value;
      Globals.companyEmail = value.settingModel?.contactEmail ?? '';

      if (value.privacyPolicyModel!.value != null) {
        privacyPolicy = value.privacyPolicyModel!.value!;
      }
      if (value.termsCondition!.value != null) {
        termsCondition = value.termsCondition!.value!;
      }
      hideAppActivity();
    }).catchError((error) {
      log(error.toString());
    });
  }

  @override
  void setState(fn) {
    if (mounted) super.setState(fn);
  }

  @override
  void dispose() {
    hideAppActivity();
    super.dispose();
    if (Platform.isIOS) {
      SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle.dark);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const RoooAppbar(
        title: "Account Settings",
        isDarkOverlay: false,
      ),
      body: Stack(
        children: [
          SingleChildScrollView(
            padding: const EdgeInsets.only(bottom: 16),
            child: Padding(
              padding: const EdgeInsets.all(10),
              child: Column(
                children: [
                  InkWell(
                    onTap: (){
                      launchScreen(const NewEditProfileScreen(isFromDahboard: false,),
                          pageRouteAnimation: PageRouteAnimation.Slide);
                    },
                    child: Row(
                      children: [
                        CachedNetworkImage(
                          imageUrl: Globals.user.profileImage ?? '',
                          imageBuilder: (context, imageProvider) => Container(
                            width: 60,
                            height: 60,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              image: DecorationImage(
                                image: imageProvider,
                                fit: BoxFit.cover,
                              ),
                            ),
                          ),
                          placeholder: (context, url) => Container(
                            width: 60,
                            height: 60,
                            decoration: const BoxDecoration(
                              shape: BoxShape.circle,
                            ),
                            child: const Center(
                              child: CircularProgressIndicator(),
                            ),
                          ),
                          errorWidget: (context, url, error) => Container(
                            width: 60,
                            height: 60,
                            decoration: const BoxDecoration(
                              shape: BoxShape.circle,
                              image: DecorationImage(
                                image: AssetImage(
                                  Assets.profilePlaceholder,
                                ),
                                fit: BoxFit.cover,
                              ),
                            ),
                          ),
                        ),
                        width20,
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              CustomText(
                                data:
                                    '${Globals.user.firstName ?? ''} ${Globals.user.lastName ?? ''}',
                                maxline: 2,
                                textOverflow: TextOverflow.ellipsis,
                                fontweight: FontWeight.bold,
                                // color: Colors.black,
                              ),
                              CustomText(
                                data: Globals.user.contactNumber ?? '',
                                // color: Colors.black,
                                fontweight: FontWeight.bold,
                              ),
                              CustomText(
                                data: Globals.user.email ?? '',
                                fontweight: FontWeight.bold,
                                // color: Colors.black,
                              ),
                            ],
                          ),
                        )
                      ],
                    ),
                  ),
                  const Divider(),
                  // settingItemWidget(Icons.language, Globals.language.language,
                  //     () {
                  //   launchScreen(LanguageScreen(),
                  //       pageRouteAnimation: PageRouteAnimation.Slide);
                  // }),
                  settingItemWidget(
                      Icons.assignment_outlined, Globals.language.privacyPolicy,
                      () {
                    launchScreen(const PrivacyPolicyScreen(),
                        pageRouteAnimation: PageRouteAnimation.Slide);
                  }),
                  settingItemWidget(
                      Icons.help_outline, Globals.language.helpSupport, () {
                    launchScreen(const HelpScreen(),
                        pageRouteAnimation: PageRouteAnimation.Slide);
                  }),
                  settingItemWidget(Icons.assignment_outlined,
                      Globals.language.termsConditions, () {
                    launchScreen(const TermsAndConditionsScreen(),
                        pageRouteAnimation: PageRouteAnimation.Slide);
                  }),
                  settingItemWidget(
                    Icons.info_outline,
                    Globals.language.about,
                    () {
                      launchScreen(AboutScreen(settingModel: settingModel),
                          pageRouteAnimation: PageRouteAnimation.Slide);
                    },
                  ),
                  settingItemWidget(
                      Icons.delete_outline, Globals.language.deleteAccount, () {
                    launchScreen(const DeleteAccountScreen(),
                        pageRouteAnimation: PageRouteAnimation.Slide);
                  }, isLast: true),
                ],
              ),
            ),
          ),
          const ActivityIndicator(),
        ],
      ),
    );
  }

  Widget settingItemWidget(IconData icon, String title, Function() onTap,
      {bool isLast = false, IconData? suffixIcon}) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        ListTile(
          contentPadding: const EdgeInsets.only(left: 16, right: 16),
          leading: Icon(
            icon,
            size: 24,
            // color: Colors.black,
          ),
          title: Text(title,
              style: primaryTextStyle(
                // color: Colors.black,
              )),
          trailing: suffixIcon != null
              ? Icon(suffixIcon, color: Colors.green)
              : const Icon(Icons.navigate_next, ),
          onTap: onTap,
        ),
        // if (!isLast) Divider(height: 0)
      ],
    );
  }
}
