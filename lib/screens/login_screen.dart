import 'package:aad_oauth/aad_oauth.dart';
import 'package:aad_oauth/model/config.dart';
import 'package:aad_oauth/model/failure.dart';
import 'package:flutter/services.dart';
import 'package:http/http.dart' as http;

import 'package:aad_oauth/model/token.dart';
import 'package:flutter_facebook_auth/flutter_facebook_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:rider/app_exports.dart';
import 'package:rider/main.dart';
import 'package:rider/model/microsoft_auth_model.dart';
import 'package:rider/screens/password_screen.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  LoginScreenState createState() => LoginScreenState();
}

class LoginScreenState extends State<LoginScreen> {
  final String _countryISOCode = "AU";
  final String _countryDialCode = "+61";

  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  final TextEditingController _phoneController = TextEditingController();

  @override
  void initState() {
    Globals.isLoggedOut = false;
    Globals.sharedPrefs.setBool(SPKeys.isFirstTime, false);
    super.initState();
  }

  @override
  void setState(fn) {
    if (mounted) super.setState(fn);
  }

  Future<void> _checkUser({
    bool isSocialLogin = false,
    String? phone,
    String? name,
    String? email,
    String? appleId,
    String? googleId,
    String? facebookId,
    String? microsoftId,
  }) async {
    hideKeyboard();

    if (!isSocialLogin && _phoneController.text.isEmpty) {
      return toast('Please enter a valid mobile number');
    }

    if (isSocialLogin) {}

    if ((!isSocialLogin && _formKey.currentState!.validate()) ||
        isSocialLogin) {
      showAppActivity();

      String firstName = "";
      String lastName = "";

      if (name != null) {
        List<String> nameList = name.split(" ");
        if (nameList.isNotEmpty) {
          firstName = nameList[0];
          if (nameList.length > 1) {
            lastName = nameList[1];
          }
        }
      }

      if (isSocialLogin) {
        _handleSocialLogin(
            firstName: firstName,
            lastName: lastName,
            email: email,
            appleId: appleId,
            googleId: googleId,
            facebookId: facebookId,
            microsoftId: microsoftId);
      } else {
        _handleMobileLogin(phone: _phoneController.text);
      }
    }
  }

  Future<void> _handleSocialLogin({
    required String firstName,
    required String lastName,
    required String? email,
    required String? appleId,
    required String? googleId,
    required String? facebookId,
    required String? microsoftId,
  }) async {
    showAppActivity();
    var response = await isUserExists(
        phoneNumber: null,
        firstName: firstName,
        lastName: lastName,
        userType: UserType.rider,
        email: email,
        appleId: appleId,
        googleId: googleId,
        facebookId: facebookId,
        microsoftId: microsoftId);

    hideAppActivity();

    if (response.status == false || response.data == null) {
      toast(response.message);
    } else {
      bool result = await Globals.sharedPrefs.setString(
        SPKeys.user,
        jsonEncode(response.data!.toJson()),
      );

      if (!result) {
        toast(Globals.language.errorMsg);
        hideAppActivity();
      } else {
        Globals.user = response.data!;
        if (response.data?.profileImage != null) {
          Globals.sharedPrefs
              .setString("USER_PROFILE", response.data!.profileImage!);
        }

        Globals.isUserLoggedIn = true;

        launchScreen(
          DashboardWrapperScreen(
            isRecentSignUp: Globals.user.isNewSignUp ?? false,
          ),
          isNewTask: true,
        );
      }
    }
  }

  Future<void> _handleMobileLogin({
    required String phone,
  }) async {
    showAppActivity();
    var response = await mobileLogin(
      userType: UserType.rider,
      phoneNumber: _countryDialCode + phone,
    );
    hideAppActivity();

    if (response.status == false || response.data == null) {
      toast(response.message);
    } else {
      if (response.data?.is_user_exists == true) {
        if (response.data?.login_screen == "password") {
          launchScreen(
            PasswordScreen(
              email: response.data?.user?.email ?? "",
              countryDialCode: _countryDialCode,
              countryISOCode: _countryISOCode,
              phone: phone,
              loginKey: response.data!.key,
              isSocialLogin: false,
            ),
            pageRouteAnimation: PageRouteAnimation.Slide,
          );
        } else if (response.data?.login_screen == "otp") {
          launchScreen(
            VerifyOTPScreen(
              countryDialCode: _countryDialCode,
              countryISOCode: _countryISOCode,
              phone: phone,
              otpVerificationKey: response.data!.key,
              isSocialLogin: false,
            ),
            pageRouteAnimation: PageRouteAnimation.Slide,
          );
        }
      } else {
        launchScreen(
          VerifyOTPScreen(
            countryDialCode: _countryDialCode,
            countryISOCode: _countryISOCode,
            phone: phone,
            otpVerificationKey: response.data!.key,
            isSocialLogin: false,
          ),
          pageRouteAnimation: PageRouteAnimation.Slide,
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        body: GestureDetector(
          onTap: () {
            hideKeyboard();
          },
          child: Stack(
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Flexible(
                    // flex: 0,
                    child: Center(
                      child: Image.asset(
                        Theme.of(context).brightness == Brightness.dark
                            ? Assets.appLogoWhite
                            : Assets.appLogoBlack,
                        width: MediaQuery.sizeOf(context).width - 120,
                      ),
                    ),
                  ),
                  Expanded(
                    flex: 2,
                    child: Padding(
                      padding: const EdgeInsets.all(
                        Layout.scaffoldBodyPadding,
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            Globals.language.enterYourMobileNumber,
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              // color: getPrimaryTextColor(),
                            ),
                          ),
                          height10,
                          Form(
                            key: _formKey,
                            child: TextFormField(
                              controller: _phoneController,
                              autofocus: true,
                              maxLength: 10,
                              validator: (value) {
                                if ((value?.length ?? 0) < 9) {
                                  return "Please enter a valid mobile number";
                                }
                                return null;
                              },
                              onChanged: (value) {},
                              keyboardType: TextInputType.phone,
                              decoration: InputDecoration(
                                counterText: "",
                                prefix: Padding(
                                  padding: const EdgeInsets.only(right: 8),
                                  child: Text(
                                    "+61",
                                    style: AppTextStyles.title,
                                  ),
                                ),
                                border: const OutlineInputBorder(),
                              ),
                              inputFormatters: [
                                FilteringTextInputFormatter.digitsOnly,
                              ],
                            ),
                          ),
                          height10,
                          AppButtonWidget(
                            width: double.infinity,
                            text: "Login/Sign Up",
                            onTap: () {
                              hideKeyboard();
                              _checkUser(
                                isSocialLogin: false,
                                email: null,
                                appleId: null,
                                googleId: null,
                                facebookId: null,
                              );
                            },
                          ),
                          const SizedBox(
                            height: 5,
                          ),
                          const Center(
                            child: Text(
                              "OR",
                              style: TextStyle(
                                fontSize: 10,
                              ),
                            ),
                          ),
                          const SizedBox(
                            height: 5,
                          ),
                          inkWellWidget(
                            onTap: () {
                              hideKeyboard();
                              _handleGoogleSignIn();
                            },
                            child: Container(
                              padding: const EdgeInsets.all(7),
                              width: MediaQuery.sizeOf(context).width,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(
                                  10,
                                ),
                                border: Border.all(
                                  color: Theme.of(context).brightness ==
                                          Brightness.dark
                                      ? Colors.white
                                      : Colors.black,
                                ),
                              ),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Image.asset(Assets.googleIcon,
                                      fit: BoxFit.cover, height: 25, width: 25),
                                  width4,
                                  const Text(
                                    "Sign in with Google",
                                  ),
                                ],
                              ),
                            ),
                          ),
                          height10,
                          inkWellWidget(
                            onTap: () {
                              hideKeyboard();
                              _handleMicrosoftSignIn();
                            },
                            child: Container(
                              padding: const EdgeInsets.all(7),
                              width: MediaQuery.sizeOf(context).width,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(
                                  10,
                                ),
                                border: Border.all(
                                  color: Theme.of(context).brightness ==
                                          Brightness.dark
                                      ? Colors.white
                                      : Colors.black,
                                ),
                              ),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Image.asset(Assets.microsoftIcon,
                                      fit: BoxFit.cover, height: 25, width: 25),
                                  width4,
                                  const Text(
                                    "Sign in with Microsoft",
                                  ),
                                ],
                              ),
                            ),
                          ),
                          if (Platform.isIOS) ...[
                            height10,
                            _appleSignInWidget(),
                          ],
                        ],
                      ),
                    ),
                  ),
                ],
              ),
              const ActivityIndicator(),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _saveDataForApple(
      String appleId, String appleEmail, String appleName,
      {bool hasLocalData = false}) async {
    if (!hasLocalData) {
      //save local
      await Future.wait([
        Globals.sharedPrefs.setString(SPKeys.appleId, appleId),
        Globals.sharedPrefs.setString(SPKeys.appleName, appleName),
        Globals.sharedPrefs.setString(SPKeys.appleEmail, appleEmail),
      ]);
    }

    String firstName = "";
    String lastName = "";

    if (appleName.isNotEmpty) {
      List<String> nameList = appleName.split(' ');

      if (nameList.isNotEmpty) {
        firstName = nameList[0];
        if (nameList.length > 1) {
          lastName = nameList[1];
        }
      }
    }

    // save to remote
    var response = await saveAppleSignupData(
      appleId: appleId,
      appleEmail: appleEmail,
      firstName: firstName,
      lastName: lastName,
    );
    if (response.status == true) {
      Globals.sharedPrefs.remove(SPKeys.appleId);
      Globals.sharedPrefs.remove(SPKeys.appleName);
      Globals.sharedPrefs.remove(SPKeys.appleEmail);
    }
  }

  Future<bool> _isAlreadySignedUpWithApple(String code) async {
    showAppActivity();
    var result = await checkAppleSignup(code);
    hideAppActivity();
    if (result == null) {
      toast(Globals.language.errorMsg);
    } else if (result['status'] == false) {
      toast(result['message']);
    } else {
      _checkUser(
        isSocialLogin: true,
        name: result['data']['first_name'] + ' ' + result['data']['last_name'],
        phone: null,
        email: result['data']['email'],
        appleId: result['data']['apple_id'],
        googleId: null,
        facebookId: null,
      );

      return true;
    }

    return false;
  }

  Future<void> _handleGoogleSignIn() async {
    showAppActivity();
    try {
      final GoogleSignInAccount? googleUser = await GoogleSignIn().signIn();
      if (googleUser == null) {
        hideAppActivity();
        return;
      }
      _checkUser(
        isSocialLogin: true,
        name: googleUser.displayName,
        phone: null,
        email: googleUser.email,
        googleId: googleUser.id,
        facebookId: null,
        appleId: null,
      );
    } catch (e) {
      toast(Globals.language.errorMsg);
      hideAppActivity();
    }
  }

  Widget _appleSignInWidget() {
    return inkWellWidget(
      onTap: () {
        _handleAppleSignIn();
      },
      child: Container(
        padding: const EdgeInsets.all(7),
        width: MediaQuery.sizeOf(context).width,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(
            10,
          ),
          border: Border.all(
            color: Theme.of(context).brightness == Brightness.dark
                ? Colors.white
                : Colors.black,
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset(
                Theme.of(context).brightness == Brightness.dark
                    ? Assets.appleIconWhite
                    : Assets.appleIconBlack,
              fit: BoxFit.cover,
              height: 25,
              width: 25,
            ),
            width4,
            const Text(
              'Sign in with Apple',
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _handleAppleSignIn() async {
    showAppActivity();
    try {
      final appleUser = await SignInWithApple.getAppleIDCredential(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
      );

      if (appleUser.userIdentifier != null) {
        //save data for future use
        _saveDataForApple(
          appleUser.userIdentifier!,
          appleUser.email ?? '',
          appleUser.givenName ?? "",
        );

        _checkUser(
          isSocialLogin: true,
          name: appleUser.givenName ?? "",
          phone: null,
          email: appleUser.email ?? "",
          appleId: appleUser.userIdentifier,
          googleId: null,
          facebookId: null,
        );
      } else {
        if (await _isAlreadySignedUpWithApple(appleUser.userIdentifier!)) {
          return;
        } else {
          toast(Globals.language.errorMsg);
        }
      }
    } catch (e) {
      hideAppActivity();
      if (e.runtimeType == SignInWithAppleAuthorizationException) {
        if ((e as SignInWithAppleAuthorizationException).code !=
            AuthorizationErrorCode.canceled) {
          toast(Globals.language.errorMsg);
        }
      } else {
        toast(Globals.language.errorMsg);
      }
    }
  }

  Future<void> _handleFacebookSignIn() async {
    showAppActivity();
    try {
      final LoginResult result = await FacebookAuth.instance.login();

      if (result.status == LoginStatus.success) {
        final userData = await FacebookAuth.instance.getUserData();

        _checkUser(
          isSocialLogin: true,
          name: userData['name'],
          phone: null,
          email: userData['email'],
          facebookId: userData['id'],
          googleId: null,
          appleId: null,
        );
      } else if (result.status == LoginStatus.failed) {
        toast(Globals.language.errorMsg);
      }
      hideAppActivity();
    } catch (e) {
      toast(Globals.language.errorMsg);
      hideAppActivity();
    }
  }

  Future<void> _handleMicrosoftSignIn() async {
    MicrosoftAuthModel? user;
    showAppActivity();

    final microsoftSignIn = AadOAuth(
      Config(
        tenant: "common",
        clientId: "7ce01d0b-4a4a-4366-91fa-fe098b07924e",
        responseType: "code",
        scope: "User.Read",
        redirectUri: "msal7ce01d0b-4a4a-4366-91fa-fe098b07924e://auth",
        loader: const Center(child: CircularProgressIndicator()),
        navigatorKey: navigatorKey,
      ),
    );
    try {
      var result = await microsoftSignIn.login();

      await result.fold(
        (Failure failure) {
          if (failure.errorType !=
              ErrorType.accessDeniedOrAuthenticationCanceled) {
            toast(Globals.language.errorMsg);
            return;
          }
          return;
        },
        (Token token) async {
          if (token.accessToken == null) {
            toast(Globals.language.errorMsg);

            return;
          } else {
            final response = await http.get(
              Uri.parse('https://graph.microsoft.com/v1.0/me'),
              headers: {
                'Authorization': 'Bearer ${token.accessToken}',
              },
            );
            if (response.statusCode == 200) {
              user = MicrosoftAuthModel.fromJson(jsonDecode(response.body));
              _checkUser(
                isSocialLogin: true,
                name: user?.displayName ?? "",
                phone: null,
                email: user?.mail ?? "",
                facebookId: null,
                googleId: null,
                microsoftId: user!.id,
                appleId: null,
              );
            } else {
              toast(Globals.language.errorMsg);
            }
          }
        },
      );

      // final msalAuth = await MsalAuth.createPublicClientApplication(
      //   clientId: Constants.msClientId,
      //   scopes: <String>[
      //     'https://graph.microsoft.com/user.read',
      //   ],
      //   androidConfig: AndroidConfig(
      //     configFilePath: 'assets/ms-login/ms.json',
      //     tenantId: Constants.msDirectoryId,
      //   ),
      //   iosConfig: IosConfig(
      //     authority:
      //         'https://login.microsoftonline.com/<MICROSOFT_TENANT_ID>/oauth2/v2.0/authorize',
      //     // Change auth middleware if you need.
      //     authMiddleware: AuthMiddleware.msAuthenticator,
      //     tenantType: TenantType.entraIDAndMicrosoftAccount,
      //   ),
      // );

      hideAppActivity();
    } catch (e) {
      toast(Globals.language.errorMsg);
      hideAppActivity();
    }
  }
}

class MyCustomClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    final path = Path();
    path.moveTo(size.width * .2, 0);
    path.lineTo(size.width, 0);

    path.lineTo(size.width, size.height * .6);

    // Define control point and destination point for curve
    var endPoint = Offset(size.width * .7, size.height);
    var controlPoint = Offset(size.width * .5, size.height * .8);

    // Create quadratic Bezier curve
    path.quadraticBezierTo(
        controlPoint.dx, controlPoint.dy, endPoint.dx, endPoint.dy);

    path.lineTo(0, size.height); // Line to bottom right corner

    path.lineTo(0, size.height * .5);
    // Define control point and destination point for curve
    endPoint = Offset(size.width * .2, 0);
    controlPoint = Offset(size.width * .4, size.height * .2);

    // Create quadratic Bezier curve
    path.quadraticBezierTo(
        controlPoint.dx, controlPoint.dy, endPoint.dx, endPoint.dy);

    path.close(); // Close the path

    return path;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) => false;
}
