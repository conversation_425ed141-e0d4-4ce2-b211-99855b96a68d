import 'package:rider/app_exports.dart';
import '../model/OrderHistory.dart';
import 'package:timeline_tile/timeline_tile.dart';

class RideHistoryScreen extends StatefulWidget {
  final List<RideHistory> rideHistory;

  const RideHistoryScreen({super.key, required this.rideHistory});

  @override
  RideHistoryScreenState createState() => RideHistoryScreenState();
}

class RideHistoryScreenState extends State<RideHistoryScreen> {
  @override
  void initState() {
    super.initState();
    init();
  }

  void init() async {
    //
  }

  @override
  void setState(fn) {
    if (mounted) super.setState(fn);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: RoooAppbar(
        title: Globals.language.rideHistory,
        isDarkOverlay: false,
      ),
      body: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: widget.rideHistory.length,
        itemBuilder: (context, index) {
          RideHistory mData = widget.rideHistory[index];
          return TimelineTile(
            alignment: TimelineAlign.start,
            isFirst: index == 0 ? true : false,
            isLast: index == (widget.rideHistory.length - 1) ? true : false,
            indicatorStyle: IndicatorStyle(
              indicatorXY: 0.1,
              drawGap: true,
              width: 50,
              padding: const EdgeInsets.symmetric(vertical: 2),
              height: 50,
              indicator: Container(
                padding: const EdgeInsets.all(8),
                decoration: const BoxDecoration(
                  color: Colors.grey,
                  shape: BoxShape.circle),
                child: Image.asset(
                  Assets.appLogoWhite,
                  fit: BoxFit.contain,
                ),
              ),
            ),
            afterLineStyle: const LineStyle(thickness: 1),
            endChild: Padding(
              padding: const EdgeInsets.symmetric(vertical: 0, horizontal: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Text(
                      mData.historyType!.replaceAll("_", " ").capitalizeFirstLetter(),
                      style: boldTextStyle()),
                  const SizedBox(height: 4),
                  Text(mData.historyMessage.validate(),
                      style: secondaryTextStyle(
                          // color: textPrimaryColorGlobal
                          )),
                  const SizedBox(height: 6),
                  Text(printDate('${mData.createdAt}'),
                      style: secondaryTextStyle()),
                  const SizedBox(height: 16),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

/*  messageData(RideHistory orderData) {
    if (getStringAsync(USER_TYPE) == CLIENT) {
      if (orderData.historyType == COURIER_ASSIGNED) {
        return 'Your Order#${orderData.orderId} has been assigned to ${orderData.historyData!.driverName}.';
      } else if (orderData.historyType == COURIER_TRANSFER) {
        return 'Your Order#${orderData.orderId} has been transfered to ${orderData.historyData!.driverName}.';
      } else {
        return '${orderData.historyMessage}';
      }
    } else {
      return '${orderData.historyMessage}';
    }
  }*/
}
