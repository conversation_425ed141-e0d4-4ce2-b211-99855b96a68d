import 'package:rider/app_exports.dart';

class VerifyOTPScreen extends StatefulWidget {
  final String countryDialCode;
  final String countryISOCode;
  final String phone;
  final bool isSocialLogin;
  final String otpVerificationKey;

  const VerifyOTPScreen({
    super.key,
    required this.countryDialCode,
    required this.countryISOCode,
    required this.phone,
    required this.isSocialLogin,
    required this.otpVerificationKey,
  });
  @override
  VerifyOTPScreenState createState() => VerifyOTPScreenState();
}

class VerifyOTPScreenState extends State<VerifyOTPScreen> {
  String _otp = '';
  String _otpVerificationKey = '';

  final TextEditingController _pinputController = TextEditingController();

  int _timerDuration = Constants.otpResendWaitSeconds;
  late Timer _timer;

  @override
  void initState() {
    hideAppActivity();
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_timerDuration == 1) {
        timer.cancel();
      }
      setState(() {
        _timerDuration--;
      });
    });

    _otpVerificationKey = widget.otpVerificationKey;

    super.initState();
  }

  @override
  void dispose() {
    hideAppActivity();
    if (_timer.isActive) {
      _timer.cancel();
    }
    super.dispose();
  }

  Future<void> _sendOTP() async {
    showAppActivity();

    var response = await reSendOTP(verificationKey: _otpVerificationKey);
    hideAppActivity();

    if (response.status == false) {
      toast(response.message);
    } else if (response.status == true) {
      toast("OTP has been resent successfully.");
      _otp = '';
      _pinputController.clear();
      _otpVerificationKey = response.data!.key;
      _timerDuration = Constants.otpResendWaitSeconds;
      _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
        if (_timerDuration == 1) {
          timer.cancel();
        }
        setState(() {
          _timerDuration--;
        });
      });
    }
  }

  Future<void> _verifyOTP() async {
    if (_otp.length != 6) {
      toast(Globals.language.enterOTP);
    } else if (_otpVerificationKey.isEmpty) {
      toast(Globals.language.serverErrorMsg);
      return;
    } else {
      showAppActivity();
      var value = await verifyOTP(
        _otpVerificationKey,
        _otp,
      );

      if (value == null) {
        toast(Globals.language.serverErrorMsg);
        hideAppActivity();
      } else if (!value['status']) {
        _pinputController.text = "";
        _otp = "";
        toast(value['message']);
        hideAppActivity();
        return;
      } else if (widget.isSocialLogin) {
        if (mounted) {
          Navigator.of(context).pop(true);
        }
      } else if (value['data']['user'] == null) {
        launchScreen(
          RegisterScreen(
            phone: widget.phone,
            countryDialCode: widget.countryDialCode,
            countryISOCode: widget.countryISOCode,
            isSocialLogin: false,
            oldUser: null,
          ),
          pageRouteAnimation: PageRouteAnimation.Slide,
        );
      } else {
        try {
          var user = UserModel.fromJson(value['data']['user']);

          bool result = await Globals.sharedPrefs.setString(
            SPKeys.user,
            jsonEncode(user.toJson()),
          );
          if (!result) {
            toast(Globals.language.errorMsg);
          } else {
            Globals.user = user;
            Globals.isUserLoggedIn = true;
            if (user?.profileImage != null) {
              Globals.sharedPrefs.setString("USER_PROFILE", user.profileImage!);
            }
            launchScreen(
              const DashboardWrapperScreen(),
              isNewTask: true,
              pageRouteAnimation: PageRouteAnimation.Slide,
            );
          }
        } catch (e) {
          toast(Globals.language.errorMsg);
        }
      }
      hideAppActivity();
    }
  }

  @override
  void setState(fn) {
    if (mounted) super.setState(fn);
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        hideKeyboard();
      },
      child: Scaffold(
        appBar: const RoooAppbar(
          title: 'Verify OTP',
          isDarkOverlay: false,
        ),
        bottomNavigationBar: BottomButton(
          text: Globals.language.verifyOTP,
          onPressed: _verifyOTP,
        ),
        body: Stack(
          children: [
            // Column(
            //   crossAxisAlignment: CrossAxisAlignment.start,
            //   children: [
            // Flexible(
            //   flex: 0,
            //   child: Stack(
            //     children: [
            //       // ClipPath(
            //       //   clipper: MyCustomClipper(),
            //       //   child: Container(
            //       //     color: Colors.black,
            //       //   ),
            //       // ),
            //       SizedBox(
            //         height: Platform.isIOS
            //             ? MediaQuery.sizeOf(context).height / 2.5 + 10
            //             : MediaQuery.sizeOf(context).height / 2 + 10,
            //         // ,
            //         child: Center(
            //             child: Image.asset(
            //           Assets.appLogo,
            //           width: MediaQuery.sizeOf(context).width - 100,
            //         )),
            //       ),
            //     ],
            //   ),
            // ),
            Padding(
              padding: const EdgeInsets.all(
                Layout.scaffoldBodyPadding,
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Text(
                  //   '${Globals.language.welcomeBack}, ',
                  //   style: const TextStyle(
                  //     fontWeight: FontWeight.bold,
                  //     fontSize: 20,
                  //   ),
                  // ),
                  const SizedBox(
                    height: 10,
                  ),
                  Text(
                    Globals.language.verifyOTPHeading,
                  ),
                  height10,
                  Center(
                    child: Pinput(
                      controller: _pinputController,
                      length: 6,
                      onCompleted: (pin) {
                        _otp = pin;
                      },
                    ),
                  ),
                  const SizedBox(
                    height: 15,
                  ),
                  Center(
                    child: InkWell(
                      onTap: _timerDuration == 0 ? _sendOTP : null,
                      child: Container(
                        padding: const EdgeInsets.all(
                          10 / 1.5,
                        ),
                        decoration: BoxDecoration(
                          color: Theme.of(context).brightness == Brightness.dark
                              ? Colors.grey[800]
                              : Colors.grey[_timerDuration == 0 ? 200 : 100],
                          borderRadius: BorderRadius.circular(
                            20,
                          ),
                        ),
                        child: _timerDuration == 0
                            ? Text(
                                Globals.language.iHaveNotReceivedACode,
                                style: TextStyle(
                                  fontSize: 12,
                                  color:
                                      Theme.of(context).colorScheme.onTertiary,
                                ),
                              )
                            : Text(
                                '${Globals.language.iHaveNotReceivedACode} ($_timerDuration)',
                                style: TextStyle(
                                  fontSize: 12,
                                  color:
                                      Theme.of(context).colorScheme.onTertiary,
                                ),
                              ),
                      ),
                    ),
                  )
                ],
              ),
            ),
            // ],
            // ),
            const ActivityIndicator(),
          ],
        ),
      ),
    );
  }
}
