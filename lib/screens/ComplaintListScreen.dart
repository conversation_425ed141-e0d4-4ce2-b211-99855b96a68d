import 'package:intl/intl.dart';
import 'package:rider/app_exports.dart';

class ComplaintListScreen extends StatefulWidget {
  final int complaint;
  const ComplaintListScreen({super.key, required this.complaint});

  @override
  ComplaintListScreenState createState() => ComplaintListScreenState();
}

class ComplaintListScreenState extends State<ComplaintListScreen> {
  TextEditingController messageCont = TextEditingController();
  ScrollController scrollController = ScrollController();
  var messageFocus = FocusNode();
  bool isMe = false;

  int currentPage = 1;
  int totalPage = 1;

  List<ComplaintList> complaintListData = [];
  String emptyDataMsg = '';

  @override
  void initState() {
    super.initState();
    init();
    scrollController.addListener(() {
      if (scrollController.position.pixels ==
          scrollController.position.maxScrollExtent) {
        if (currentPage < totalPage) {
          currentPage++;
          setState(() {});

          init();
        }
      }
    });
  }

  void init() async {
    await complaintList(complaintId: widget.complaint, currentPage: currentPage)
        .then((value) {
      if (value == null) {
        toast(Globals.language.errorMsg);
        return;
      }
      currentPage = value.pagination!.currentPage!;
      totalPage = value.pagination!.totalPages!;
      emptyDataMsg = value.message ?? '';

      if (currentPage == 1) {
        complaintListData.clear();
      }
      complaintListData.addAll(value.data!);
      setState(() {});
    }).onError((error, stackTrace) {
      log(error.toString());
      handleError(error, stackTrace);
    });
  }

  Future<void> save() async {
    Map req = {
      "complaint_id": widget.complaint,
      "comment": messageCont.text.trim(),
    };
    await complaintComment(request: req).then((value) {
      messageCont.clear();

      init();
    }).onError((error, stackTrace) {
      log(error.toString());
      handleError(error, stackTrace);
    });
  }

  printTime(String data) {
    String time = "";
    DateTime date = DateTime.parse(data).toLocal();

    time = DateFormat('dd-MMM-yy, hh:mm a').format(date);

    return time;
  }

  @override
  void setState(fn) {
    if (mounted) super.setState(fn);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: RoooAppbar(
        title: Globals.language.complainList,
        isDarkOverlay: false,
      ),
      body: Stack(
        children: [
          Container(
            padding: const EdgeInsets.only(bottom: 76),
            height: MediaQuery.sizeOf(context).height,
            width: MediaQuery.sizeOf(context).width,
            child: ListView.builder(
                reverse: true,
                controller: scrollController,
                itemCount: complaintListData.length,
                itemBuilder: (_, index) {
                  ComplaintList mData = complaintListData[index];
                  return Container(
                    margin: complaintListData[index].addedBy != "ADMIN"
                        ? EdgeInsets.only(
                            top: 6,
                            bottom: 6,
                            left: isRTL
                                ? 0
                                : MediaQuery.sizeOf(context).width * 0.25,
                            right: 8)
                        : EdgeInsets.only(
                            top: 6,
                            bottom: 6,
                            left: 8,
                            right: isRTL
                                ? 0
                                : MediaQuery.sizeOf(context).width * 0.25),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        if (complaintListData[index].addedBy == "ADMIN")
                          Container(
                            height: 35,
                            width: 35,
                            decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                image: DecorationImage(
                                    image: NetworkImage(
                                        mData.userProfileImage.validate())),
                                border: Border.all(color: Colors.black12)),
                          ),
                        if (complaintListData[index].addedBy == "ADMIN")
                          const SizedBox(width: 8),
                        Expanded(
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            crossAxisAlignment:
                                complaintListData[index].addedBy != "ADMIN"
                                    ? CrossAxisAlignment.end
                                    : CrossAxisAlignment.start,
                            mainAxisAlignment:
                                complaintListData[index].addedBy != "ADMIN"
                                    ? MainAxisAlignment.end
                                    : MainAxisAlignment.start,
                            children: [
                              Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 12, vertical: 8),
                                decoration: BoxDecoration(
                                    boxShadow: const [
                                      BoxShadow(
                                          color: Colors.grey,
                                          blurRadius: 0.1,
                                          spreadRadius: 0.2), //BoxShadow
                                    ],
                                    // color: complaintListData[index].addedBy !=
                                    //         ADMIN
                                    //     ?
                                    //     : Theme.of(context).cardColor,
                                    borderRadius: BorderRadius.circular(16)),
                                child: Text(
                                  mData.comment.validate(),
                                  // style: primaryTextStyle(
                                  //     color:
                                  //         complaintListData[index].addedBy !=
                                  //                 ADMIN
                                  //             ? Colors.white
                                  //             : textPrimaryColorGlobal),
                                  maxLines: null,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(printTime(mData.createdAt.validate()),
                                  style: secondaryTextStyle(size: 12)),
                            ],
                          ),
                        ),
                        if (complaintListData[index].addedBy != "ADMIN")
                          const SizedBox(width: 8),
                        if (complaintListData[index].addedBy != 'ADMIN')
                          Container(
                            height: 35,
                            width: 35,
                            decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                image: DecorationImage(
                                    image: NetworkImage(
                                        mData.userProfileImage.validate())),
                                border: Border.all(color: Colors.black12)),
                          ),
                      ],
                    ),
                  );
                }),
          ),
          Positioned(
            bottom: Platform.isIOS ? 10 * 2 : 16,
            left: 16,
            right: 16,
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(30),
                boxShadow: const [
                  BoxShadow(
                    spreadRadius: 0.5,
                    blurRadius: 0.5,
                  ),
                ],
                color: Theme.of(context).cardColor,
              ),
              padding: const EdgeInsets.only(
                left: 8,
                right: 8,
              ),
              width: MediaQuery.sizeOf(context).width,
              child: Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: messageCont,
                      decoration: InputDecoration(
                        border: InputBorder.none,
                        hintText: Globals.language.writeMsg,
                        hintStyle: secondaryTextStyle(),
                        contentPadding:
                            const EdgeInsets.symmetric(horizontal: 8),
                      ),
                      cursorColor: Colors.black,
                      focusNode: messageFocus,
                      textCapitalization: TextCapitalization.sentences,
                      keyboardType: TextInputType.multiline,
                      minLines: 1,
                      style: primaryTextStyle(),
                      // textInputAction: mIsEnterKey ? TextInputAction.send : TextInputAction.newline,
                      onSubmitted: (s) {
                        if (messageCont.text.isEmpty) {
                          return toast(Globals.language.pleaseEnterMsg);
                        } else {
                          save();
                        }
                      },
                      maxLines: 5,
                    ),
                  ),
                  inkWellWidget(
                    child: const Icon(Icons.send, size: 25),
                    onTap: () {
                      if (messageCont.text.isEmpty) {
                        return toast(Globals.language.pleaseEnterMsg);
                      } else {
                        save();
                      }
                    },
                  )
                ],
              ),
            ),
          ),
          if (isAppActivityRunning.value) loaderWidget(),
          if (!isAppActivityRunning.value && complaintListData.isEmpty)
            emptyWidget(emptyDataMsg: emptyDataMsg)
        ],
      ),
    );
  }
}
