import 'package:rider/app_exports.dart';

import 'package:flutter/services.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:rider/screens/RideHelpScreen.dart';
import '../components/coupon_info.dart';
import '../screens/ComplaintScreen.dart';
import 'package:url_launcher/url_launcher.dart';
// import '../components/AboutWidget.dart';
// import '../components/GenerateInvoice.dart';
import '../model/ComplaintModel.dart';
import '../global/models/ride_model.dart';
import '../model/DriverRatting.dart';
import '../model/OrderHistory.dart';

import 'RideHistoryScreen.dart';

class RideDetailScreen extends StatefulWidget {
  final int orderId;
  final bool isFromHistory;
  final bool isUpcomingRide;
  final bool isFromNotifications;

  const RideDetailScreen({
    super.key,
    required this.orderId,
    this.isFromHistory = false,
    this.isUpcomingRide = false,
    this.isFromNotifications = false,
  });

  @override
  RideDetailScreenState createState() => RideDetailScreenState();
}

class RideDetailScreenState extends State<RideDetailScreen> {
  RiderModel? riderModel;
  List<RideHistory> rideHistory = [];
  DriverRatting? driverRatting;
  ComplaintModel? complaintData;
  Payment? payment;
  UserModel? userData;

  String emptyDataMsg = '';

  @override
  void initState() {
    showAppActivity();
    super.initState();
    init();
  }

  @override
  void dispose() {
    hideAppActivity();
    if (Platform.isIOS) {
      SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle.dark);
    }
    super.dispose();
  }

  void init() async {
    showAppActivity();
    await rideDetail(orderId: widget.orderId).then((value) async {
      if (value == null) {
        toast(Globals.language.errorMsg);
        return;
      }
      riderModel = value.data!;
      rideHistory.addAll(value.rideHistory!);
      driverRatting = value.driverRatting;
      complaintData = value.complaintModel;
      payment = value.payment;
      if (riderModel!.driverId != null) {
        await getDriverDetail(userId: riderModel!.driverId).then((value) {
          if (!value.status) {
            toast(value.message);
          } else {
            userData = value.data;
          }

          setState(() {
            hideAppActivity();
          });
        }).onError((error, stackTrace) {
          toast(Globals.language.serverErrorMsg);
          handleError(error, stackTrace);
        });
      }
      setState(() {
        hideAppActivity();
      });
    }).onError((error, stackTrace) {
      toast(Globals.language.serverErrorMsg);
      handleError(error, stackTrace);
    });
  }

  @override
  void setState(fn) {
    if (mounted) super.setState(fn);
  }

  Future<void> _getInvoiceLink() async {
    showAppActivity();

    var result = await getInvoiceLink(widget.orderId);
    if (result == null || result['status'] != true) {
      toast(result?['message'] ?? Globals.language.errorMsg);
    } else {
      launchUrl(
        Uri.parse(
          result['data']['link'],
        ),
        mode: LaunchMode.externalApplication,
      );
    }
    hideAppActivity();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
            riderModel != null
                ? "${Globals.language.lblRide} #${riderModel!.id}"
                : "",
            style: boldTextStyle(color: Colors.white)),
        systemOverlayStyle: Platform.isIOS ? SystemUiOverlayStyle.light : null,
        actions: widget.isUpcomingRide
            ? null
            : [
                /* Raise complaint button*/
                AppButtonWidget(
                    fullWidth: false,
                    color: Colors.red,
                    text: "Raise Complaint",
                    textStyle: const TextStyle(fontSize: 2),
                    onTap: () {
                      if ((complaintData?.status ?? "resolved").trim().toLowerCase() == "resolved") {
                        launchScreen(
                          ComplaintScreen(
                            driverRatting: driverRatting ?? DriverRatting(),
                            complaintModel: complaintData,
                            riderModel: riderModel,
                            isFromHistory: widget.isFromHistory,
                          ),
                          pageRouteAnimation: PageRouteAnimation.SlideBottomTop,
                        );
                      } else {
                        launchScreen(
                          RideHelpScreen(indicatorUpdater: () {}),
                          pageRouteAnimation: PageRouteAnimation.SlideBottomTop,
                        );
                      }
                    })
              ],
      ),
      bottomNavigationBar: widget.isFromHistory || widget.isFromNotifications
          ? null
          : Padding(
              padding:
                  EdgeInsets.fromLTRB(10, 10, 10, Platform.isIOS ? 10 * 2 : 10),
              child: AppButton(
                text: Globals.language.continueD,
                onPressed: () {
                  // init();
                  launchScreen(
                    const DashboardWrapperScreen(),
                    isNewTask: true,
                  );
                },
              ),
            ),
      body: Stack(
        children: [
          if (riderModel != null)
            SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 8),
                  Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(13),
                    ),
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            userData != null
                                ? Text(
                                    "${userData!.firstName!} ${userData!.lastName!}",
                                    style: boldTextStyle())
                                : const SizedBox(),
                            widget.isUpcomingRide
                                ? const SizedBox()
                                : inkWellWidget(
                                    onTap: () {
                                      _getInvoiceLink();
                                    },
                                    child: Row(
                                      mainAxisAlignment: MainAxisAlignment.end,
                                      children: [
                                        Text(Globals.language.invoice,
                                            style: primaryTextStyle()),
                                        const SizedBox(width: 4),
                                        const Padding(
                                          padding: EdgeInsets.only(top: 2),
                                          child: Icon(
                                            MaterialIcons.file_download,
                                            size: 18,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                          ],
                        ),
                        const Divider(),
                        Row(
                          children: [
                            const Icon(Ionicons.calendar,
                                // color: textSecondaryColorGlobal,
                                size: 18),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Padding(
                                padding: const EdgeInsets.only(top: 2),
                                child: Text(
                                    (riderModel!.status !=
                                                RideStatus.canceled &&
                                            riderModel!.status !=
                                                RideStatus.completed)
                                        ? printDate(
                                            riderModel!.datetime.validate())
                                        : printDate(
                                            riderModel!.createdAt.validate()),
                                    style: primaryTextStyle(size: 14)),
                              ),
                            ),
                            if (riderModel!.isPoolingRide == true)
                              const Badge(
                                label: Padding(
                                  padding: EdgeInsets.all(4.0),
                                  child: Text("Pool"),
                                ),
                              ),
                            const SizedBox(
                              width: 2,
                            ),
                            if (riderModel!.isBusinessRide == true)
                              const Badge(
                                label: Padding(
                                  padding: EdgeInsets.all(4.0),
                                  child: Text("Business"),
                                ),
                              )
                          ],
                        ),
                        const Divider(height: 30, thickness: 1),
                        Text(
                            '${Globals.language.lblDistance} ${riderModel!.distance.toString()} ${riderModel!.distanceUnit.toString()}',
                            style: boldTextStyle(size: 14)),
                        const SizedBox(height: 10),
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Column(
                              children: [
                                const Icon(Icons.near_me, color: Colors.green),
                                const SizedBox(height: 4),
                                SizedBox(
                                  height: 80,
                                  child: DottedLine(
                                    dashColor: checkIfDarkModeIsOn(context)
                                        ? Colors.white
                                        : Colors.black,
                                    direction: Axis.vertical,
                                    lineLength: double.infinity,
                                    lineThickness: 1,
                                    dashLength: 2,
                                  ),
                                ),
                                const SizedBox(height: 2),
                                const Icon(Icons.location_on,
                                    color: Colors.red),
                              ],
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  if (riderModel!.startTime != null)
                                    Text(
                                        riderModel!.startTime != null
                                            ? printDate(riderModel!.startTime!)
                                            : '',
                                        style: secondaryTextStyle(size: 12)),
                                  if (riderModel!.startTime != null)
                                    const SizedBox(height: 4),
                                  SizedBox(
                                    height: 60,
                                    child: Text(
                                        riderModel!.startAddress.validate(),
                                        maxLines: 2,
                                        overflow: TextOverflow.ellipsis,
                                        style: primaryTextStyle(size: 14)),
                                  ),
                                  const SizedBox(
                                    height: 20,
                                  ),
                                  if (riderModel!.endTime != null)
                                    Text(
                                        riderModel!.endTime != null
                                            ? printDate(riderModel!.endTime!)
                                            : '',
                                        style: secondaryTextStyle(size: 12)),
                                  if (riderModel!.endTime != null)
                                    const SizedBox(height: 4),
                                  SizedBox(
                                    height: 60,
                                    child: Text(
                                        riderModel!.endAddress.validate(),
                                        maxLines: 2,
                                        overflow: TextOverflow.ellipsis,
                                        style: primaryTextStyle(size: 14)),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                        const Divider(height: 30, thickness: 1),
                        if (!widget.isUpcomingRide)
                          inkWellWidget(
                            onTap: () {
                              launchScreen(
                                  RideHistoryScreen(rideHistory: rideHistory),
                                  pageRouteAnimation:
                                      PageRouteAnimation.SlideBottomTop);
                            },
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(Globals.language.viewHistory,
                                    style: primaryTextStyle()),
                                const Icon(Entypo.chevron_right, size: 18),
                              ],
                            ),
                          ),
                      ],
                    ),
                  ),
                  _getStopsView(),
                  _getDestinationsView(),
                  if (!widget.isUpcomingRide) const SizedBox(height: 16),
                  if (!widget.isUpcomingRide)
                    Container(
                      decoration: const BoxDecoration(),
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(Globals.language.paymentDetails,
                              style: boldTextStyle(size: 16)),
                          const Divider(height: 30, thickness: 1),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(Globals.language.paymentType,
                                  style: primaryTextStyle(
                                    size: 14,
                                  )),
                              Text(
                                  paymentStatus(
                                      riderModel!.paymentType.validate()),
                                  style: boldTextStyle(
                                    size: 14,
                                  )),
                            ],
                          ),
                          const SizedBox(height: 16),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(Globals.language.paymentStatus,
                                  style: primaryTextStyle(
                                    size: 14,
                                  )),
                              Text(
                                  paymentStatus(
                                      riderModel!.paymentStatus.validate()),
                                  style: boldTextStyle(
                                    size: 14,
                                  )),
                            ],
                          ),
                        ],
                      ),
                    ),
                  if (riderModel!.otherRiderData != null)
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SizedBox(height: 16),
                        Container(
                          width: MediaQuery.sizeOf(context).width,
                          decoration: const BoxDecoration(
                              // color: Globals.appStore.isDarkMode
                              //     ? scaffoldSecondaryDark
                              //     :.withOpacity(0.05),
                              ),
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(Globals.language.lblRideInformation,
                                  style: boldTextStyle()),
                              const Divider(height: 30, thickness: 1),
                              RichText(
                                text: TextSpan(
                                  children: [
                                    const WidgetSpan(
                                        child: Padding(
                                      padding: EdgeInsets.only(right: 8),
                                      child: Icon(
                                        FontAwesome.user,
                                        size: 18,
                                        // color: textPrimaryColorGlobal
                                      ),
                                    )),
                                    TextSpan(
                                        text: riderModel!.otherRiderData!.name
                                            .validate(),
                                        style: primaryTextStyle())
                                  ],
                                ),
                              ),
                              // SizedBox(height: 10),
                              // InkWell(
                              //   onTap: () {
                              //     launchUrl(
                              //         Uri.parse(
                              //             'tel:${riderModel!.otherRiderData!.conatctNumber.validate()}'),
                              //         mode: LaunchMode.externalApplication);
                              //   },
                              //   child: Row(
                              //     crossAxisAlignment: CrossAxisAlignment.start,
                              //     children: [
                              //       Container(
                              //         padding: EdgeInsets.all(2),
                              //         decoration: BoxDecoration(
                              //             color: Colors.green,
                              //             borderRadius: radius(6)),
                              //         child: Icon(Icons.call_sharp,
                              //             color: Colors.white, size: 16),
                              //       ),
                              //       SizedBox(width: 8),
                              //       Text(
                              //           riderModel!
                              //               .otherRiderData!.conatctNumber
                              //               .validate(),
                              //           style: primaryTextStyle())
                              //     ],
                              //   ),
                              // ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  if (!widget.isUpcomingRide) const SizedBox(height: 16),
                  if (!widget.isUpcomingRide &&
                      (riderModel?.driverName ?? "").isNotEmpty)
                    InkWell(
                      onTap: () {},
                      child: Container(
                        width: MediaQuery.sizeOf(context).width,
                        decoration: const BoxDecoration(),
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(Globals.language.aboutDriver,
                                style: boldTextStyle()),
                            const Divider(height: 30, thickness: 1),
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                ClipRRect(
                                  borderRadius: BorderRadius.circular(35),
                                  child: commonCachedNetworkImage(
                                      riderModel!.driverProfileImage.validate(),
                                      height: 70,
                                      width: 70,
                                      fit: BoxFit.cover),
                                ),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      const SizedBox(height: 2),
                                      Text(riderModel!.driverName.validate(),
                                          style: boldTextStyle()),
                                      const SizedBox(height: 6),
                                      if (driverRatting != null)
                                        RatingBar.builder(
                                          direction: Axis.horizontal,
                                          glow: false,
                                          allowHalfRating: false,
                                          ignoreGestures: true,
                                          wrapAlignment:
                                              WrapAlignment.spaceBetween,
                                          itemCount: 5,
                                          itemSize: 16,
                                          initialRating: double.parse(
                                              driverRatting!.rating.toString()),
                                          itemPadding:
                                              const EdgeInsets.symmetric(
                                                  horizontal: 0),
                                          itemBuilder: (context, _) =>
                                              const Icon(Icons.star,
                                                  color: Colors.amber),
                                          onRatingUpdate: (rating) {
                                            //
                                          },
                                        ),
                                      // SizedBox(height: 2),
                                      // Row(
                                      //   mainAxisAlignment:
                                      //       MainAxisAlignment.spaceEvenly,
                                      //   children: [
                                      //     Expanded(
                                      //       child: Text(
                                      //           riderModel!.driverContactNumber
                                      //               .validate(),
                                      //           style:
                                      //               primaryTextStyle(size: 14)),
                                      //     ),
                                      //     // InkWell(
                                      //     //   onTap: () {
                                      //     //     launchUrl(
                                      //     //         Uri.parse(
                                      //     //             'tel:${riderModel!.driverContactNumber}'),
                                      //     //         mode: LaunchMode
                                      //     //             .externalApplication);
                                      //     //   },
                                      //     //   child: Container(
                                      //     //     padding: EdgeInsets.all(4),
                                      //     //     decoration: BoxDecoration(
                                      //     //         color: Colors.green,
                                      //     //         borderRadius:
                                      //     //             BorderRadius.circular(
                                      //     //                 defaultRadius)),
                                      //     //     child: Icon(Icons.call_sharp,
                                      //     //         color: Colors.white, size: 18),
                                      //     //   ),
                                      //     // )
                                      //   ],
                                      // )
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  if (!widget.isUpcomingRide) const SizedBox(height: 16),
                  if (!widget.isUpcomingRide)
                    Container(
                      decoration: const BoxDecoration(
                          // color: Globals.appStore.isDarkMode
                          //     ? scaffoldSecondaryDark
                          //     :.withOpacity(0.05),
                          ),
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(Globals.language.priceDetail,
                              style: boldTextStyle()),
                          const Divider(height: 30, thickness: 1),
                          totalCount(
                              title: "Fare",
                              value: (riderModel!.rideFare ?? 0)),
                          totalCount(
                              title: Globals.language.waitTime,
                              value: (riderModel!.perMinuteWaitingCharge ?? 0)),
                          totalCount(
                              title: Globals.language.stateCharges,
                              value: (riderModel!.extraChargesAmount ?? 0)),
                          totalCount(
                              title: Globals.language.tip,
                              value: (riderModel!.tips ?? 0)),
                          totalCount(
                              title: "GST", value: (riderModel!.tax ?? 0)),
                          totalCount(
                              title: "Peak Fare",
                              value: (riderModel!.peak_fare ?? 0)),
                          totalCount(
                              title: "Stripe Transaction Charges",
                              value: (riderModel!.stripe_transaction_charges ??
                                  0)),
                          totalCount(
                              title: "Airport Charges",
                              value: (riderModel!.airportCharges ?? 0)),
                          totalCount(
                              title: "Platform Fee",
                              value: (riderModel!.platformFee ?? 0)),
                          totalCount(
                              title: Globals.language.couponDiscount,
                              value: (riderModel!.couponDiscount ?? 0)),
                          if (payment != null) const SizedBox(height: 8),
                          if ((riderModel!.extraCharges ?? []).isNotEmpty)
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(Globals.language.extraCharges,
                                    style: boldTextStyle()),
                                const SizedBox(height: 8),
                                ...riderModel!.extraCharges!.map((e) {
                                  return Padding(
                                    padding: const EdgeInsets.only(
                                        top: 4, bottom: 4),
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Text(
                                            e.key
                                                .validate()
                                                .capitalizeFirstLetter(),
                                            style: primaryTextStyle()),
                                        Text(
                                            '${Constants.currencySymbol} ${e.value}',
                                            style: primaryTextStyle()),
                                      ],
                                    ),
                                  );
                                })
                              ],
                            ),
                          if (riderModel!.couponData != null &&
                              riderModel!.couponDiscount != 0)
                            const SizedBox(height: 8),
                          if (riderModel!.couponData != null &&
                              riderModel!.couponDiscount != 0)
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                inkWellWidget(
                                  onTap: () async {
                                    await showDialog(
                                      context: context,
                                      builder: (context) => AlertDialog(
                                          content: CouponInfo(
                                              riderModel!.couponData),
                                          contentPadding:
                                              const EdgeInsets.all(0)),
                                      barrierDismissible: false,
                                    );
                                  },
                                  child: Row(
                                    children: [
                                      Text(Globals.language.couponDiscount,
                                          style: primaryTextStyle()),
                                      const SizedBox(width: 4),
                                      const Icon(
                                        Icons.info_outline,
                                        size: 14,
                                        // color: textPrimaryColorGlobal,
                                      )
                                    ],
                                  ),
                                ),
                                Text(
                                    '-${Constants.currencySymbol} ${riderModel!.couponDiscount.toString()}',
                                    style:
                                        primaryTextStyle(color: Colors.green)),
                              ],
                            ),
                          const Divider(thickness: 1),
                          totalCount(
                              title: Globals.language.total,
                              value: (riderModel!.totalAmount ?? 0),
                              isTotal: true),
                          const SizedBox(height: 10),
                        ],
                      ),
                    ),
                ],
              ),
            ),
          if (!isAppActivityRunning.value && riderModel == null)
            emptyWidget(emptyDataMsg: emptyDataMsg),
          const ActivityIndicator(),
        ],
      ),
    );
  }

  Widget _getStopsView() {
    if ((riderModel!.stops ?? []).isEmpty) {
      return const SizedBox();
    }
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
              color: Colors.grey.shade100,
              borderRadius: BorderRadius.circular(
                10,
              )),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                "Ride Stops",
                style: TextStyle(
                  color: Colors.black,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Divider(),
              ...riderModel!.stops!.map((e) {
                return Material(
                  child: ListTile(
                    tileColor: Colors.white,
                    title: Text(
                      e.title,
                      style: const TextStyle(color: Colors.black),
                    ),
                  ),
                );
              }),
            ],
          ),
        ),
        if ((riderModel!.stops ?? []).isNotEmpty) const SizedBox(height: 8),
      ],
    );
  }

  Widget _getDestinationsView() {
    if ((riderModel!.destinations ?? []).isEmpty) {
      return const SizedBox();
    }
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
              color: Colors.grey.shade100,
              borderRadius: BorderRadius.circular(
                10,
              )),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                "Ride Destinations",
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
              ),
              const Divider(),
              ...riderModel!.destinations!.map((e) {
                return Material(
                  child: ListTile(
                    tileColor: Colors.white,
                    title: Text(
                      e.title,
                      style: const TextStyle(color: Colors.black),
                    ),
                  ),
                );
              }),
            ],
          ),
        ),
        if ((riderModel!.stops ?? []).isNotEmpty) const SizedBox(height: 8),
      ],
    );
  }
}
