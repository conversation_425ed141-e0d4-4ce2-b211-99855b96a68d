import 'package:flutter/services.dart';
import 'package:rider/app_exports.dart';
import 'package:rider/features/ride_flow/screen/current_ride_screen.dart';
import 'package:rider/interactive_onboarding.dart';
import 'package:rider/screens/all_rides_screen.dart';
import 'package:rider/screens/review_screen.dart';
import 'package:rider/screens/rider_dashboard_screen.dart';
import 'package:rider/screens/account_screen.dart';
import 'package:rider/screens/waiting_charges_and_tip_screen.dart';

class DashboardWrapperScreen extends StatefulWidget {
  final bool isRecentSignUp;

  const DashboardWrapperScreen({super.key, this.isRecentSignUp = false});

  @override
  State<DashboardWrapperScreen> createState() => _DashboardWrapperScreenState();
}

class _DashboardWrapperScreenState extends State<DashboardWrapperScreen> {
  late PageController _pageController;

  int _currentIndex = 0;

  final List<Widget> _pages = [];

  DateTime _lastTime = DateTime.now();

  @override
  void initState() {
    _pages.addAll([
      RiderDashBoardScreen(
        isRecentSignUp: widget.isRecentSignUp,
      ),
      const AllRidesScreen(),
      const AccountScreen(),
    ]);
    hideAppActivity();

    AppMqttService.initialize().then((a) {
      getCurrentRequest();
    });

    // Globals.isAdminNotifiedForSOS = false;


    _pageController = PageController(initialPage: _currentIndex);

    afterBuildCreated(() {
      Globals.homePageToMyRidesNavigator = () {
        _pageController.jumpToPage(1);
        setState(() {
          _currentIndex = 1;
        });
      };
      Globals.homePageRedirect = () {
        _pageController.jumpToPage(0);
        setState(() {
          _currentIndex = 0;
        });
      };
    });

    super.initState();
  }

  @override
  void dispose() {
    _pageController.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) {
        if (!didPop) {
          if (DateTime.now().difference(_lastTime).inSeconds <= 2) {
            SystemNavigator.pop();
          } else {
            _lastTime = DateTime.now();
            toast("Double tap to exit");
          }
        }
      },
      child: Scaffold(
        // appBar: AppBar(
        //   title: Text('Test'),
        // ),
        bottomNavigationBar: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (checkIfDarkModeIsOn(context))
              Container(
                color: Colors.black,
                height: 1.5,
              ),
            Container(
              decoration: BoxDecoration(
                color: checkIfDarkModeIsOn(context) ? Colors.black : Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Theme.of(context).primaryColor,
                    spreadRadius: 0.0,
                    blurRadius: 0,
                    offset: const Offset(0, -1)
                  )
                ]
              ),
              child: BottomNavigationBar(
                selectedIconTheme: IconThemeData(
                  color: Theme.of(context).primaryColor
                ),
                unselectedIconTheme: IconThemeData(
                  color: checkIfDarkModeIsOn(context) ? Colors.grey : Colors.black
                ),
                unselectedItemColor: checkIfDarkModeIsOn(context) ? Colors.grey : Colors.black,
                selectedItemColor: Theme.of(context).primaryColor,
                unselectedFontSize: 12,
                selectedFontSize: 0,
                backgroundColor: checkIfDarkModeIsOn(context) ? Colors.black : Colors.white,
                currentIndex: _currentIndex,
                onTap: (selectedPageIndex) {
                  hideAppActivity();

                  setState(() {
                    _currentIndex = selectedPageIndex;
                    _pageController.jumpToPage(selectedPageIndex);
                  });
                },
                items: [
                  BottomNavigationBarItem(
                    key: Globals.dashboardWidgetKey,
                    icon: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(40)
                      ),
                      child:  Icon(
                        Icons.home,
                        color: Theme.of(context).brightness == Brightness.dark
                            ? Colors.grey[400]
                            : Colors.black,
                      ),
                    ),
                    label: Globals.language.homeText,
                  ),
                  BottomNavigationBarItem(
                    key: Globals.ridesWidgetKey,
                    icon: Icon(
                      Icons.bookmark,
                      color: Theme.of(context).brightness == Brightness.dark
                          ? Colors.grey[400]
                          : Colors.black,
                    ),
                    label: Globals.language.activityText,
                  ),
                  BottomNavigationBarItem(
                    key: Globals.profileWidgetKey,
                    icon: Icon(
                      Icons.account_circle,
                      color: Theme.of(context).brightness == Brightness.dark
                          ? Colors.grey[400]
                          : Colors.black,
                    ),
                    label: Globals.language.accountText,
                  ),
                  //   BottomNavigationBarItem(
                  //   icon: Icon(
                  //     Icons.account_circle,
                  //   ),
                  //   label: "selectService",
                  // )
                ],
              ),
            ),
          ],
        ),
        body: PageView(
          controller: _pageController,
          physics: const NeverScrollableScrollPhysics(),
          children: _pages,
        ),
      ),
    );
  }
}

Future<void> getCurrentRequest({bool showLoading = true}) async {
  await getCurrentRideRequest().then((value) {
    if (value.status == false) {
      toast(value.message);
      return;
    }
    var servicesListData = value.data!.rideRequest ?? value.data!.onRideRequest;
    if (servicesListData != null) {
      if (!(value.data!.isSecondPreauth ?? false) &&
          (servicesListData.status == RideStatus.reached ||
              servicesListData.status == RideStatus.completed)) {
        launchScreen(WaitingChargesAndTipScreen(request: value.data!),
            isNewTask: true);
      } else if (servicesListData.status != RideStatus.completed) {
        launchScreen(
          isNewTask: true,
          CurrentRideScreen(currentRide: value.data!),
          pageRouteAnimation: PageRouteAnimation.SlideBottomTop,
        );
      } else if (servicesListData.status == RideStatus.completed &&
          servicesListData.isRiderRated == 0) {
        launchScreen(
          ReviewScreen(
            rideRequest: servicesListData,
            driverData: value.data!.driver,
          ),
          pageRouteAnimation: PageRouteAnimation.SlideBottomTop,
          isNewTask: true,
        );
      }
    }
  }).onError((error, stackTrace) {
    handleError(error, stackTrace);

    log(error.toString());
  });
}
