import 'package:flex_color_scheme/flex_color_scheme.dart';
import 'package:rider/app_exports.dart';

import 'package:rider/model/PaginationModel.dart';
import 'package:rider/screens/add_new_card.dart';

import 'package:rider/services/stripe_service.dart';

class PaymentCardsScreen extends StatefulWidget {
  const PaymentCardsScreen({super.key});

  @override
  MyCarsScreenState createState() => MyCarsScreenState();
}

class MyCarsScreenState extends State<PaymentCardsScreen> {
  PaymentCardDataResponse dataResponse =
      PaymentCardDataResponse(pagination: PaginationModel(), data: []);

  int nextPage = 1;
  int totalPage = 1;
  int riderId = 0;
  bool isMoreData = true;
  bool isNoData = false;

  @override
  void initState() {
    getPaymentCards();
    super.initState();
  }

  @override
  void setState(fn) {
    if (mounted) super.setState(fn);
  }

  @override
  void dispose() {
    hideAppActivity();
    super.dispose();
  }

  Future<void> getPaymentCards() async {
    showAppActivity();
    dataResponse.data = [];

    var response = await StripeService.getSavedCard(Globals.user.stripeCustomerId);
    if (response == null) {
      showErrorToast();
      return;
    }

    for (var card in response) {
      if (!HelperMethods.isStripeCardExpired(
          expiryMonth: card.exp_month, expiryYear: card.exp_year)) {
        dataResponse.data.add(card);
      }
    }

    setState(() {
      hideAppActivity();
    });
  }

  Future<void> deleteCard(String id) async {
    bool? shouldDelete = await showAppDialog(
      title: "Are you sure you want to delete this card?",
      dialogType: AppDialogType.confirmation,
      onAccept: () {
        return Navigator.of(context).pop(true);
      },
    );

    if (shouldDelete == true) {
      showAppActivity();
      var response = await StripeService.deleteCard(cardId: id);
      if (!response) {
        showErrorToast();
        return hideAppActivity();
      }
      dataResponse.data.removeWhere((element) => element.id == id);
      setState(() {
        hideAppActivity();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: RoooAppbar(
        title: Globals.language.paymentCards,
        isDarkOverlay: false,
      ),
      bottomNavigationBar: BottomButton(
        text: Globals.language.addNewCard,
        onPressed: () async {
          bool? result = await Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => const AddNewCardScreen(
                previousCards: [],
              ),
            ),
          );

          if (result == true) {
            getPaymentCards();
          }
        },
      ),
      body: Stack(
        children: [
          dataResponse.data.isEmpty && !isAppActivityRunning.value
              ? emptyWidget(
                  emptyDataMsg: Globals.language.noCards,
                )
              : ListView.separated(
                  padding: const EdgeInsets.all(
                    10,
                  ),
                  itemBuilder: (context, index) {
                    var item = dataResponse.data[index];
                    return Column(
                      children: [
                        PaymentCardWidget(
                          isSelected: false,
                          card: item,
                          onSelect: () {},
                          onDelte: () {
                            deleteCard(item.id);
                          },
                        ),
                      ],
                    );
                  },
                  separatorBuilder: (context, index) => const SizedBox(
                    height: 10,
                  ),
                  itemCount: dataResponse.data.length,
                ),
          const ActivityIndicator(),
        ],
      ),
    );
  }
}

class PaymentCardWidget extends StatelessWidget {
  final bool isSelected;
  final SavedCard card;
  final Function onSelect;
  final Function onDelte;
  const PaymentCardWidget({
    super.key,
    required this.card,
    required this.isSelected,
    required this.onSelect,
    required this.onDelte,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: EdgeInsets.zero,
      shape: BeveledRectangleBorder(
        borderRadius: BorderRadius.circular(0.0),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Material(
            shape: BeveledRectangleBorder(
              borderRadius: BorderRadius.circular(2),
            ),
            color:AppColors.darkThemePrimaryColor,
            child: InkWell(
              onTap: () {
                onSelect();
              },
              child: Container(
                width: 400,
                height: 180,
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(0.0),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      card.brand.toUpperCase(),
                      style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 24,
                          color: Colors.white),
                    ),
                    const SizedBox(
                      height: 20,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        ...[
                          for (int i = 0; i < 3; i++)
                            const Text('XXXX',
                                style: TextStyle(
                                    fontSize: 18, color: Colors.white)),
                        ],
                        Text(
                          card.last4,
                          style: const TextStyle(fontSize: 18, color: Colors.white),
                        ),
                      ],
                    ),
                    const SizedBox(
                      height: 20,
                    ),
                    Row(
                      children: [
                        const Text(
                          'VALID THRU',
                          style: TextStyle(color: Colors.white),
                        ),
                        const SizedBox(
                          width: 10,
                        ),
                        Text(
                          '${card.exp_month}/${card.exp_year}',
                          style: const TextStyle(
                              fontWeight: FontWeight.bold, color: Colors.white),
                        ),
                      ],
                    )
                  ],
                ),
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: InkWell(
              onTap: () {
                onDelte();
              },
              child: const Icon(
                Icons.delete,
                color: Colors.red,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
