import 'package:rider/app_exports.dart';

abstract class Constants {
  static const String appName = 'ROOO Rider';
 static var serverErrorMessage = 'Server error';

 static double borderRadiusValue = 10;
static BorderRadius appRadius = BorderRadius.circular(borderRadiusValue);

static double screenPaddingValue = 18;
static EdgeInsets screenPadding = EdgeInsets.all(screenPaddingValue);


static const defaultCountryIsoCode = 'AU';



/* OneSignal*/
  static const String oneSignalRiderAppId =
      '************************************';
  static const String oneSignalRiderRestKey =
      '************************************************';

  static const String oneSignalDriverAppId =
      '************************************';
  static const String oneSignalDriverRestKey =
      '************************************************';

  static const errorMSG = 'Something Went Wrong';

  static const googleMapAPIKey = 'AIzaSyBE1ks4PYI4zjuLEph1qCo5201O2XKrqM8';

  static const serverURL =
      'https://rooo.com.au/software'; // Don't add slash at the end of the url
  static const apiURL = "$serverURL/api/";

  static const stripeURL = 'https://api.stripe.com/v1/payment_intents';

//Zego
  static const zegoAppId = 697103749;
  static const zegoAppSign =
      '2f51d4c8da2997643f81c52fa7cf43ee5f5e2636752a4474115ff7ec666a7074';
  static const zegoResorceId = "rider_zego_resource_1";
  static const zegoNoUserError = "6000281";

  static const fixedAmount = 'fixed';
  static const perAmount = 'percentage';
  static const cashWallet = 'cash_wallet';
  static const cash = 'cash';
  static const wallet = 'wallet';
  static const preAuth = 'pre-authorized payment';

  static const int maxStopsInARide = 3;
  // static const String currencySymbol = "\$";
  static const String currencySymbol = "AUD ";

  static const String msClientId = "79af15f3-5806-473b-b380-57aa680891d0";
  static const String msDirectoryId = "65688fba-2686-45a3-8bba-9c6576507527";

  static const int otpResendWaitSeconds = 60;
}

/* shared preference keys */
abstract class SPKeys {
  static const isFirstTime = 'IS_FIRST_TIME';
  static const isLocationPermissionAsked = 'IS_LOCATION_PERMISSION_ASKED';
  static const isLoggedIn = 'IS_LOGGED_IN';
  static const user = "USER";
  static const remainingTime = "REMAINING_TIME";
  static const playerId = "PLAYER_ID";
  static const appleId = "APPLE_ID";
  static const appleName = "APPLE_NAME";
  static const appleEmail = "APPLE_EMAIL";
}

/* Ride Status */
abstract class RideStatus {
  static const String upcoming = 'upcoming';
  static const String accepted = 'accepted';
  static const String arriving = 'arriving';
  static const String arrived = 'arrived';
  static const String inProgress = 'in_progress';
  static const String reached = 'reached';
  static const String canceled = 'canceled';
  static const String driverCanceled = 'driver_canceled';
  static const String completed = 'completed';
  static const String canceledByRider = 'rider';
  static const String canceledAuto = 'completed';
  static const String newRideRequested = 'new_ride_requested';
  static const String driverOutOfRoute = 'driver_invalid_route';
  static const String stopsUpdated = 'ride_stops_updated';
  static const String destinationUpdated = 'ride_destination_updated';
  static const String newRideAfterDriverCanceled = 'new_ride_after_driver_canceled';
  static const String driverStartedWaitingTime = 'wait_time_start_';
}

/* Firestore Collections */
abstract class FirestoreCollections {
  static const chatCollection = "messages";
}

abstract class NotificationTypes {
  static const rideAlertNotification = 'ride_alert';
  static const rideStartNotification = 'ride_start';
  static const rideNearStartNotification = 'ride_near_start';
  static const chatMsgNotification = 'chat_msg';
  static const inboxMsgNotification = 'new_inbox';
  static const careMsgNotification = 'care_msg';
  static const notificationMsgNotification = 'notification_msg';
  static const newCareRelatedMessage = 'carecomment';
  static const newIssueRelatedMessage = 'complaintcomment';
  static const newOpportunityAccepted = 'opportunity_accepted';
}

abstract class Assets {
  static const appLogoWhite = "assets/logo-white.png";
  static const appLogoBlack = "assets/logo-black.png";
  static const driverSearching = "assets/images/SearchingGif.gif";
  static const googleIcon = "assets/images/ic_google.png";
  static const facebookIcon = "assets/images/ic_facebook.png";
  static const microsoftIcon = "assets/images/ic_microsoft.png";

  static const appleIcon = "assets/images/ic_apple.png";
  static const appleIconWhite = "assets/images/apple_icon_white.png";
  static const appleIconBlack = "assets/images/apple_icon_black.png";
  static const profilePlaceholder = "assets/placeholders/profile.png";
  static const destinationIcon = 'assets/images/ic_des_pin.png';

  static const stopMarker = 'assets/images/ic_source_pin2.png';
  static const sourceIcon = 'assets/images/ic_source_pin.png';
  static const driverIcon = 'assets/images/driver-icon.png';
  static const riderWithDriverIcon = 'assets/images/rider_with_driver.png';





}

abstract class AppColors {
  static const lightThemePrimaryColor = Color(0xffff9800);
  static const lightThemeSecondaryColor = Color(0xff009688);

  static const darkThemePrimaryColor = Color(0xff009688);
  static const darkThemeSecondaryColor = Color(0xffff9800);

  static Color primaryBlackColor = Colors.black;
  static Color primaryWhiteColor = Colors.white;
  static Color primaryTextFieldColor = Colors.grey;

  static Color textfieldBorderBlackColor = Colors.black;

  static Color polylineColor = Colors.blue;

  static Color greenColor = Colors.green;
  static Color redColor = Colors.red;
    static Color blackColor(BuildContext context) => Theme.of(context).colorScheme.tertiary;
    static Color whiteColor(BuildContext context) => Theme.of(context).colorScheme.onTertiary;
    static Color primaryColor(BuildContext context){
  return Theme.of(context).colorScheme.primary;
}

}

abstract class Gender {
  static const select = 'select';
  static const male = 'male';
  static const female = 'female';
  static const preferNotToSay = 'prefer not to say';
  static const other = 'other';
}

abstract class Status {
  static String pending = 'pending';
  static String closed = 'closed';
}

abstract class UserType {
  static const rider = 'rider';
  static const driver = 'driver';
  static const admin = 'admin';
}

abstract class Layout {
  static const scaffoldBodyPadding = 15.0;
  static const formFieldsSeparator = SizedBox(height: 10);
}

abstract class AdType {
  static const dashboard = "dashboard";
  static const searchingRide = "driverfinding";
  static const rideReview = "review";
}

abstract class PaymentType {
  static const wallet = "wallet";
  static const rideBooking = "ride_booking";
  static const rideWaitingAndTip = "ride_waiting_tip_charges";
  static const rideStopChange = "ride_stop_change";
  static const rideDestinationChange = "ride_destination_change";
}

abstract class AppCred{

static const mapBoxPublicTokenKey = 'pk.eyJ1Ijoicm9vby1hdSIsImEiOiJjbTVxZWVnbzIwYnI4MmpxN3JwcXNqNmh3In0.NDxJ9Dy56Dh2fuMmLZXqtA';



}
