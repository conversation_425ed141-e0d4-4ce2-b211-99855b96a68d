import 'dart:math';

import 'package:rider/app_exports.dart';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/scheduler.dart';

import '../../main.dart';
import 'Extensions/loader.dart';

Widget dotIndicator(list, i) {
  return SizedBox(
    height: 16,
    child: Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(
        list.length,
        (ind) {
          return Container(
            height: 8,
            width: 8,
            margin: const EdgeInsets.all(4),
            decoration: BoxDecoration(
                color: i == ind ? Colors.white : Colors.grey.withOpacity(0.5),
                borderRadius: BorderRadius.circular(10)),
          );
        },
      ),
    ),
  );
}

InputDecoration inputDecoration(BuildContext context,
    {String? label, Widget? prefixIcon, Widget? suffixIcon}) {
  return InputDecoration(
      prefixIcon: prefixIcon,
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(10),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(10),
      ),
      disabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(10),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(10),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(10),
      ),
      alignLabelWithHint: true,
      filled: true,
      isDense: true,
      labelText: label ?? "",
      labelStyle: primaryTextStyle(),
      suffixIcon: suffixIcon,
      counterText: '');
}

extension BooleanExtensions on bool? {
  /// Validate given bool is not null and returns given value if null.
  bool validate({bool value = false}) => this ?? value;
}

EdgeInsets dynamicAppButtonPadding(BuildContext context) {
  return const EdgeInsets.symmetric(vertical: 14, horizontal: 16);
}

Widget inkWellWidget({Function()? onTap, required Widget child}) {
  return InkWell(
      onTap: onTap,
      highlightColor: Colors.transparent,
      hoverColor: Colors.transparent,
      splashColor: Colors.transparent,
      child: child);
}

bool get isRTL => false;

Widget commonCachedNetworkImage(
  String? url, {
  double? height,
  double? width,
  BoxFit? fit,
  AlignmentGeometry? alignment,
  bool usePlaceholderIfUrlEmpty = true,
  double? radius,
  bool? circular,
}) {
  if (url != null && url.isEmpty) {
    return placeHolderWidget(
      height: height,
      width: width,
      fit: fit,
      alignment: alignment,
      radius: radius,
      circular: circular,
    );
  } else if (url.validate().startsWith('http')) {
    return CachedNetworkImage(
      imageUrl: url!,
      height: height,
      width: width,
      fit: fit,
      alignment: alignment as Alignment? ?? Alignment.center,
      errorWidget: (_, s, d) {
        return placeHolderWidget(
            height: height,
            width: width,
            fit: fit,
            alignment: alignment,
            radius: radius);
      },
      placeholder: (_, s) {
        if (!usePlaceholderIfUrlEmpty) return const SizedBox();
        return placeHolderWidget(
          height: height,
          width: width,
          fit: fit,
          alignment: alignment,
          radius: radius,
          circular: circular,
        );
      },
    );
  } else {
    return Image.network(url!,
        height: height,
        width: width,
        fit: fit,
        alignment: alignment ?? Alignment.center);
  }
}

Widget placeHolderWidget({
  double? height,
  double? width,
  BoxFit? fit,
  AlignmentGeometry? alignment,
  double? radius,
  bool? circular,
}) {
  return Container(
    height: height,
    width: width,
    decoration: BoxDecoration(
      shape: circular != null ? BoxShape.circle : BoxShape.rectangle,
      image: DecorationImage(
        image: const AssetImage(
          'images/placeholder.jpg',
        ),
        fit: fit ?? BoxFit.cover,
        alignment: alignment ?? Alignment.center,
      ),
    ),
  );
  // return Image.asset(
  //   'images/placeholder.jpg',
  //   height: height,
  //   width: width,
  //   fit: fit ?? BoxFit.cover,
  //   alignment: alignment ?? Alignment.center,
  // );
}

tabContainer({required List tabs, TabController? controller}) {
  return Container(
    height: 40,
    margin: const EdgeInsets.only(right: 16, left: 16, top: 16),
    decoration: const BoxDecoration(
        // borderRadius: appRadius,
        ),
    child: TabBar(
      controller: controller,
      indicator: const BoxDecoration(
          // borderRadius: appRadius,
          ),
      labelColor: Theme.of(navigatorKey.currentContext!).primaryColor,
      unselectedLabelColor:
          Theme.of(navigatorKey.currentContext!).brightness == Brightness.dark
              ? Colors.white
              : Colors.black,
      labelStyle: boldTextStyle(
        size: 14,
      ),
      tabs: tabs.map((e) {
        return Tab(
          child: Text(
            HelperMethods.firstCharUppercase(e),
            style: const TextStyle(
              fontSize: 12,
            ),
          ),
        );
      }).toList(),
    ),
  );
}

List<BoxShadow> defaultBoxShadow({
  Color? shadowColor,
  double? blurRadius,
  double? spreadRadius,
  Offset offset = const Offset(0.0, 0.0),
}) {
  return [
    BoxShadow(
      color: shadowColor ?? Colors.grey.withOpacity(0.2),
      blurRadius: blurRadius ?? 4.0,
      spreadRadius: spreadRadius ?? 1.0,
      offset: offset,
    )
  ];
}

/// Hide soft keyboard
void hideKeyboard() =>
    FocusScope.of(navigatorKey.currentContext!).requestFocus(FocusNode());

const double degrees2Radians = pi / 180.0;

double radians(double degrees) => degrees * degrees2Radians;

const default_Language = 'en';

Future<bool> isNetworkAvailable() async {
  var connectivityResult = await Connectivity().checkConnectivity();
  return connectivityResult != ConnectivityResult.none;
}

Widget loaderWidget() {
  return Container(
    color: Colors.transparent,
    child: Center(
      child: Container(
        padding: const EdgeInsets.all(10),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
                color: Colors.grey.withOpacity(0.4),
                blurRadius: 10,
                spreadRadius: 0,
                offset: const Offset(0.0, 0.0)),
          ],
        ),
        width: 50,
        height: 50,
        child: const CircularProgressIndicator(strokeWidth: 3),
      ),
    ),
  );
}

void afterBuildCreated(Function()? onCreated) {
  makeNullable(SchedulerBinding.instance)!
      .addPostFrameCallback((_) => onCreated?.call());
}

T? makeNullable<T>(T? value) => value;

String printDate(String date) {
  try {
    return DateTime.parse(date).toString().split(".000")[0];
  } catch (e) {
    return "Invalid date";
  }
}

Widget emptyWidget({required String emptyDataMsg}) {
  return Column(
    mainAxisAlignment: MainAxisAlignment.center,
    children: [
      // Center(
      //     child: Image.asset(
      //   // 'images/no_data.png',
      //   Globals.isDarkModeOn
      //       ? 'assets/images/no_data_white.png'
      //       : 'assets/images/no_data_black.png',
      //   width: 100,
      // )),

      const Center(
          child: Icon(
        Icons.error_outline,
        size: 80,
      )),
      const SizedBox(height: 16),
      Center(
        child: Text(
          emptyDataMsg,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
          ),
        ),
      )
    ],
  );
}

Future<bool> saveOneSignalPlayerId() async {
  try {
    // return await OneSignal.shared.getDeviceState().then((value) async {
    //   if (value!.userId.validate().isNotEmpty) {
    //     return Globals.sharedPrefs.setString(PLAYER_ID, value.userId.validate());
    //   } else {
    //     return false;
    //   }
    // });
    return true;
  } catch (e) {
    return false;
  }
}

String statusTypeIcon({String? type}) {
  String icon = 'images/icons/ic_new_ride_requested.png';
  if (type == RideStatus.newRideRequested) {
    icon = 'images/icons/ic_new_ride_requested.png';
  } else if (type == RideStatus.accepted) {
    icon = 'images/icons/ic_accepted.png';
  } else if (type == RideStatus.arriving) {
    icon = 'images/icons/ic_arriving.png';
  } else if (type == RideStatus.arrived) {
    icon = 'images/icons/ic_arrived.png';
  } else if (type == RideStatus.inProgress) {
    icon = 'images/icons/in_progress.png';
  } else if (type == RideStatus.canceled) {
    icon = 'images/icons/ic_canceled.png';
  } else if (type == RideStatus.completed) {
    icon = 'images/icons/ic_completed.png';
  }
  return icon;
}

Widget scheduleOptionWidget(
    BuildContext context, bool isSelected, String imagePath, String title) {
  return Container(
    padding: const EdgeInsets.all(16),
    decoration: BoxDecoration(
      border: Border.all(),
    ),
    child: Row(
      children: [
        ImageIcon(AssetImage(imagePath),
            size: 20, color: isSelected ? Colors.grey : Colors.grey),
        const SizedBox(width: 16),
        Text(title, style: boldTextStyle()),
      ],
    ),
  );
}

Widget totalCount({
  required String title,
  required num value,
  bool isTotal = false,
}) {
  if (value <= 0) {
    return const SizedBox();
  }

  return Padding(
    padding: const EdgeInsets.only(bottom: 3),
    child: Row(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: Text(title,
              style: isTotal == true ? boldTextStyle() : primaryTextStyle()),
        ),
        Expanded(
          child: Text('${Constants.currencySymbol} $value',
              textAlign: TextAlign.end,
              style: isTotal == true ? boldTextStyle() : primaryTextStyle()),
        ),
      ],
    ),
  );
}

/// Handle error and loading widget when using FutureBuilder or StreamBuilder
Widget snapWidgetHelper<T>(
  AsyncSnapshot<T> snap, {
  Widget? errorWidget,
  Widget? loadingWidget,
  String? defaultErrorMessage,
  @Deprecated('Do not use this') bool checkHasData = false,
  Widget Function(String)? errorBuilder,
}) {
  if (snap.hasError) {
    log(snap.error);
    if (errorBuilder != null) {
      return errorBuilder.call(defaultErrorMessage ?? snap.error.toString());
    }
    return Center(
      child: errorWidget ??
          Text(
            defaultErrorMessage ?? snap.error.toString(),
            style: primaryTextStyle(),
          ),
    );
  } else if (!snap.hasData) {
    return loadingWidget ?? const Loader();
  } else {
    return const SizedBox();
  }
}

String statusName({String? status}) {
  if (status == RideStatus.newRideRequested) {
    status = Globals.language.newRideRequested;
  } else if (status == RideStatus.accepted) {
    status = Globals.language.accepted;
  } else if (status == RideStatus.arriving) {
    status = Globals.language.arriving;
  } else if (status == RideStatus.arrived) {
    status = Globals.language.arrived;
  } else if (status == RideStatus.inProgress) {
    status = Globals.language.inProgress;
  } else if (status == RideStatus.canceled) {
    status = Globals.language.cancelled;
  } else if (status == RideStatus.completed) {
    status = Globals.language.completed;
  }
  return status!;
}

String paymentStatus(String paymentStatus) {
  if (paymentStatus.toLowerCase() == "pending") {
    return Globals.language.pending;
  } else if (paymentStatus.toLowerCase() == "failed") {
    return Globals.language.failed;
  } else if (paymentStatus.toLowerCase() == "paid") {
    return Globals.language.paid;
  } else if (paymentStatus.toLowerCase() == "cash") {
    return Globals.language.cash;
  } else if (paymentStatus.toLowerCase() == "wallet") {
    return Globals.language.wallet;
  } else if (paymentStatus.toLowerCase() == "pre_auth" ||
      paymentStatus.toLowerCase().startsWith('pre-auth')) {
    return Globals.language.preAuthorizedPayment;
  }
  return Globals.language.pending;
}

String changeStatusText(String? status) {
  if (status == null) {
    return '';
  }
  status = status.trim().toLowerCase();
  if (status == RideStatus.completed) {
    return Globals.language.completed;
  } else if (status == RideStatus.canceled) {
    return Globals.language.cancelled;
  } else if (status == "pending") {
    return Globals.language.pending;
  } else if (status == RideStatus.upcoming) {
    return Globals.language.upcoming;
  } else if (status == "closed") {
    return 'Closed';
  }
  return '';
}

String changeGender(String? name) {
  if (name == Gender.male) {
    return Globals.language.male;
  } else if (name == Gender.female) {
    return Globals.language.female;
  } else if (name == Gender.other) {
    return Globals.language.other;
  }
  return '';
}

bool isDarkMode() {
  int hour = DateTime.now().hour;
  return [
    18,
    19,
    20,
    21,
    22,
    23,
    0,
    1,
    2,
    3,
    4,
    5,
    6,
  ].contains(hour);
}

bool checkIfDarkModeIsOn(BuildContext context) {
  Globals.isDarkModeOn = (Theme.of(context).brightness == Brightness.dark);
  return Globals.isDarkModeOn;
}

DateTime dateAfter90Days(DateTime todayDate, int extendedDay) {
  return todayDate.add(
    Duration(
      days: extendedDay,
    ),
  );
}

String dateToString(DateTime date) {
  String day = (date.day < 10) ? '0${date.day}' : date.day.toString();
  String month = (date.month < 10) ? '0${date.month}' : date.month.toString();
  return '$day/$month/${date.year}';
}

String dateToInfoString(DateTime datePassed) {
  DateTime date = datePassed.toLocal();
  String day = (date.day < 10) ? '0${date.day}' : date.day.toString();
  String month = (date.month < 10) ? '0${date.month}' : date.month.toString();

  String minutes =
      (date.minute < 10) ? '0${date.minute}' : date.minute.toString();

  return '$day-$month-${date.year} ${date.hour}:$minutes ${date.hour >= 12 ? 'PM' : 'AM'}';
}

String timeToString(TimeOfDay time) {
  String hour = (time.hourOfPeriod < 10)
      ? '0${time.hourOfPeriod}'
      : time.hourOfPeriod.toString();
  String minutes =
      (time.minute < 10) ? '0${time.minute}' : time.minute.toString();
  return '$hour:$minutes ${time.period.name}';
}

void showErrorToast() {
  toast(Globals.language.errorMsg);
}

bool isToday(DateTime dateTime) {
  DateTime today = DateTime.now();
  return today.year == dateTime.year &&
      today.month == dateTime.month &&
      today.day == dateTime.day;
}

String? getArrivalTime(int? seconds) {
  if (seconds == null) {
    return null;
  }
  Duration duration = Duration(seconds: seconds);

  String twoDigits(int n) => n.toString().padLeft(2, "");
  String twoDigitMinutes = twoDigits(duration.inMinutes.remainder(60));

  return "${twoDigits(duration.inHours)} hours and $twoDigitMinutes minutes";

  // String text = duration.inMinutes.toString() + ' minutes';
  // if (duration.inMinutes >= 60) {
  //   text = duration.inHours.toString() + ' hours';
  // }
  // return text;
}

void handleError(dynamic error, StackTrace stackTrace) {
  if (kDebugMode) {
    if (error is TypeError) {
      var data = stackTrace.toString();
      data = data.substring(data.indexOf('#0') + 3, data.indexOf('#1'));
      data = data.substring(data.lastIndexOf('/') + 1);
      showAppDialog(
        dialogType: AppDialogType.error,
        title: 'Received wrong data from the API.\n\n$error\n\n$data',
        onAccept: () {
          Navigator.of(navigatorKey.currentContext!).pop();
        },
      );
    }
  }
}

// A function that checks if a character is an alphabet
bool isAlphabet(String char) {
  return char.codeUnitAt(0) >= 65 && char.codeUnitAt(0) <= 90 ||
      char.codeUnitAt(0) >= 97 && char.codeUnitAt(0) <= 122;
}

// A function that checks if a character is a number
bool isNumber(String char) {
  return char.codeUnitAt(0) >= 48 && char.codeUnitAt(0) <= 57;
}

bool validateBusinessLicense(String str) {
  // Rule 1: string must start with an alphabet
  if (!isAlphabet(str[0])) {
    return false;
  }
  // Rule 2: no special characters are allowed
  // Rule 3: string must have numbers
  // Rule 4: on occurrence of a number the following string must be numbers
  bool hasNumber = false;
  bool hasSpecial = false;
  for (int i = 1; i < str.length; i++) {
    String char = str[i];
    if (isNumber(char)) {
      hasNumber = true;
    } else if (isAlphabet(char)) {
      if (hasNumber) {
        // Violates rule 4
        return false;
      }
    } else {
      // Violates rule 2
      hasSpecial = true;
      break;
    }
  }
  return hasNumber && !hasSpecial;
}

bool isValidImageSize(int sizeInBytes) {
  const megabyte = 1024 * 1024;
  num sizeInMB = num.parse((sizeInBytes / megabyte).toStringAsFixed(2));
  return sizeInMB <= 2;
}
