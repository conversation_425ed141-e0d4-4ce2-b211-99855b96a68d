import 'package:rider/features/care/models/care_details_response_model.dart';
import 'package:rider/model/PaginationModel.dart';

class CareResponse {
  final bool status;
  final String message;
  final CareDetails data;

  CareResponse({
    required this.status,
    required this.message,
    required this.data,
  });

  factory CareResponse.fromMap(Map<String, dynamic> map) {
    return CareResponse(
      status: map['status'] as bool,
      message: map['message'] as String,
      data: CareDetails.fromMap(map['data'] as Map<String, dynamic>),
    );
  }
}

class CareDetails {
  final int id;
  final int driverId;
  final String subject;
  final String message;
  final int status;
  final String createdBy;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? deletedAt;
  final List<CareComment> carecomment;

  CareDetails({
    required this.id,
    required this.driverId,
    required this.subject,
    required this.message,
    required this.status,
    required this.createdBy,
    required this.createdAt,
    required this.updatedAt,
    this.deletedAt,
    required this.carecomment,
  });

  factory CareDetails.fromMap(Map<String, dynamic> map) {
    return CareDetails(
      id: map['id'] as int,
      driverId: map['driver_id'] as int,
      subject: map['subject'] as String,
      message: map['message'] as String,
      status: map['status'] as int,
      createdBy: map['created_by'] as String,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
      deletedAt: map['deleted_at'] != null
          ? DateTime.parse(map['deleted_at'] as String)
          : null,
      carecomment: (map['carecomment'] as List)
          .map((comment) => CareComment.fromMap(comment))
          .toList(),
    );
  }
}

class Care {
  PaginationModel pagination;
  List<CareData> data;
  String? message;
  Care({required this.pagination, required this.data, this.message});

  factory Care.fromJson(Map<String, dynamic> json) {
    List<CareData> data = [];
    for (var i = 0; i < json['data'].length; i++) {
      data.add(
        CareData.fromJson(
          json['data'][i],
        ),
      );
    }
    return Care(
        pagination: PaginationModel.fromJson(
          json['pagination'],
        ),
        data: data,
        message: json['message']);
  }
}

class CareData {
  int id;
  String subject;
  String message;
  String status;
  bool? isFromAdmin;
  String? createdAt;
  String? updatedAt;
  CareData({
    required this.id,
    required this.message,
    required this.status,
    required this.subject,
    required this.isFromAdmin,
    required this.createdAt,
    required this.updatedAt,
  });

  factory CareData.fromJson(Map<String, dynamic> json) {
    return CareData(
      id: json['id'] as int,
      subject: json['subject'] as String,
      status: (json['status'] ?? "") as String,
      message: json['message'] as String,
      isFromAdmin: json['isFromAdmin'] as bool?,
      createdAt: json['created_at'] as String?,
      updatedAt: json['updated_at'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'subject': subject,
      'message': message,
    };
  }
}
