import 'package:rider/model/PaginationModel.dart';

class AdvertisementResponse {
  PaginationModel pagination;
  List<Advertisement> data;
  String? message;
  AdvertisementResponse(
      {required this.pagination, required this.data, this.message});

  factory AdvertisementResponse.fromJson(Map<String, dynamic> json) {
    List<Advertisement> data = [];
    for (var i = 0; i < json['data'].length; i++) {
      data.add(
        Advertisement.fromJson(
          json['data'][i],
        ),
      );
    }
    return AdvertisementResponse(
        pagination: PaginationModel.fromJson(
          json['pagination'],
        ),
        data: data,
        message: json['message']);
  }
}

class Advertisement {
  int id;
  String imageURL;
  String linkURL;
  Advertisement({
    required this.id,
    required this.imageURL,
    required this.linkURL,
  });

  factory Advertisement.fromJson(Map<String, dynamic> json) {
    return Advertisement(
      id: json['id'] as int,
      imageURL: json['imageURL'] as String,
      linkURL: json['linkURL'] as String,
    );
  }
}
