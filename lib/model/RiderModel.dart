import 'package:rider/features/ride_flow/screen/manage_ride_stops_logic.dart';

import '../model/CouponData.dart';
import '../model/ExtraChargeRequestModel.dart';

class RiderModel {
  int? id;
  int? riderId;
  num? tips;
  int? serviceId;
  String? datetime;
  int? isSchedule;
  int? rideAttempt;
  String? otp;
  num? totalAmount;
  num? subtotal;
  num? extraChargesAmount;
  num? peak_fare;
  num? stripe_transaction_charges;
  num? airportCharges;
  num? platformFee;

  CouponData? couponData;
  num? tax;
  int? driverId;
  String? driverName;
  String? riderName;
  String? driverProfileImage;
  String? riderProfileImage;
  String? startLatitude;
  String? startLongitude;
  String? startAddress;
  String? endLatitude;
  String? endLongitude;
  String? endAddress;
  String? distanceUnit;
  String? startTime;
  String? endTime;
  num? distance;
  num? duration;
  int? seatCount;
  String? reason;
  String? status;
  num? baseFare;
  num? rideFare;
  num? minimumFare;
  num? perDistance;
  num? perMinuteDrive;
  num? perMinuteWaiting;
  num? waitingTime;
  num? waitingTimeLimit;
  num? waitingTimeCharges;
  num? cancelationCharges;
  String? cancelBy;
  int? paymentId;
  String? paymentType;
  String? paymentStatus;
  List<ExtraChargeRequestModel>? extraCharges;
  num? couponDiscount;
  int? couponCode;
  int? isRiderRated;
  int? isDriverRated;
  int? maxTimeForFindDriverForRideRequest;
  String? createdAt;
  String? updatedAt;
  num? perMinuteWaitingCharge;
  num? perMinuteDriveCharge;
  num? perDistanceCharge;
  String? driverContactNumber;
  String? riderContactNumber;
  OtherRiderData? otherRiderData;
  List<RideStop>? stops;
  List<RideDestination>? destinations;
  bool? isPoolingRide;
  bool? isBusinessRide;

  RiderModel({
    this.id,
    this.riderId,
    this.serviceId,
    this.datetime,
    this.isSchedule,
    this.rideAttempt,
    this.otp,
    this.totalAmount,
    this.subtotal,
    this.extraChargesAmount,
    this.peak_fare,
    this.stripe_transaction_charges,
    this.driverId,
    this.driverName,
    this.riderName,
    this.driverProfileImage,
    this.riderProfileImage,
    this.startLatitude,
    this.startLongitude,
    this.startAddress,
    this.endLatitude,
    this.endLongitude,
    this.endAddress,
    this.distanceUnit,
    this.startTime,
    this.endTime,
    this.tips,
    this.distance,
    this.duration,
    this.seatCount,
    this.reason,
    this.status,
    this.couponData,
    this.baseFare,
    this.minimumFare,
    this.perDistance,
    this.perMinuteDrive,
    this.perMinuteWaiting,
    this.waitingTime,
    this.waitingTimeLimit,
    this.waitingTimeCharges,
    this.cancelationCharges,
    this.cancelBy,
    this.paymentId,
    this.paymentType,
    this.paymentStatus,
    this.extraCharges,
    this.couponDiscount,
    this.couponCode,
    this.isRiderRated,
    this.isDriverRated,
    this.maxTimeForFindDriverForRideRequest,
    this.createdAt,
    this.updatedAt,
    this.perDistanceCharge,
    this.perMinuteDriveCharge,
    this.perMinuteWaitingCharge,
    this.driverContactNumber,
    this.riderContactNumber,
    this.otherRiderData,
    this.tax,
    this.stops,
    this.destinations,
    this.isPoolingRide,
    this.isBusinessRide,
    this.airportCharges,
    this.platformFee,
    this.rideFare,
  });

  RiderModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    riderId = json['rider_id'];
    serviceId = json['service_id'];
    // couponData:
    // json['coupon_data'] != null
    //     ? CouponData.fromJson(json['coupon_data'])
    //     : [];
    datetime = json['datetime'];
    isSchedule = json['is_schedule'];
    rideAttempt = json['ride_attempt'];
    otp = json['otp'];
    tips = json['tips'];
    totalAmount = json['total_amount'];
    subtotal = json['subtotal'];
    extraChargesAmount = json['extra_charges_amount'];
    peak_fare = json['peak_fare'];
    stripe_transaction_charges = json['stripe_transaction_charges'];
    driverId = json['driver_id'];
    driverName = json['driver_name'];
    riderName = json['rider_name'];
    driverProfileImage = json['driver_profile_image'];
    riderProfileImage = json['rider_profile_image'];
    startLatitude = json['start_latitude'];
    startLongitude = json['start_longitude'];
    startAddress = json['start_address'];
    endLatitude = json['end_latitude'];
    endLongitude = json['end_longitude'];
    endAddress = json['end_address'];
    distanceUnit = json['distance_unit'];
    startTime = json['start_time'];
    endTime = json['end_time'];
    distance = json['distance'];
    duration = json['duration'];
    seatCount = json['seat_count'];
    reason = json['reason'];
    status = json['status'];
    baseFare = json['base_fare'];
    tax = json['tax'];
    minimumFare = json['minimum_fare'];
    perDistance = json['per_distance'];
    perMinuteDrive = json['per_minute_drive'];
    perMinuteWaiting = json['per_minute_waiting'];
    waitingTime = json['waiting_time'];
    waitingTimeLimit = json['waiting_time_limit'];
    waitingTimeCharges = json['waiting_time_charges'];
    cancelationCharges = json['cancelation_charges'];
    cancelBy = json['cancel_by'];
    paymentId = json['payment_id'];
    paymentType = json['payment_type'];
    paymentStatus = json['payment_status'];
    riderContactNumber = json['rider_contact_number'];
    driverContactNumber = json['driver_contact_number'];
    if (json['extra_charges'] != null) {
      extraCharges = <ExtraChargeRequestModel>[];
      json['extra_charges'].forEach((v) {
        extraCharges!.add(ExtraChargeRequestModel.fromJson(v));
      });
    }
    couponDiscount = json['coupon_discount'];
    couponCode = json['coupon_code'];

    isRiderRated = json['is_rider_rated'];
    isDriverRated = json['is_driver_rated'];
    maxTimeForFindDriverForRideRequest =
        json['max_time_for_find_driver_for_ride_request'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    perDistanceCharge = json['per_distance_charge'];
    perMinuteDriveCharge = json['per_minute_drive_charge'];
    perMinuteWaitingCharge = json['per_minute_waiting_charge'];
    otherRiderData = json['other_rider_data'] != null
        ? OtherRiderData.fromJson(json['other_rider_data'])
        : null;

    stops = json['stops'] != null
        ? (json['stops'] as List).map((i) => RideStop.fromJson(i)).toList()
        : null;

    destinations = json['destinationPlaces'] != null
        ? (json['destinationPlaces'] as List)
            .map((i) => RideDestination.fromJson(i))
            .toList()
        : null;

    isPoolingRide = json['is_pool'];
    isBusinessRide = json['is_business_ride'];
    airportCharges = json['airport_charges'];
    platformFee = json['platform_fee'];
    rideFare = json['combine_fare'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['rider_id'] = riderId;
    data['service_id'] = serviceId;
    data['datetime'] = datetime;
    data['is_schedule'] = isSchedule;
    data['ride_attempt'] = rideAttempt;
    data['otp'] = otp;
    data['total_amount'] = totalAmount;
    data['subtotal'] = subtotal;
    data['extra_charges_amount'] = extraChargesAmount;
    data['peak_fare'] = peak_fare;
    data['stripe_transaction_charges'] = stripe_transaction_charges;
    data['driver_id'] = driverId;
    data['driver_name'] = driverName;
    data['rider_name'] = riderName;
    data['driver_profile_image'] = driverProfileImage;
    data['rider_profile_image'] = riderProfileImage;
    data['start_latitude'] = startLatitude;
    data['start_longitude'] = startLongitude;
    data['start_address'] = startAddress;
    data['end_latitude'] = endLatitude;
    data['end_longitude'] = endLongitude;
    data['end_address'] = endAddress;
    data['distance_unit'] = distanceUnit;
    data['start_time'] = startTime;
    data['end_time'] = endTime;
    data['distance'] = distance;
    data['duration'] = duration;
    data['seat_count'] = seatCount;
    data['reason'] = reason;
    data['status'] = status;
    data['base_fare'] = baseFare;
    data['minimum_fare'] = minimumFare;
    data['per_distance'] = perDistance;
    data['per_minute_drive'] = perMinuteDrive;
    data['per_minute_waiting'] = perMinuteWaiting;
    data['waiting_time'] = waitingTime;
    data['waiting_time_limit'] = waitingTimeLimit;
    data['waiting_time_charges'] = waitingTimeCharges;
    data['cancelation_charges'] = cancelationCharges;
    data['cancel_by'] = cancelBy;
    data['payment_id'] = paymentId;
    data['payment_type'] = paymentType;
    data['payment_status'] = paymentStatus;
    if (extraCharges != null) {
      data['extra_charges'] = extraCharges!.map((v) => v.toJson()).toList();
    }
    data['coupon_discount'] = couponDiscount;
    data['coupon_code'] = couponCode;
    if (couponData != null) {
      data['coupon_data'] = couponData!.toJson();
    }
    data['is_rider_rated'] = isRiderRated;
    data['is_driver_rated'] = isDriverRated;
    data['max_time_for_find_driver_for_ride_request'] =
        maxTimeForFindDriverForRideRequest;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    data['per_distance_charge'] = perDistanceCharge;
    data['per_minute_drive_charge'] = perMinuteDriveCharge;
    data['per_minute_waiting_charge'] = perMinuteWaitingCharge;
    data['rider_contact_number'] = perMinuteDriveCharge;
    data['driver_contact_number'] = perMinuteWaitingCharge;
    if (otherRiderData != null) {
      data['other_rider_data'] = otherRiderData!.toJson();
    }
    return data;
  }
}

class OtherRiderData {
  String? name;
  String? conatctNumber;

  OtherRiderData({this.name, this.conatctNumber});

  OtherRiderData.fromJson(Map<String, dynamic> json) {
    name = json['name'];
    conatctNumber = json['contact_number'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['name'] = name;
    data['contact_number'] = conatctNumber;
    return data;
  }
}
