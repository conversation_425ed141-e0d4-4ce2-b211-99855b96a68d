import 'PaginationModel.dart';

class VehicleTypeResponse {
  PaginationModel pagination;
  List<VehicleType> data;
  String? message;

  VehicleTypeResponse({
    required this.pagination,
    required this.data,
    this.message,
  });

  factory VehicleTypeResponse.fromJson(Map<String, dynamic> json) {
    List<VehicleType> vehicleData = [];
    for (var i = 0; i < json['data'].length; i++) {
      vehicleData.add(
        VehicleType.fromJson(
          json['data'][i],
        ),
      );
    }
    return VehicleTypeResponse(
        pagination: PaginationModel.fromJson(
          json['pagination'],
        ),
        data: vehicleData,
        message: json['message']);
  }
}

class VehicleType {
  int id;
  String service_image;
  String name;

  VehicleType({
    required this.id,
    required this.service_image,
    required this.name,
  });

  factory VehicleType.fromJson(Map<String, dynamic> json) {
    return VehicleType(
      id: json['id'],
      service_image: json['service_image'],
      name: json['name'],
    );
  }
}
