
class MicrosoftAuthModel {
  final String userPrincipalName;
  final String id;
  final String displayName;
  final String surname;
  final String givenName;
  final String preferredLanguage;
  final String mail;
  final String? mobilePhone;
  final String? jobTitle;
  final String? officeLocation;
  final List<String> businessPhones;

  MicrosoftAuthModel({
    required this.userPrincipalName,
    required this.id,
    required this.displayName,
    required this.surname,
    required this.givenName,
    required this.preferredLanguage,
    required this.mail,
    this.mobilePhone,
    this.jobTitle,
    this.officeLocation,
    required this.businessPhones,
  });

  // From JSON
  factory MicrosoftAuthModel.fromJson(Map<String, dynamic> json) {
    return MicrosoftAuthModel(
      userPrincipalName: json['userPrincipalName'],
      id: json['id'],
      displayName: json['displayName'],
      surname: json['surname'],
      givenName: json['givenName'],
      preferredLanguage: json['preferredLanguage'],
      mail: json['mail'],
      mobilePhone: json['mobilePhone'],
      jobTitle: json['jobTitle'],
      officeLocation: json['officeLocation'],
      businessPhones: List<String>.from(json['businessPhones']),
    );
  }

  // To JSON
  Map<String, dynamic> toJson() {
    return {
      'userPrincipalName': userPrincipalName,
      'id': id,
      'displayName': displayName,
      'surname': surname,
      'givenName': givenName,
      'preferredLanguage': preferredLanguage,
      'mail': mail,
      'mobilePhone': mobilePhone,
      'jobTitle': jobTitle,
      'officeLocation': officeLocation,
      'businessPhones': businessPhones,
    };
  }
}
