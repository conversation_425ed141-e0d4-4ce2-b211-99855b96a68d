import 'PaginationModel.dart';

class ComplaintCommentModel {
  List<ComplaintList>? data;
  PaginationModel? pagination;
  String? message;

  ComplaintCommentModel({this.data, this.pagination, this.message});

  factory ComplaintCommentModel.fromJson(Map<String, dynamic> json) {
    return ComplaintCommentModel(
        data: json['data'] != null
            ? (json['data'] as List)
                .map((i) => ComplaintList.fromJson(i))
                .toList()
            : null,
        pagination: json['pagination'] != null
            ? PaginationModel.fromJson(json['pagination'])
            : null,
        message: json['message']);
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    if (pagination != null) {
      data['pagination'] = pagination!.toJson();
    }
    return data;
  }
}

class ComplaintList {
  String? addedBy;
  String? comment;
  int? complaintId;
  String? createdAt;
  int? id;
  String? status;
  String? updatedAt;
  int? userId;
  String? userName;
  String? userProfileImage;

  ComplaintList({
    this.addedBy,
    this.comment,
    this.complaintId,
    this.createdAt,
    this.id,
    this.status,
    this.updatedAt,
    this.userId,
    this.userName,
    this.userProfileImage,
  });

  factory ComplaintList.fromJson(Map<String, dynamic> json) {
    return ComplaintList(
      addedBy: json['added_by'],
      comment: json['comment'],
      complaintId: json['complaint_id'],
      createdAt: json['created_at'],
      id: json['id'],
      status: json['status'],
      updatedAt: json['updated_at'],
      userId: json['user_id'],
      userName: json['user_name'],
      userProfileImage: json['user_profile_image'],
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['added_by'] = addedBy;
    data['comment'] = comment;
    data['complaint_id'] = complaintId;
    data['created_at'] = createdAt;
    data['id'] = id;
    data['status'] = status;
    data['updated_at'] = updatedAt;
    data['user_id'] = userId;
    data['user_name'] = userName;
    data['user_profile_image'] = userProfileImage;
    return data;
  }
}
