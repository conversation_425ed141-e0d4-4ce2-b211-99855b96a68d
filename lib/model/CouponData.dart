class CouponData {
  String? code;
  String? couponType;
  String? createdAt;
  String? description;
  num? discount;
  String? discountType;
  String? endDate;
  int? id;
  int? maximumDiscount;
  int? minimumAmount;
  int? regionIds;
  List<String>? serviceIds;
  String? startDate;
  int? status;
  String? title;
  String? updatedAt;
  int? usageLimitPerRider;

  CouponData({
    this.code,
    this.couponType,
    this.createdAt,
    this.description,
    this.discount,
    this.discountType,
    this.endDate,
    this.id,
    this.maximumDiscount,
    this.minimumAmount,
    this.regionIds,
    this.serviceIds,
    this.startDate,
    this.status,
    this.title,
    this.updatedAt,
    this.usageLimitPerRider,
  });

  factory CouponData.fromJson(Map<String, dynamic> json) {
    return CouponData(
      code: json['code'],
      couponType: json['coupon_type'],
      createdAt: json['created_at'],
      description: json['description'],
      discount: json['discount'],
      discountType: json['discount_type'],
      endDate: json['end_date'],
      id: json['id'],
      maximumDiscount: json['maximum_discount'],
      minimumAmount: json['minimum_amount'],
      regionIds: json['region_ids'],
      serviceIds: List<String>.from((json['service_ids'] ?? [])),
      startDate: json['start_date'],
      status: json['status'],
      title: json['title'],
      updatedAt: json['updated_at'],
      usageLimitPerRider: json['usage_limit_per_rider'],
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    data['coupon_type'] = couponType;
    data['created_at'] = createdAt;
    data['description'] = description;
    data['discount'] = discount;
    data['discount_type'] = discountType;
    data['end_date'] = endDate;
    data['id'] = id;
    data['maximum_discount'] = maximumDiscount;
    data['minimum_amount'] = minimumAmount;
    data['region_ids'] = regionIds;
    data['service_ids'] = serviceIds;
    data['start_date'] = startDate;
    data['status'] = status;
    data['title'] = title;
    data['updated_at'] = updatedAt;
    data['usage_limit_per_rider'] = usageLimitPerRider;
    return data;
  }
}
