import 'package:rider/app_exports.dart';

import '../../main.dart';

class LanguageDataModel {
  int? id;
  String? name;
  String? languageCode;
  String? fullLanguageCode;
  String? flag;
  String? subTitle;

  LanguageDataModel({
    this.id,
    this.name,
    this.languageCode,
    this.flag,
    this.fullLanguageCode,
    this.subTitle,
  });

  static List<String> languages() {
    List<String> list = [];

    for (var element in localeLanguageList) {
      list.add(element.languageCode.validate());
    }

    return list;
  }

  static List<Locale> languageLocales() {
    List<Locale> list = [];

    for (var element in localeLanguageList) {
      list.add(Locale(element.languageCode.validate(),
          element.fullLanguageCode.validate()));
    }

    return list;
  }
}

LanguageDataModel? getSelectedLanguageModel({String? defaultLanguage}) {
  LanguageDataModel? data = LanguageDataModel(
    name: "English",
    languageCode: "en",
    flag: "us",
    fullLanguageCode: "en_US",
  );

  // for (var element in localeLanguageList) {
  //   // if (element.languageCode ==
  //   //     (Globals.sharedPrefs.getString(SELECTED_LANGUAGE_CODE) ??
  //   //         default_Language)) {
  //   //   data = element;
  //   //   currentLanguage = data!.languageCode!;
  // }

  return data;
}
