import 'package:firebase_auth/firebase_auth.dart';
import 'package:rider/features/ride_flow/screen/manage_ride_stops_logic.dart';

class LoginResponse extends ApiBaseResponse<UserModel> {
  LoginResponse(
      {required super.status, required super.message, required super.data});

  factory LoginResponse.fromJson(Map<String, dynamic> json) {
    return LoginResponse(
      data: (json['data'] != null && json['data']['user'] != null)
          ? UserModel.fromJson(json['data']['user'])
          : null,
      message: json['message'],
      status: json['status'],
    );
  }
}

class UserDetailsResponse extends ApiBaseResponse<UserModel> {
  UserDetailsResponse(
      {required super.status, required super.message, required super.data});

  factory UserDetailsResponse.fromJson(Map<String, dynamic> json) {
    return UserDetailsResponse(
      data: (json['data'] != null) ? UserModel.from<PERSON>son(json['data']) : null,
      message: json['message'],
      status: json['status'],
    );
  }
}

class MobileLoginResponse extends ApiBaseResponse<MobileLoginData> {
  MobileLoginResponse(
      {required super.status, required super.message, required super.data});

  factory MobileLoginResponse.fromJson(Map<String, dynamic> json) {
    return MobileLoginResponse(
      data:
          json['data'] != null ? MobileLoginData.fromJson(json['data']) : null,
      message: json['message'],
      status: json['status'],
    );
  }
}

class MobileLoginData {
  String key;
  String login_screen;
  bool is_user_exists;
  UserModel? user;

  MobileLoginData(
      {required this.key,
      required this.login_screen,
      required this.is_user_exists,
      this.user});

  factory MobileLoginData.fromJson(Map<String, dynamic> json) {
    return MobileLoginData(
        key: json["key"],
        login_screen: json["login_screen"],
        is_user_exists: json["is_user_exists"],
        user: json["user"] == null ? null : UserModel.fromJson(json["user"]));
  }
}

class UserModel {
  var id;
  bool? isNewSignUp;
  bool isProfileComplete;
  String stripeCustomerId;
  String stripeEphemeralKey;
  String? uid;
  String? firstName;
  String? lastName;
  String? email;
  String? contactNumber;
  String? username;
  String? gender;
  String? otherGenderText;
  String? emailVerifiedAt;
  bool? is_verified_request;
  bool? share_otp_during_ride;

  String? address;
  String? login_screen;

  String userType;
  String? playerId;
  String? fleetId;
  String? latitude;
  String? longitude;
  String? lastNotificationSeen;
  String? status;
  int? isOnline;
  String? displayName;
  String? loginType;
  String? timezone;
  String? createdAt;
  String? updatedAt;
  String? apiToken;
  String? profileImage;
  int? isVerifiedDriver;
  UserDetails? userDetails;
  int? regionId;
  int? provinceId;
  String? firestore_id;

  /* otp verification key*/

  String? otpVerificationKey;
  List<KeyMessage>? waitingForApproval;

  UserModel({
    this.id,
    this.isProfileComplete = true,
    this.stripeCustomerId = "",
    this.stripeEphemeralKey = "",
    this.share_otp_during_ride,
    this.firstName,
    this.lastName,
    this.email,
    this.username,
    this.contactNumber,
    this.gender,
    this.emailVerifiedAt,
    this.address,
    required this.userType,
    this.playerId,
    this.fleetId,
    this.latitude,
    this.longitude,
    this.lastNotificationSeen,
    this.status,
    this.isOnline,
    required this.uid,
    required this.firestore_id,
    this.displayName,
    this.loginType,
    this.timezone,
    this.createdAt,
    this.updatedAt,
    this.apiToken,
    this.profileImage,
    this.isVerifiedDriver,
    this.userDetails,
    this.is_verified_request,
    this.otpVerificationKey,
    this.regionId,
    this.provinceId,
    this.waitingForApproval,
    this.login_screen,
    this.otherGenderText,
    this.isNewSignUp,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'],
      firestore_id: json['firestore_id'],
      isProfileComplete: json['is_profile_complete'] ?? true,
      stripeCustomerId: json['stripe_customer_id'] ?? "",
      stripeEphemeralKey: json['stripe_ephemeral_key'] ?? "",
      share_otp_during_ride: json['share_otp_during_ride'],
      firstName: json['first_name'] ?? "",
      lastName: json['last_name'] ?? "",
      email: json['email'],
      username: json['username'],
      login_screen: json['login_screen'],
      contactNumber: json['contact_number'],
      gender: json['gender'],
      otherGenderText: json['other_gender_text'],
      emailVerifiedAt: json['email_verified_at'],
      address: json['address'],
      userType: json['user_type'],
      playerId: json['player_id'],
      fleetId: json['fleet_id'],
      latitude: json['latitude'],
      longitude: json['longitude'],
      lastNotificationSeen: json['last_notification_seen'],
      status: json['status'],
      isOnline: json['is_online'],
      uid: json['uid'],
      displayName: json['display_name'],
      loginType: json['login_type'],
      timezone: json['timezone'],
      createdAt: json['created_at'],
      updatedAt: json['updated_at'],
      apiToken: json['api_token'],
      profileImage: json['profile_image'],
      isVerifiedDriver: json['is_verified_driver'],
      is_verified_request: json['is_verified_request'],
      otpVerificationKey: json['key'],
      regionId: json['region_id'],
      provinceId: json['province_id'],
      waitingForApproval: json['waitingForApproval'] != null
          ? List<KeyMessage>.from(
              json['waitingForApproval'].map((x) => KeyMessage.fromMap(x)))
          : null,
      userDetails: json['user_detail'] == null
          ? null
          : UserDetails.fromJson(
              json['user_detail'],
            ),
      isNewSignUp: json['wizard_show'],
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['login_screen'] = login_screen;
    data['share_otp_during_ride'] = share_otp_during_ride;

    data['first_name'] = firstName;
    data['firestore_id'] = firestore_id;

    data['last_name'] = lastName;
    data['email'] = email;
    data['username'] = username;
    data['contact_number'] = contactNumber;
    data['gender'] = gender;
    data['email_verified_at'] = emailVerifiedAt;
    data['address'] = address;
    data['user_type'] = userType;
    data['player_id'] = playerId;
    data['fleet_id'] = fleetId;
    data['latitude'] = latitude;
    data['longitude'] = longitude;
    data['last_notification_seen'] = lastNotificationSeen;
    data['status'] = status;
    data['is_online'] = isOnline;
    data['uid'] = uid;
    data['display_name'] = displayName;
    data['login_type'] = loginType;
    data['timezone'] = timezone;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    data['api_token'] = apiToken;
    data['profile_image'] = profileImage;
    data['region_id'] = regionId;
    data['province_id'] = provinceId;
    data['is_verified_driver'] = isVerifiedDriver;
    data['stripe_customer_id'] = stripeCustomerId;
    data['stripe_ephemeral_key'] = stripeEphemeralKey;
   
    return data;
  }
}

class Pivot {
  int? modelId;
  String? modelType;
  int? roleId;

  Pivot({this.modelId, this.modelType, this.roleId});

  factory Pivot.fromJson(Map<String, dynamic> json) {
    return Pivot(
      modelId: json['model_id'],
      modelType: json['model_type'],
      roleId: json['role_id'],
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['model_id'] = modelId;
    data['model_type'] = modelType;
    data['role_id'] = roleId;
    return data;
  }
}

class Role {
  String? createdAt;
  String? guardName;
  int? id;
  String? name;
  Pivot? pivot;
  int? status;

  //Object? updated_at;

  Role({
    this.createdAt,
    this.guardName,
    this.id,
    this.name,
    this.pivot,
    this.status,
    /*this.updated_at*/
  });

  factory Role.fromJson(Map<String, dynamic> json) {
    return Role(
      createdAt: json['created_at'],
      guardName: json['guard_name'],
      id: json['id'],
      name: json['name'],
      pivot: json['pivot'] != null ? Pivot.fromJson(json['pivot']) : null,
      status: json['status'],
      //updated_at: json['updated_at'] != null ? Object.fromJson(json['updated_at']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['created_at'] = createdAt;
    data['guard_name'] = guardName;
    data['id'] = id;
    data['name'] = name;
    data['status'] = status;
    if (pivot != null) {
      data['pivot'] = pivot!.toJson();
    }
    /* if (this.updated_at != null) {
            data['updated_at'] = this.updated_at!.toJson();
     }*/
    return data;
  }
}

class UserDetails {
  String? business_name;
  String? business_address1;
  String? business_address2;
  String? business_city;
  String? business_state;
  String? business_postal_code;
  String? business_license_number;

  UserDetails({
    this.business_address1,
    this.business_address2,
    this.business_city,
    this.business_license_number,
    this.business_name,
    this.business_postal_code,
    this.business_state,
  });

  factory UserDetails.fromJson(Map<String, dynamic> json) {
    return UserDetails(
      business_name: json['business_name'],
      business_address1: json['business_address1'],
      business_address2: json['business_address2'],
      business_city: json['business_city'],
      business_state: json['business_state'],
      business_postal_code: json['business_postal_code'],
      business_license_number: json['business_license_number'],
    );
  }
}

class RegisterResponse {
  UserModel? data;
  String? message;
  bool status;

  RegisterResponse({
    this.data,
    this.message,
    required this.status,
  });

  factory RegisterResponse.fromJson(Map<String, dynamic> json) {
    return RegisterResponse(
      data: json['data'] != null ? UserModel.fromJson(json['data']) : null,
      message: json['message'],
      status: json['status'],
    );
  }
}

class VerificationKeyData {
  String key;
  VerificationKeyData({required this.key});

  Map<String, dynamic> toMap() {
    return {
      'key': key,
    };
  }

  factory VerificationKeyData.fromMap(Map<String, dynamic> map) {
    return VerificationKeyData(
      key: map['key'],
    );
  }
}

class SentOtpResponse extends ApiBaseResponse<VerificationKeyData> {
  SentOtpResponse(
      {required super.status, required super.message, required super.data});

  factory SentOtpResponse.fromJson(Map<String, dynamic> json) {
    return SentOtpResponse(
      data: json['data'] != null
          ? VerificationKeyData.fromMap(json['data'])
          : null,
      message: json['message'],
      status: json['status'],
    );
  }
}

class KeyMessage {
  String key;
  String message;
  KeyMessage({required this.key, required this.message});

  factory KeyMessage.fromMap(Map<String, dynamic> json) {
    return KeyMessage(
      key: json['key'],
      message: json['message'],
    );
  }
}
