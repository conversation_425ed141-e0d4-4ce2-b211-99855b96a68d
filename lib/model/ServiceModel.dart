import '../model/PaginationModel.dart';

class ServiceModel {
  List<ServiceList>? data;
  PaginationModel? pagination;

  ServiceModel({this.data, this.pagination});

  factory ServiceModel.fromJson(Map<String, dynamic> json) {
    return ServiceModel(
      data: json['data'] != null ? (json['data'] as List).map((i) => ServiceList.fromJson(i)).toList() : null,
      pagination: json['pagination'] != null ? PaginationModel.fromJson(json['pagination']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    if (pagination != null) {
      data['pagination'] = pagination!.toJson();
    }
    return data;
  }
}

class ServiceList {
  num? adminCommission;
  num? baseFare;
  num? cancellationFee;
  num? capacity;
  String? commissionType;
  String? createdAt;
  num? fleetCommission;
  num? id;
  var minimumDistance;
  num? minimumFare;
  String? name;
  String? paymentMethod;
  num? perDistance;
  num? perDistancePriorCancel;
  num? perMinuteDrive;
  num? perMinutePriorCancel;
  num? perMinuteWait;
  Region? region;
  num? regionId;
  String? serviceImage;
  num? status;
  String? updatedAt;
  num? waitingTimeLimit;

  ServiceList({
    this.adminCommission,
    this.baseFare,
    this.cancellationFee,
    this.capacity,
    this.commissionType,
    this.createdAt,
    this.fleetCommission,
    this.id,
    this.minimumDistance,
    this.minimumFare,
    this.name,
    this.paymentMethod,
    this.perDistance,
    this.perDistancePriorCancel,
    this.perMinuteDrive,
    this.perMinutePriorCancel,
    this.perMinuteWait,
    this.region,
    this.regionId,
    this.serviceImage,
    this.status,
    this.updatedAt,
    this.waitingTimeLimit,
  });

  factory ServiceList.fromJson(Map<String, dynamic> json) {
    return ServiceList(
      adminCommission: json['admin_commission'],
      baseFare: json['base_fare'],
      cancellationFee: json['cancellation_fee'],
      capacity: json['capacity'],
      commissionType: json['commission_type'],
      createdAt: json['created_at'],
      fleetCommission: json['fleet_commission'],
      id: json['id'],
      minimumDistance: json['minimum_distance'],
      minimumFare: json['minimum_fare'],
      name: json['name'],
      paymentMethod: json['payment_method'],
      perDistance: json['per_distance'],
      perDistancePriorCancel: json['per_distance_prior_cancel'],
      perMinuteDrive: json['per_minute_drive'],
      perMinutePriorCancel: json['per_minute_prior_cancel'],
      perMinuteWait: json['per_minute_wait'],
      region: json['region'] != null ? Region.fromJson(json['region']) : null,
      regionId: json['region_id'],
      serviceImage: json['service_image'],
      status: json['status'],
      updatedAt: json['updated_at'],
      waitingTimeLimit: json['waiting_time_limit'],
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['admin_commission'] = adminCommission;
    data['base_fare'] = baseFare;
    data['cancellation_fee'] = cancellationFee;
    data['capacity'] = capacity;
    data['commission_type'] = commissionType;
    data['created_at'] = createdAt;
    data['fleet_commission'] = fleetCommission;
    data['id'] = id;
    data['minimum_distance'] = minimumDistance;
    data['minimum_fare'] = minimumFare;
    data['name'] = name;
    data['payment_method'] = paymentMethod;
    data['per_distance'] = perDistance;
    data['per_distance_prior_cancel'] = perDistancePriorCancel;
    data['per_minute_drive'] = perMinuteDrive;
    data['per_minute_prior_cancel'] = perMinutePriorCancel;
    data['per_minute_wait'] = perMinuteWait;
    data['region_id'] = regionId;
    data['service_image'] = serviceImage;
    data['status'] = status;
    data['updated_at'] = updatedAt;
    data['waiting_time_limit'] = waitingTimeLimit;
    if (region != null) {
      data['region'] = region!.toJson();
    }
    return data;
  }
}

class Region {
  String? createdAt;
  String? currencyCode;
  String? currencyName;
  String? distanceUnit;
  int? id;
  String? name;
  int? status;
  String? timezone;
  String? updatedAt;

  Region({this.createdAt, this.currencyCode, this.currencyName, this.distanceUnit, this.id, this.name, this.status, this.timezone, this.updatedAt});

  factory Region.fromJson(Map<String, dynamic> json) {
    return Region(
      createdAt: json['created_at'],
      currencyCode: json['currency_code'],
      currencyName: json['currency_name'],
      distanceUnit: json['distance_unit'],
      id: json['id'],
      name: json['name'],
      status: json['status'],
      timezone: json['timezone'],
      updatedAt: json['updated_at'],
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['created_at'] = createdAt;
    data['currency_code'] = currencyCode;
    data['currency_name'] = currencyName;
    data['distance_unit'] = distanceUnit;
    data['id'] = id;
    data['name'] = name;
    data['status'] = status;
    data['timezone'] = timezone;
    data['updated_at'] = updatedAt;
    return data;
  }
}