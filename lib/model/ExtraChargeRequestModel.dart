class ExtraChargeRequestModel {
  String? key;
  num? value;
  String? valueType;

  ExtraChargeRequestModel({this.key, this.value, this.valueType});

  ExtraChargeRequestModel.fromJson(Map<String, dynamic> json) {
    key = json['key'];
    value = json['value'];
    valueType = json['value_type'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['key'] = key;
    data['value'] = value;
    data['value_type'] = valueType;
    return data;
  }
}
