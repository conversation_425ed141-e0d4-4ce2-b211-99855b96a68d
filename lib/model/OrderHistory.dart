class RideHistory {
  String? createdAt;
  String? datetime;
  HistoryData? historyData;
  String? historyMessage;
  String? historyType;
  int? id;
  int? rideRequestId;
  String? updatedAt;

  RideHistory({
    this.createdAt,
    this.datetime,
    this.historyData,
    this.historyMessage,
    this.historyType,
    this.id,
    this.rideRequestId,
    this.updatedAt,
  });

  factory RideHistory.fromJson(Map<String, dynamic> json) {
    return RideHistory(
      createdAt: json['created_at'],
      datetime: json['datetime'],
      historyData: json['history_data'] != null ? HistoryData.fromJson(json['history_data']) : null,
      historyMessage: json['history_message'],
      historyType: json['history_type'],
      id: json['id'],
      rideRequestId: json['ride_request_id'],
      updatedAt: json['updated_at'],
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['created_at'] = createdAt;
    data['datetime'] = datetime;
    data['history_message'] = historyMessage;
    data['history_type'] = historyType;
    data['id'] = id;
    data['ride_request_id'] = rideRequestId;
    data['updated_at'] = updatedAt;
    if (historyData != null) {
      data['history_data'] = historyData!.toJson();
    }
    return data;
  }
}

class HistoryData {
  int? driverId;
  String? driverName;

  HistoryData({this.driverId, this.driverName});

  factory HistoryData.fromJson(Map<String, dynamic> json) {
    return HistoryData(
      driverId: json['driver_id'],
      driverName: json['driver_name'],
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['driver_id'] = driverId;
    data['driver_name'] = driverName;
    return data;
  }
}
