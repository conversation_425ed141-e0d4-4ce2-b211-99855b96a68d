// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:rider/global/models/ride_model.dart';

class HomePageData {
  List<OnRideRequest> scheduledRides;
  List<HomePageBlogData> blogs;
  List<HomePageFeatureCardData> features;
  int schedule_ride_time;
  int advance_booking_limit;

  HomePageData({
    required this.schedule_ride_time,
    required this.advance_booking_limit,
    required this.scheduledRides,
    required this.blogs,
    required this.features,
  });

  factory HomePageData.fromJson(Map<String, dynamic> json) {
    List<HomePageBlogData> blogs = [];
    for (var element in (json['blogs'] as List<dynamic>)) {
      blogs.add(
        HomePageBlogData.fromJson(
          element,
        ),
      );
    }

    List<HomePageFeatureCardData> features = [];
    for (var element in (json['features'] as List<dynamic>)) {
      features.add(
        HomePageFeatureCardData.fromJson(
          element,
        ),
      );
    }

    List<OnRideRequest> rides = [];
    for (var element in (json['scheduledRides'] as List<dynamic>)) {
      rides.add(
        OnRideRequest.fromJson(
          element,
        ),
      );
    }

    int scheduleRideTime = json['schedule_ride_time'];
    int advanceBookingLimit = json['advance_booking_limit'];

    return HomePageData(
        schedule_ride_time: scheduleRideTime,
        advance_booking_limit: advanceBookingLimit,
        scheduledRides: rides,
        blogs: blogs,
        features: features);
  }
}

class HomePageBlogData {
  int id;
  String title;
  String subTitle;
  String imageURL;
  String backgroundColor;
  String textColor;

  HomePageBlogData({
    required this.id,
    required this.title,
    required this.subTitle,
    required this.imageURL,
    required this.backgroundColor,
    required this.textColor,
  });

  factory HomePageBlogData.fromJson(Map<String, dynamic> json) {
    return HomePageBlogData(
        id: json['id'],
        imageURL: json['imageURL'],
        subTitle: json['sub_title'],
        title: json['title'],
        backgroundColor: json['bg_color'],
        textColor: json['text_color']);
  }
}

class HomePageFeatureCardData {
  int id;
  String text;
  String textColor;
  String backgroundColor;
  String borderColor;
  String imageURL;

  HomePageFeatureCardData({
    required this.id,
    required this.text,
    required this.textColor,
    required this.backgroundColor,
    required this.borderColor,
    required this.imageURL,
  });

  factory HomePageFeatureCardData.fromJson(Map<String, dynamic> json) {
    return HomePageFeatureCardData(
      text: json['title'],
      textColor: json['text_color'],
      backgroundColor: json['bg_color'],
      borderColor: json['border_color'],
      imageURL: json['imageURL'],
      id: json['id'],
    );
  }
}



class CounterDataResponse {
  bool status;
  String message;
  CountersData? data;

  CounterDataResponse({
    required this.status,
    required this.message,
    this.data,
  });

  factory CounterDataResponse.fromMap(Map<String, dynamic> json) {
    return CounterDataResponse(
      status: json['status'],
      message: json['message'],
      data: json['data'] != null ? CountersData.fromMap(json['data']) : null,
    );
  }

}



class CountersData {
  int careCount;
  int notificationCount;
  int inboxCount;
  int rideIssuesCount;
  
  CountersData({
    required this.careCount,
    required this.notificationCount,
    required this.inboxCount,
    required this.rideIssuesCount,
  });


  factory CountersData.fromMap(Map<String, dynamic> map) {
    return CountersData(
      careCount: map['care_count'],
      notificationCount: map['notification_count'],
      inboxCount: map['inbox_count'],
      rideIssuesCount: map['ride_issues_count'],
    );
  }

}
