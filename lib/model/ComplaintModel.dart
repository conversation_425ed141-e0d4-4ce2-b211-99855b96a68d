class ComplaintModel {
  String? complaintBy;
  String? createdAt;
  String? description;
  int? driverId;
  String? driverName;
  String? driverProfileImage;
  int? id;
  int? rideRequestId;
  int? riderId;
  String? riderName;
  String? riderProfileImage;
  String? status;
  String? subject;
  String? updatedAt;

  ComplaintModel({
    this.complaintBy,
    this.createdAt,
    this.description,
    this.driverId,
    this.driverName,
    this.driverProfileImage,
    this.id,
    this.rideRequestId,
    this.riderId,
    this.riderName,
    this.riderProfileImage,
    this.status,
    this.subject,
    this.updatedAt,
  });

  factory ComplaintModel.fromJson(Map<String, dynamic> json) {
    return ComplaintModel(
      complaintBy: json['complaint_by'],
      createdAt: json['created_at'],
      description: json['description'],
      driverId: json['driver_id'],
      driverName: json['driver_name'],
      driverProfileImage: json['driver_profile_image'],
      id: json['id'],
      rideRequestId: json['ride_request_id'],
      riderId: json['rider_id'],
      riderName: json['rider_name'],
      riderProfileImage: json['rider_profile_image'],
      status: json['status'],
      subject: json['subject'],
      updatedAt: json['updated_at'],
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['complaint_by'] = complaintBy;
    data['created_at'] = createdAt;
    data['description'] = description;
    data['driver_id'] = driverId;
    data['driver_name'] = driverName;
    data['driver_profile_image'] = driverProfileImage;
    data['id'] = id;
    data['ride_request_id'] = rideRequestId;
    data['rider_id'] = riderId;
    data['rider_name'] = riderName;
    data['rider_profile_image'] = riderProfileImage;
    data['status'] = status;
    data['subject'] = subject;
    data['updated_at'] = updatedAt;
    return data;
  }
}
