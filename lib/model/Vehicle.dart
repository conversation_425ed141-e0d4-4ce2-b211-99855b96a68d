import 'PaginationModel.dart';

class VehicleDataResponse {
  PaginationModel pagination;
  List<Vehicle> data;
  String? message;

  VehicleDataResponse({
    required this.pagination,
    required this.data,
    this.message,
  });

  factory VehicleDataResponse.fromJson(Map<String, dynamic> json) {
    List<Vehicle> vehicleData = [];
    for (var i = 0; i < json['data'].length; i++) {
      vehicleData.add(
        Vehicle.fromJson(
          json['data'][i],
        ),
      );
    }
    return VehicleDataResponse(
        pagination: PaginationModel.fromJson(
          json['pagination'],
        ),
        data: vehicleData,
        message: json['message']);
  }
}

class Vehicle {
  int? id;
  String imageURL;
  String service_name;
  int service_id;
  String name;
  String plateNumber;
  String transmission;

  Vehicle({
    required this.id,
    required this.service_id,
    required this.imageURL,
    required this.name,
    required this.service_name,
    required this.plateNumber,
    required this.transmission,
  });

  factory Vehicle.fromJson(Map<String, dynamic> json) {
    return Vehicle(
      id: json['id'],
      name: json['name'],
      service_id: json['service_id'],
      imageURL: json['imageURL'],
      service_name: json['service_name'],
      plateNumber: json['plateNumber'],
      transmission: json['transmission'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      "id": id,
      "name": name,
      "service_id": service_id,
      "imageURL": imageURL,
      "service_name": service_name,
      "plateNumber": plateNumber,
      "transmission": transmission,
    };
  }
}
