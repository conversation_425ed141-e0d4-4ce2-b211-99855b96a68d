import 'PaginationModel.dart';

class PaymentCardDataResponse {
  PaginationModel pagination;
  List<SavedCard> data;
  String? message;

  PaymentCardDataResponse({
    required this.pagination,
    required this.data,
    this.message,
  });

  factory PaymentCardDataResponse.fromJson(Map<String, dynamic> json) {
    List<SavedCard> vehicleData = [];
    for (var i = 0; i < json['data'].length; i++) {
      vehicleData.add(
        SavedCard.fromJson(
          json['data'][i],
        ),
      );
    }
    return PaymentCardDataResponse(
        pagination: PaginationModel.fromJson(
          json['pagination'],
        ),
        data: vehicleData,
        message: json['message']);
  }
}

class SavedCard {
  String id;
  String brand;
  int exp_month;
  int exp_year;
  String last4;
  String fingerPrint;

  SavedCard({
    required this.id,
    required this.brand,
    required this.exp_month,
    required this.exp_year,
    required this.last4,
    required this.fingerPrint,
  });

  factory SavedCard.fromJson(Map<String, dynamic> json) {
    return SavedCard(
      id: json['id'],
      brand: json['card']['brand'],
      exp_month: json['card']['exp_month'],
      exp_year: json['card']['exp_year'],
      last4: json['card']['last4'],
      fingerPrint: json['card']['fingerprint'],
    );
  }
}
