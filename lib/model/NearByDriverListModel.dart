class NearByDriverListModel {
  String? displayName;
  String? firstName;
  int? id;
  num? isAvailable;
  num? isOnline;
  String? lastLocationUpdateAt;
  String? lastName;
  String? latitude;
  String? longitude;
  num? rating;
  String? status;

  NearByDriverListModel({
    this.displayName,
    this.firstName,
    this.id,
    this.isAvailable,
    this.isOnline,
    this.lastLocationUpdateAt,
    this.lastName,
    this.latitude,
    this.longitude,
    this.rating,
    this.status,
  });

  factory NearByDriverListModel.fromJson(Map<String, dynamic> json) {
    return NearByDriverListModel(
      displayName: json['display_name'],
      firstName: json['first_name'],
      id: json['id'],
      isAvailable: json['is_available'],
      isOnline: json['is_online'],
      lastLocationUpdateAt: json['last_location_update_at'],
      lastName: json['last_name'],
      latitude: json['latitude'],
      longitude: json['longitude'],
      rating: json['rating'],
      status: json['status'],
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['display_name'] = displayName;
    data['first_name'] = firstName;
    data['id'] = id;
    data['is_available'] = isAvailable;
    data['is_online'] = isOnline;
    data['last_location_update_at'] = lastLocationUpdateAt;
    data['last_name'] = lastName;
    data['latitude'] = latitude;
    data['longitude'] = longitude;
    data['rating'] = rating;
    data['status'] = status;
    return data;
  }
}
