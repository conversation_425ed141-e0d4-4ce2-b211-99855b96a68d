// ignore_for_file: public_member_api_docs, sort_constructors_first

import 'package:rider/model/PaginationModel.dart';

class Inbox {
  PaginationModel pagination;
  List<InboxData> data;
  String? message;
  Inbox({
    required this.pagination,
    required this.data,
    this.message,
  });

  factory Inbox.fromJson(Map<String, dynamic> json) {
    List<InboxData> inboxData = [];
    for (var i = 0; i < json['data'].length; i++) {
      inboxData.add(
        InboxData.fromJson(
          json['data'][i],
        ),
      );
    }
    return Inbox(
        pagination: PaginationModel.fromJson(
          json['pagination'],
        ),
        data: inboxData,
        message: json['message']);
  }
}

class InboxData {
  int id;
  String title;
  String description;
  bool is_read;
  String imageURL;
  String created_at;
  InboxData({
    required this.id,
    required this.title,
    required this.description,
    required this.is_read,
    required this.imageURL,
    required this.created_at,
  });

  factory InboxData.fromJson(Map<String, dynamic> json) {
    return InboxData(
      description: json['description'] as String,
      id: json['id'] as int,
      imageURL: json['imageURL'] as String,
      is_read: json['is_read'] as bool,
      title: json['title'] as String,
      created_at: json['created_at'] as String,
    );
  }
}
