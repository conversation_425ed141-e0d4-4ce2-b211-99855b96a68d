import '../model/OrderHistory.dart';
import '../model/RiderModel.dart';
import 'PaginationModel.dart';

class RiderListModel {
  List<RiderModel>? data;
  PaginationModel? pagination;
  String? message;
  List<RideHistory>? rideHistory;

  RiderListModel({
    this.data,
    this.pagination,
    this.rideHistory,
    this.message,
  });

  factory RiderListModel.fromJson(Map<String, dynamic> json) {
    return RiderListModel(
        data: json['data'] != null
            ? (json['data'] as List).map((i) => RiderModel.fromJson(i)).toList()
            : null,
        pagination: json['pagination'] != null
            ? PaginationModel.fromJson(json['pagination'])
            : null,
        rideHistory: json['ride_history'] != null
            ? (json['ride_history'] as List)
                .map((i) => RideHistory.fromJson(i))
                .toList()
            : null,
        message: json['message']);
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    if (pagination != null) {
      data['pagination'] = pagination!.toJson();
    }
    if (rideHistory != null) {
      data['ride_history'] = rideHistory!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}
