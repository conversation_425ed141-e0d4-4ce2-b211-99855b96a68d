import 'package:rider/features/ride_flow/screen/manage_ride_stops_logic.dart';

import 'SettingModel.dart';

class AppSettingModel {
  RegionModel? region;
  SettingModel? settingModel;
  List<RideSetting>? rideSetting;
  List<WalletSetting>? walletSetting;
  CurrencySetting? currencySetting;
  PrivacyPolicyModel? privacyPolicyModel;
  PrivacyPolicyModel? termsCondition;
  dynamic twilio;
  String? account_delete_instructions_for_rider;
  String? about_us_instruction_rider;

  AppSettingModel({
    this.region,
    this.rideSetting,
    this.walletSetting,
    this.currencySetting,
    this.settingModel,
    this.privacyPolicyModel,
    this.termsCondition,
    this.account_delete_instructions_for_rider,
    this.about_us_instruction_rider,
  });

  AppSettingModel.fromJson(Map<String, dynamic> json) {
    region = json['region'] != null ? RegionModel.fromJson(json['region']) : null;

    settingModel = settingModel ??
        (json['app_setting'] != null
            ? SettingModel.fromJson(json['app_setting'])
            : null);

    if (json['ride_setting'] != null) {
      rideSetting = <RideSetting>[];
      json['ride_setting'].forEach((v) {
        rideSetting!.add(RideSetting.fromJson(v));
      });
    }
    if (json['Wallet_setting'] != null) {
      walletSetting = <WalletSetting>[];
      json['Wallet_setting'].forEach((v) {
        walletSetting!.add(WalletSetting.fromJson(v));
      });
    }
    currencySetting = json['currency_setting'] != null
        ? CurrencySetting.fromJson(json['currency_setting'])
        : null;
    privacyPolicyModel = json['privacy_policy'] != null
        ? PrivacyPolicyModel.fromJson(json['privacy_policy'])
        : null;
    termsCondition = json['terms_condition'] != null
        ? PrivacyPolicyModel.fromJson(json['terms_condition'])
        : null;

    twilio = json['twilio']?['contact_number'];

    about_us_instruction_rider = json['about_us_instruction_rider'];
    account_delete_instructions_for_rider =
        json['account_delete_instructions_for_rider'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (region != null) {
      data['region'] = region!.toJson();
    }
    if (settingModel != null) {
      data['app_seeting'] = settingModel!.toJson();
    }
    if (rideSetting != null) {
      data['ride_setting'] = rideSetting!.map((v) => v.toJson()).toList();
    }
    if (walletSetting != null) {
      data['Wallet_setting'] = walletSetting!.map((v) => v.toJson()).toList();
    }
    if (currencySetting != null) {
      data['currency_setting'] = currencySetting!.toJson();
    }
    if (privacyPolicyModel != null) {
      data['privacy_policy'] = privacyPolicyModel!.toJson();
    }
    if (termsCondition != null) {
      data['terms_condition'] = termsCondition!.toJson();
    }
    return data;
  }
}

class RegionModel {
  int? id;
  String? name;
  String? currencyName;
  String? currencyCode;
  String? distanceUnit;
  int? status;
  String? timezone;
  String? createdAt;
  String? updatedAt;

  RegionModel({
    this.id,
    this.name,
    this.currencyName,
    this.currencyCode,
    this.distanceUnit,
    this.status,
    this.timezone,
    this.createdAt,
    this.updatedAt,
  });

  RegionModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    currencyName = json['currency_name'];
    currencyCode = json['currency_code'];
    distanceUnit = json['distance_unit'];
    status = json['status'];
    timezone = json['timezone'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['currency_name'] = currencyName;
    data['currency_code'] = currencyCode;
    data['distance_unit'] = distanceUnit;
    data['status'] = status;
    data['timezone'] = timezone;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    return data;
  }
}

class WalletSetting {
  int? id;
  String? key;
  String? type;
  String? value;

  WalletSetting({this.id, this.key, this.type, this.value});

  WalletSetting.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    key = json['key'];
    type = json['type'];
    value = json['value'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['key'] = key;
    data['type'] = type;
    data['value'] = value;
    return data;
  }
}

class RideSetting {
  int? id;
  String? key;
  String? type;
  String? value;

  RideSetting({this.id, this.key, this.type, this.value});

  RideSetting.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    key = json['key'];
    type = json['type'];
    value = json['value'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['key'] = key;
    data['type'] = type;
    data['value'] = value;
    return data;
  }
}

class CurrencySetting {
  String? name;
  String? code;
  String? symbol;
  String? position;

  CurrencySetting({this.name, this.code, this.position, this.symbol});

  CurrencySetting.fromJson(Map<String, dynamic> json) {
    name = json['name'];
    code = json['code'];
    position = json['position'];
    symbol = json['symbol'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['name'] = name;
    data['code'] = code;
    data['position'] = position;
    data['symbol'] = symbol;
    return data;
  }
}

class PrivacyPolicyModel {
  int? id;
  String? key;
  String? type;
  String? value;

  PrivacyPolicyModel({this.id, this.key, this.type, this.value});

  factory PrivacyPolicyModel.fromJson(Map<String, dynamic> json) {
    return PrivacyPolicyModel(
      id: json['id'],
      key: json['key'],
      type: json['type'],
      value: json['value'],
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['key'] = key;
    data['type'] = type;
    data['value'] = value;
    return data;
  }
}

class AppSettingsResponse extends ApiBaseResponse<AppSettingModel> {
  AppSettingsResponse(
      {required super.status, required super.message, required super.data});

  factory AppSettingsResponse.fromMap(Map<String, dynamic> map) {
    return AppSettingsResponse(
      status: map["status"],
      message: map["message"],
      data: map["data"] == null ? null : AppSettingModel.fromJson(map["data"]),
    );
  }
}
