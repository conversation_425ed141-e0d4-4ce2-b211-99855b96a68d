class NotificationListModel {
  int? allUnreadCount;
  List<NotificationData>? notificationData;
  String? message;

  NotificationListModel(
      {this.allUnreadCount, this.notificationData, this.message});

  factory NotificationListModel.fromJson(Map<String, dynamic> json) {
    return NotificationListModel(
      allUnreadCount: json['all_unread_count'],
      notificationData: json['notification_data'] != null
          ? (json['notification_data'] as List)
              .map((i) => NotificationData.fromJson(i))
              .toList()
          : null,
      message: json['message'],
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['all_unread_count'] = allUnreadCount;
    if (notificationData != null) {
      data['notification_data'] =
          notificationData!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class NotificationData {
  NotificationOrderData? data;
  String? createdAt;
  String? id;
  String? readAt;

  NotificationData({this.data, this.createdAt, this.id, this.readAt});

  factory NotificationData.fromJson(Map<String, dynamic> json) {
    return NotificationData(
      data: json['data'] != null
          ? NotificationOrderData.fromJson(json['data'])
          : null,
      createdAt: json['created_at'],
      id: json['id'],
      readAt: json['read_at'],
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['created_at'] = createdAt;
    data['id'] = id;
    data['read_at'] = readAt;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class NotificationOrderData {
  int? id;
  int? complaintId;
  String? message;
  String? subject;
  String? type;

  NotificationOrderData(
      {this.id, this.complaintId, this.message, this.subject, this.type});

  factory NotificationOrderData.fromJson(Map<String, dynamic> json) {
    return NotificationOrderData(
      id: json['id'],
      complaintId: json['complaint_id'],
      message: json['message'],
      subject: json['subject'],
      type: json['type'],
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['complaint_id'] = complaintId;
    data['message'] = message;
    data['subject'] = subject;
    data['type'] = type;
    return data;
  }
}
