import 'package:chucker_flutter/chucker_flutter.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:mapbox_maps_flutter/mapbox_maps_flutter.dart';
import 'package:rider/app_exports.dart';
import 'package:rider/components/booking_widget.dart';
import 'package:rider/features/edit_profile/cubit/edit_profile_cubit.dart';
import 'package:rider/global/models/ride_model.dart';
import 'package:rider/screens/splash_screen.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/services.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:rider/screens/waiting_charges_and_tip_screen.dart';
import '/model/FileModel.dart';
import '/model/LanguageDataModel.dart';
import 'firebase_options.dart';
import 'global/constants/app_theme.dart';
import 'language/AppLocalizations.dart';
import 'service/ChatMessagesService.dart';
import 'service/NotificationService.dart';
import 'service/UserServices.dart';
import 'utils/DataProvider.dart';

List<LanguageDataModel> localeLanguageList = [];
LanguageDataModel? selectedLanguageDataModel;

List<FileModel> fileList = [];
bool mIsEnterKey = false;
bool isCurrentlyOnNoInternet = false;

ChatMessageService chatMessageService = ChatMessageService();
NotificationService notificationService = NotificationService();
UserService userService = UserService();

final navigatorKey = GlobalKey<NavigatorState>();

get getContext => navigatorKey.currentState?.overlay?.context;

Future<void> initialize({
  double? defaultDialogBorderRadius,
  List<LanguageDataModel>? aLocaleLanguageList,
  String? defaultLanguage,
}) async {
  localeLanguageList = aLocaleLanguageList ?? [];
  selectedLanguageDataModel =
      getSelectedLanguageModel(defaultLanguage: default_Language);
}

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  Globals.sharedPrefs = await SharedPreferences.getInstance();
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);
  await initialize(aLocaleLanguageList: languageList());
  MapboxOptions.setAccessToken(AppCred.mapBoxPublicTokenKey);

  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  // ChuckerFlutter.showOnRelease = true;
  runApp(const MyApp());
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> with WidgetsBindingObserver {
  DateTime _lastAppPausedTime = DateTime.now();

  @override
  void initState() {
    WidgetsBinding.instance.addObserver(this);

    super.initState();
    init();

    afterBuildCreated(() {
      Globals.fToast.init(navigatorKey.currentContext!);
      checkIfDarkModeIsOn(context);
    });
  }

  @override
  void setState(fn) {
    if (mounted) super.setState(fn);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    switch (state) {
      case AppLifecycleState.paused:
        _lastAppPausedTime = DateTime.now();
        break;

      case AppLifecycleState.resumed:
        checkIfDarkModeIsOn(context);
        _doNotificationRelatedWork();
        // _checkForCurrentRide();
      default:
    }
  }

  Future<void> _doNotificationRelatedWork() async {
    if (Globals.isUserLoggedIn && await Permission.notification.isGranted) {
      OneSignalService.init(appId: Constants.oneSignalRiderAppId);
    }
  }

  Future<void> _checkForCurrentRide() async {
    if (Globals.isUserLoggedIn == false) {
      return;
    }
    if (Globals.isWaitingAndTipChargesScreenOpened) {
      return;
    }
    if (Globals.isRideReviewScreenOpened) {
      return;
    }
    if (Globals.isRidePendingForPayment) {
      return;
    }
    if (DateTime.now().difference(_lastAppPausedTime).inSeconds > 4) {
      if (Globals.currentRideScreenUpdator != null) {
        Globals.currentRideScreenUpdator!();
      } else {
        getCurrentRequest(showLoading: false);
      }
    }
  }

  void init() async {}

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (context) => EditProfileCubit(),
        ),
      ],
      child: MaterialApp(
        navigatorKey: navigatorKey,
        navigatorObservers: [ChuckerFlutter.navigatorObserver],
        debugShowCheckedModeBanner: false,
        title: Constants.appName,
        theme: AppTheme.lightTheme,
        darkTheme: AppTheme.darkTheme,
        themeMode: ThemeMode.system,
        builder: (context, child) {
          SystemChrome.setSystemUIOverlayStyle(
            SystemUiOverlayStyle(
              statusBarColor: Theme.of(context).primaryColor,
              statusBarIconBrightness:
                  Theme.of(context).brightness == Brightness.dark
                      ? Brightness.light
                      : Brightness.dark,
            ),
          );
          return FToastBuilder()(context,
              ScrollConfiguration(behavior: MyBehavior(), child: child!));
        },
        // home: const SplashScreen(),
       home: WaitingChargesAndTipScreen(request: RideModel()),
        supportedLocales: LanguageDataModel.languageLocales(),
        localizationsDelegates: const [
          AppLocalizations(),
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
        ],
        localeResolutionCallback: (locale, supportedLocales) => locale,
        locale: const Locale(default_Language),
      ),
    );
  }
}

class MyBehavior extends ScrollBehavior {
  @override
  Widget buildOverscrollIndicator(
      BuildContext context, Widget child, ScrollableDetails details) {
    return child;
  }
}
