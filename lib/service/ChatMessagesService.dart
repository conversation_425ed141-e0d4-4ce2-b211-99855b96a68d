import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:rider/app_exports.dart';
import '../model/ChatMessageModel.dart';
import '../model/ContactModel.dart';
import 'BaseServices.dart';

class ChatMessageService extends BaseService {
  FirebaseFirestore fireStore = FirebaseFirestore.instance;
  late CollectionReference userRef;

  ChatMessageService() {
    ref = fireStore.collection("messages");
    userRef = fireStore.collection("users");
  }

  Query chatMessagesWithPagination({
    required String? currentUserId,
    required String receiverUserId,
    required int rideId,
  }) {
    return ref!
        // .doc(currentUserId)
        // .collection(receiverUserId)
        .where('rideId', isEqualTo: rideId)
        .orderBy("createdAt", descending: true);
  }

  Future<DocumentReference> addMessage(ChatMessageModel data) async {
    var doc = await ref!
        // .doc(data.senderId)
        // .collection(data.receiverId!)
        .add(data.toJson());
    doc.update({'id': doc.id});
    return doc;
  }

  Future<void> addMessageToDb(DocumentReference senderDoc,
      ChatMessageModel data, UserModel sender, UserModel? user,
      {File? image}) async {
    String imageUrl = '';

    // if (image != null) {
    //   String fileName = basename(image.path);
    //   Reference storageRef =
    //       _storage.ref().child("chatImages/${Globals.user.uid}/$fileName");

    //   UploadTask uploadTask = storageRef.putFile(image);

    //   await uploadTask.then((e) async {
    //     await e.ref.getDownloadURL().then((value) async {
    //       imageUrl = value;

    //       fileList.removeWhere((element) => element.id == senderDoc.id);
    //     }).catchError((e) {
    //       log(e);
    //     });
    //   }).catchError((e) {
    //     log(e);
    //   });
    // }

    updateChatDocument(senderDoc, image: image, imageUrl: imageUrl);

    userRef.doc(data.senderFirestoreId).update({"lastMessageTime": data.createdAt});
    addToContacts(senderId: data.senderFirestoreId, receiverId: data.recieverFirestoreId);

    DocumentReference receiverDoc = await ref!
        // .doc(data.receiverId)
        // .collection(data.senderId!)
        .add(data.toJson());

    updateChatDocument(receiverDoc, image: image, imageUrl: imageUrl);

    userRef.doc(data.recieverFirestoreId).update({"lastMessageTime": data.createdAt});
  }

  DocumentReference? updateChatDocument(DocumentReference data,
      {File? image, String? imageUrl}) {
    Map<String, dynamic> sendData = {'id': data.id};

    if (image != null) {
      sendData.putIfAbsent('photoUrl', () => imageUrl);
    }
    // log(sendData);
    data.update(sendData);

    log("Data $sendData");
    return null;
  }

  DocumentReference getContactsDocument({String? of, String? forContact}) {
    return userRef.doc(of).collection("contacts").doc(forContact);
  }

  addToContacts({String? senderId, String? receiverId}) async {
    Timestamp currentTime = Timestamp.now();

    await addToSenderContacts(senderId, receiverId, currentTime);
    await addToReceiverContacts(senderId, receiverId, currentTime);
  }

  Future<void> addToSenderContacts(
      String? senderId, String? receiverId, currentTime) async {
    DocumentSnapshot senderSnapshot =
        await getContactsDocument(of: senderId, forContact: receiverId).get();

    if (!senderSnapshot.exists) {
      //does not exists
      ContactDataModel receiverContact = ContactDataModel(
        uid: receiverId,
        addedOn: currentTime,
      );

      await getContactsDocument(of: senderId, forContact: receiverId)
          .set(receiverContact.toJson());
    }
  }

  Future<void> addToReceiverContacts(
    String? senderId,
    String? receiverId,
    currentTime,
  ) async {
    DocumentSnapshot receiverSnapshot =
        await getContactsDocument(of: receiverId, forContact: senderId).get();

    if (!receiverSnapshot.exists) {
      //does not exists
      ContactDataModel senderContact = ContactDataModel(
        uid: senderId,
        addedOn: currentTime,
      );
      await getContactsDocument(of: receiverId, forContact: senderId)
          .set(senderContact.toJson());
    }
  }

  //Fetch User List

  Stream<QuerySnapshot> fetchContacts({String? userId}) {
    return userRef.doc(userId).collection("contacts").snapshots();
  }

  Stream<List<UserModel>> getUserDetailsById({String? id, String? searchText}) {
    return userRef
        .where("uid", isEqualTo: id)
        .where('caseSearch',
            arrayContains: searchText.validate().isEmpty
                ? null
                : searchText!.toLowerCase())
        .snapshots()
        .map((event) => event.docs
            .map((e) => UserModel.fromJson(e.data() as Map<String, dynamic>))
            .toList());
  }

  Stream<QuerySnapshot> fetchLastMessageBetween(
      {required String senderId, required String receiverId}) {
    return ref!
        .doc(senderId.toString())
        .collection(receiverId.toString())
        .orderBy("createdAt", descending: false)
        .snapshots();
  }

  Future<void> clearAllMessages(
      {String? senderId, required String receiverId}) async {
    final WriteBatch batch = fireStore.batch();

    ref!.doc(senderId).collection(receiverId).get().then((value) {
      for (var document in value.docs) {
        batch.delete(document.reference);
      }

      return batch.commit();
    }).catchError((e) {});
  }

  Future<void> deleteChat(
      {String? senderId, required String receiverId}) async {
    ref!.doc(senderId).collection(receiverId).doc().delete();
    userRef.doc(senderId).collection("contacts").doc(receiverId).delete();
  }

  Future<void> deleteSingleMessage(
      {String? senderId,
      required String receiverId,
      String? documentId}) async {
    try {
      ref!.doc(senderId).collection(receiverId).doc(documentId).delete();
    } on Exception catch (e) {
      log(e.toString());
      throw 'Something went wrong';
    }
  }

  Future<void> setUnReadStatusToTrue(
      {required String senderFirestoreId,
      required String receiverFirestoreId,
      required int rideId,
      String? documentId}) async {
    try {
      ref!
          .where(
            'isMessageRead',
            isEqualTo: false,
          )
          .where('rideId', isEqualTo: rideId)
          .where('receiverFirestoreId', isEqualTo: senderFirestoreId)
          .get()
          .then((value) {
        for (var element in value.docs) {
          element.reference.update({
            'isMessageRead': true,
          });
        }
      });
    } catch (e) {
      print(e);
    }
  }

  Stream<int> getUnReadCount(
      {String? senderId, required String receiverId, String? documentId}) {
    return ref!
        .doc(senderId.toString())
        .collection(receiverId)
        .where('isMessageRead', isEqualTo: false)
        .where('receiverId', isEqualTo: senderId)
        .snapshots()
        .map(
          (event) => event.docs.length,
        )
        .handleError((e) {
      return e;
    });
  }
}
