// import 'package:rider/global/widgets/app_button.dart';
// import 'package:rider/main.dart';
// import 'package:rider/utils/Extensions/app_common.dart';
// import 'package:rider/utils/Extensions/app_textfield.dart';
// import 'package:flutter/foundation.dart';
// import 'package:flutter/material.dart';

// class CouponDialogCard extends StatelessWidget {
//   final 
//   const CouponDialogCard({super.key});

//   @override
//   Widget build(BuildContext context) {
//     return AlertDialog(
//                                     shape: dialogShape(),
//                                     // titlePadding: EdgeInsets.zero,
//                                     backgroundColor:
//                                         Theme.of(context).cardColor,
//                                     elevation: 4,
//                                     title: Column(
//                                       children: [
//                                         Row(
//                                           children: [
//                                             Expanded(
//                                               child: Text(
//                                                 Globals.language.applyCoupons,
//                                                 style: TextStyle(),
//                                               ),
//                                             ),
//                                             SizedBox(
//                                               height: 30,
//                                               child: FloatingActionButton(
//                                                   onPressed: () {
//                                                     Navigator.of(context).pop();
//                                                   },
//                                                   child: Icon(Icons.close)),
//                                             ),
//                                           ],
//                                         ),
//                                         Divider()
//                                       ],
//                                     ),
//                                     content: Column(
//                                       mainAxisSize: MainAxisSize.min,
//                                       crossAxisAlignment:
//                                           CrossAxisAlignment.start,
//                                       children: [
//                                         Form(
//                                           key: _couponFormKey,
//                                           child: Column(
//                                             children: [
//                                               AppTextField(
//                                                   isValidationRequired: true,
//                                                   validator: (value) {
//                                                     if (value!.length < 3) {
//                                                       return language
//                                                           .thisFieldRequired;
//                                                     }
//                                                     return null;
//                                                   },
//                                                   errorThisFieldRequired:
//                                                       language
//                                                           .thisFieldRequired,
//                                                   controller:
//                                                       _promoCodeController,
//                                                   textFieldType:
//                                                       TextFieldType.ADDRESS,
//                                                   decoration: InputDecoration(
//                                                       hintText: language
//                                                           .lblCouponCode)),
//                                               const SizedBox(
//                                                 height: 20,
//                                               ),
//                                               AppButton(
//                                                   text: Globals.language.applyCoupons,
//                                                   onPressed: () {
//                                                     if (_couponFormKey
//                                                         .currentState!
//                                                         .validate()) {
//                                                       Navigator.of(context)
//                                                           .pop();
//                                                       _getEstimatedPriceTime();
//                                                     }
//                                                   })
//                                             ],
//                                           ),
//                                         )
//                                       ],
//                                     ),
//                                   ),;
//   }
// }