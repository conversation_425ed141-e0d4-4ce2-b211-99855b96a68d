import 'package:rider/app_exports.dart';

class CouponChip extends StatefulWidget {
  final String value;
  final void Function() onClose;
  const CouponChip({super.key, required this.value, required this.onClose});

  @override
  CouponChipState createState() => CouponChipState();
}

class CouponChipState extends State<CouponChip> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _showSuccessDialog(context);
    });
  }

  void _showSuccessDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20.0),
          ),
          child: Container(
            padding: const EdgeInsets.all(20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  '🎉Congratulations!',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: AppColors.greenColor,
                  ),
                ),
                const SizedBox(height: 10),
                Text(
                  'Coupon applied successfully.',
                  textAlign: TextAlign.center,
                  style: TextStyle(fontSize: 16, color: AppColors.greenColor),
                ),
                const SizedBox(height: 20),
                AppButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  text: "Close",
                  icon: Icon(Icons.check, color: AppColors.greenColor),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(
        vertical: 8,
      ),
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.5),
            spreadRadius: 1,
            blurRadius: 5,
            offset: const Offset(0, 3),
          ),
        ],
        border: Border.all(color: AppColors.greenColor),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            widget.value,
            style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16,color: Colors.black),
          ),
          const SizedBox(width: 20),
          Text(
            "Coupon applied",
            style: TextStyle(fontSize: 12, color: AppColors.greenColor),
          ),
          const SizedBox(width: 8),
          AppButton(
            backgoundColor: Colors.grey.shade300,
            onPressed: widget.onClose,
            text: "",
            icon: const Icon(
              Icons.close,
            ),
          )
        ],
      ),
    );
  }
}
