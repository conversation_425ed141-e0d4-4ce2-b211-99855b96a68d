import 'package:rider/app_exports.dart';
import 'package:rider/features/booking/utils/saved_places_map_utils.dart';
import 'package:rider/features/select_location/models/mapbox_location_model.dart';

class MapPickupScreen extends StatefulWidget {
  const MapPickupScreen({super.key});

  @override
  State<MapPickupScreen> createState() => _MapPickupScreenState();
}

class _MapPickupScreenState extends State<MapPickupScreen> {
  final SavedPlacesMapUtils _mapUtils = SavedPlacesMapUtils();

  @override
  void initState() {
    super.initState();
    _mapUtils.resetInitialState();
  }

  @override
  void dispose() {
    _mapUtils.release();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const RoooAppbar(title: "Pick from map"),
      body: Stack(
        children: [
          _mapUtils.getMapWidget(),
          Center(
            child: SizedBox(
              height: 20,
              width: 20,
              child: Image.asset(Assets.destinationIcon),
            ),
          ),
          Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: ValueListenableBuilder<MapBoxLocationModel>(
                valueListenable: _mapUtils.addressNotifier,
                builder: (context, value, child) {
              
                  if (value.point.latitude == -1 || value.point.latitude == 0) {
                    return Container(
                        padding: Constants.screenPadding,
                        color: Colors.black,
                        // decoration: BoxDecoration(
                        //     borderRadius: Constants.appRadius,
                        //     color: Colors.black.withValues(alpha: 0.7)),
                        child: const Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text('Loading....',
                                style: TextStyle(color: Colors.white)),
                            CircularProgressIndicator()
                          ],
                        ));
                  }
                  if (value.point.latitude == -2) {
                    return const Text("No Address found");
                  }
                  if (value.point.latitude == -3) {
                    return Text(Globals.language.errorMsg);
                  }
                  return Container(
                      padding: EdgeInsets.fromLTRB(16, 16, 16, Platform.isIOS ? 50 : 30),
                      color: Colors.black,
                      // decoration: BoxDecoration(
                      //   borderRadius: Constants.appRadius,
                      //   color: Colors.black.withValues(alpha: 0.7),
                      // ),
                      child: Column(
                        children: [
                          Text(
                            "Address: ${value.address}",
                            style: const TextStyle(color: Colors.white),
                          ),
                          height10,
                          AppButtonWidget(
                              fullWidth: false,
                              text: "Select",
                              onTap: () {
                                Navigator.pop(context, value);
                              })
                        ],
                      ));
                },
              )),
          Positioned(
            right: 16,
            top: 16,
            child: FloatingActionButton(
              onPressed: () async {
                await _mapUtils.flyToLocation(
                  Globals.currentLocation!.lat.toDouble(),
                  Globals.currentLocation!.lng.toDouble(),
                );
                await Future.delayed(const Duration(seconds: 2));
              },
              child: const Icon(Icons.my_location),
            ),
          ),
        ],
      ),
    );
  }
}
