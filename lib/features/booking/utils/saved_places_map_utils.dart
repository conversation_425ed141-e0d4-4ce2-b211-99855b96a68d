import 'package:location/location.dart' as nlp;
import 'package:mapbox_maps_flutter/mapbox_maps_flutter.dart';
import 'package:rider/app_exports.dart';
import 'package:rider/features/select_location/models/mapbox_location_model.dart';
import 'package:rider/main.dart';
import 'package:rider/services/map_box_service.dart';
import 'package:rider/services/models/annotation_managers.dart';
import 'package:http/http.dart' as http;

class SavedPlacesMapUtils {
  static final SavedPlacesMapUtils _instance = SavedPlacesMapUtils._internal();

  factory SavedPlacesMapUtils() {
    return _instance;
  }
  SavedPlacesMapUtils._internal();
  MapboxMap? _mapboxMap;
  AnnotationManagers? _annotationManagers;
  bool _isInitialized = false;
  int _referenceCount = 0;
  bool _isInitial = true;
  Widget? _mapWidget;
  Function(MapBoxLocationModel)? onAddressChanged;
  ValueNotifier<MapBoxLocationModel>? _address;
  Timer? _debounce;
  MapboxMap? get mapboxMap => _mapboxMap;
  bool get isInitialized => _isInitialized;
  ValueNotifier<MapBoxLocationModel> get addressNotifier {
    _address ??= ValueNotifier(MapBoxLocationModel(
        point: MapBoxPoint(latitude: 0, longitude: 0), address: ""));
    return _address!;
  }

  final Set<Factory<OneSequenceGestureRecognizer>> gestureRecognizers = {
    Factory<OneSequenceGestureRecognizer>(() => EagerGestureRecognizer()),
  };

  Widget getMapWidget() {
    _mapWidget ??= MapWidget(
      onCameraChangeListener: _onCameraChanged,
      gestureRecognizers: gestureRecognizers,
      key: const ValueKey("savedPlacesMapWidget"),
      onMapCreated: _onMapCreated,
    );
    return _mapWidget!;
  }

  Future<void> _onMapCreated(MapboxMap mapboxMap) async {
    if (_isInitialized && _mapboxMap != null) {
      _referenceCount++;
      return;
    }

    showAppActivityDialog(
        context: navigatorKey.currentContext!, title: "Loading Map...");

    _mapboxMap = mapboxMap;
    _annotationManagers =
        await MapBoxService.initializeAnnotationManagers(mapboxMap);
    _isInitialized = true;
    _referenceCount++;

    await _configureMap();
  }

  void _onCameraChanged(CameraChangedEventData cameraChangedEventData) {
    if (_debounce?.isActive ?? false) {
      _debounce?.cancel();
    }

    _debounce = Timer(const Duration(milliseconds: 500), () {
      if (_isInitial) {
        _isInitial = false;
        return;
      }

      _processReverseGeocode(cameraChangedEventData);
    });
  }

  Future<void> _processReverseGeocode(
      CameraChangedEventData cameraChangedEventData) async {
    final addressNotifier = this.addressNotifier;
    addressNotifier.value = MapBoxLocationModel(
        point: MapBoxPoint(longitude: -1, latitude: -1), address: "-1");

    final result = await reverseGeocode(cameraChangedEventData);
    addressNotifier.value = result;

    if (onAddressChanged != null) {
      onAddressChanged!(result);
    }
  }

  Future<void> _configureMap() async {
    if (_mapboxMap == null) return;

    nlp.LocationData currentLocation;
    if (Globals.currentLocation == null) {
      currentLocation = await nlp.Location().getLocation();
    } else {
      currentLocation = nlp.LocationData.fromMap({
        "latitude": Globals.currentLocation!.lat,
        "longitude": Globals.currentLocation!.lng,
      });
    }

    _mapboxMap!.location.updateSettings(
        LocationComponentSettings(enabled: true, pulsingEnabled: true));

    await _mapboxMap!.setBounds(CameraBoundsOptions(
      maxZoom: 20,
      minZoom: 2,
    ));

    await Future.delayed(const Duration(seconds: 1));
    await _mapboxMap!.flyTo(
      CameraOptions(
          zoom: 16,
          center: Point(
              coordinates: Position(currentLocation.longitude ?? 0,
                  currentLocation.latitude ?? 0))),
      MapAnimationOptions(),
    );
    await Future.delayed(const Duration(seconds: 1));

    Navigator.of(navigatorKey.currentContext!).pop();
  }

  Future<void> flyToLocation(double latitude, double longitude,
      {double zoom = 16}) async {
    if (_mapboxMap == null) return;

    _mapboxMap!.flyTo(
        CameraOptions(
            zoom: zoom,
            center: Point(coordinates: Position(longitude, latitude))),
        MapAnimationOptions());
  }

  Future<MapBoxLocationModel> reverseGeocode(
      CameraChangedEventData cameraChangedEventData) async {
    double lat =
        cameraChangedEventData.cameraState.center.coordinates.lat.toDouble();
    double lng =
        cameraChangedEventData.cameraState.center.coordinates.lng.toDouble();

    final String url =
        'https://api.mapbox.com/geocoding/v5/mapbox.places/$lng,$lat.json?access_token=${AppCred.mapBoxPublicTokenKey}&language=en';

    try {
      final response = await http.get(Uri.parse(url));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['features'] != null && data['features'].isNotEmpty) {
          return MapBoxLocationModel(
              point: MapBoxPoint(
                  longitude: data['features'][0]['center'][0],
                  latitude: data['features'][0]['center'][1]),
              address: data['features'][0]['place_name']);
        } else {
          return MapBoxLocationModel(
              point: MapBoxPoint(longitude: -2, latitude: -2), address: "-2");
        }
      } else {
        return MapBoxLocationModel(
            point: MapBoxPoint(longitude: -3, latitude: -3), address: "-3");
      }
    } catch (e) {
      HelperMethods.toast(Globals.language.errorMsg);
      return MapBoxLocationModel(
          point: MapBoxPoint(longitude: -4, latitude: -4), address: "-4");
    }
  }

  void resetInitialState() {
    _isInitial = true;
  }

  void release() {
    _referenceCount--;
    _debounce?.cancel();
    if (_referenceCount <= 0) {
      _dispose();
    }
  }

  void _dispose() {
    if (_annotationManagers != null) {
      MapBoxService.clearAnnotations(_annotationManagers!.pointManager);
      MapBoxService.clearAnnotations(_annotationManagers!.polylineManager);
      MapBoxService.clearAnnotations(_annotationManagers!.circleManager);
    }

    _address?.dispose();
    _address = null;

    _mapboxMap = null;
    _annotationManagers = null;
    _mapWidget = null;
    _isInitialized = false;
    _referenceCount = 0;
    onAddressChanged = null;
  }
}
