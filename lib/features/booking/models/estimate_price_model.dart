import 'package:rider/features/ride_flow/screen/manage_ride_stops_logic.dart';
import 'package:rider/global/models/response_model.dart';

import '../../../model/CouponData.dart';

class EstimatePriceResponseModel extends ResponseModel<List<ServicesListData>> {
  EstimatePriceResponseModel({
    required super.status,
    required super.message,
    required super.data,
  });

  // Factory constructor for creating a new CurrentRideResponseModel instance from a map.
  factory EstimatePriceResponseModel.fromJson(Map<String, dynamic> json) {
    return EstimatePriceResponseModel(
      status: json['status'],
      message: json['message'],
      data: json['data'] != null
          ? (json['data'] as List)
              .map((i) => ServicesListData.fromJson(i))
              .toList()
          : null,
    );
  }

  // Method to convert a CurrentRideResponseModel instance to a map.
  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'message': message,
      'data': data!.map((v) => v.toJson()).toList(),
    };
  }
}

class EstimatePriceModel {
  List<ServicesListData>? data;
  // PaginationModel? pagination;
  // String? message;
  // bool? status;

  EstimatePriceModel({
    this.data,
  });

  factory EstimatePriceModel.fromJson(Map<String, dynamic> json) {
    return EstimatePriceModel(
      data: json['data'] != null
          ? (json['data'] as List)
              .map((i) => ServicesListData.fromJson(i))
              .toList()
          : null,
      // pagination: json['pagination'] != null
      //     ? PaginationModel.fromJson(json['pagination'])
      //     : null,
      // message: json['message'],
      // status: json['status'],
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    // if (this.pagination != null) {
    //   data['pagination'] = this.pagination!.toJson();
    // }
    // data['message'] = this.message;
    return data;
  }
}

class ServiceListDataRequestData {
  num? baseFare;
  num? minimumFare;
  num? baseDistance;
  num? perDistance;
  num? perDistanceCharge;
  num? perMinuteDriveCharge;
  num? subtotal;
  num? totalAmount;
  num? extraCharges;
  num? finalAmount;
  num? extraChargesAmount;
  num? couponDiscount;
  num? distance;
  num? perMinuteDrive;
  num? perMinuteWaiting;
  num? perMinuteWaitingCharge;
  num? waitingTimeLimit;
  ServiceListDataRequestData({
    this.baseFare,
    this.minimumFare,
    this.baseDistance,
    this.perDistance,
    this.perDistanceCharge,
    this.perMinuteDriveCharge,
    this.extraCharges,
    this.finalAmount,
    this.subtotal,
    this.totalAmount,
    this.extraChargesAmount,
    this.couponDiscount,
    this.distance,
    this.perMinuteDrive,
    this.perMinuteWaiting,
    this.perMinuteWaitingCharge,
    this.waitingTimeLimit,
  });

  factory ServiceListDataRequestData.fromJson(Map<String, dynamic> json) {
    return ServiceListDataRequestData(
      baseFare: json['base_fare'],
      minimumFare: json['minimum_fare'],
      baseDistance: json['base_distance'],
      perDistance: json['per_distance'],
      perDistanceCharge: json['per_distance_charge'],
      perMinuteDriveCharge: json['per_minute_drive_charge'],
      extraCharges: json['extra_charges'],
      finalAmount: json['final_amount'],
      subtotal: json['subtotal'],
      totalAmount: json['total_amount'],
      extraChargesAmount: json['extra_charges_amount'],
      couponDiscount: json['coupon_discount'],
      distance: json['distance'],
      perMinuteDrive: json['per_minute_drive'],
      perMinuteWaiting: json['per_minute_waiting'],
      perMinuteWaitingCharge: json['per_minute_waiting_charge'],
      waitingTimeLimit: json['waiting_time_limit'],
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['base_fare'] = baseFare;
    data['minimum_fare'] = minimumFare;
    data['base_distance'] = baseDistance;
    data['per_distance'] = perDistance;
    data['per_distance_charge'] = perDistanceCharge;
    data['per_minute_drive_charge'] = perMinuteDriveCharge;
    data['final_amount'] = finalAmount;
    data['extra_charges'] = extraCharges;
    data['subtotal'] = subtotal;
    data['total_amount'] = totalAmount;
    data['extra_charges_amount'] = extraChargesAmount;
    data['coupon_discount'] = couponDiscount;
    data['distance'] = distance;
    data['per_minute_drive'] = perMinuteDrive;
    data['per_minute_waiting'] = perMinuteWaiting;
    data['per_minute_waiting_charge'] = perMinuteWaitingCharge;
    data['waiting_time_limit'] = waitingTimeLimit;
    return data;
  }
}

class ServicesListData {
  double? adminCommission;
  num? baseFare;
  num? cancellationFee;
  num? capacity;
  String? commissionType;
  String? createdAt;
  num? discountAmount;
  dynamic distance;
  dynamic distancePrice;
  num? duration;
  num? fleetCommission;
  num? id;
  dynamic minimumDistance;
  dynamic minimumDistanceInKm;
  num? minimumFare;
  String? name;
  String? paymentMethod;
  num? perDistance;
  num? perMinuteDrive;
  num? perMinuteWait;
  num? pickupDuration;
  Region? region;
  num? regionId;
  String? serviceImage;
  dynamic status;
  num? subtotal;
  num? timePrice;
  num? totalAmount;
  num? tax;
  num? couponDiscount;
  num? extraChargesAmount;
  num? finalAmount;
  String? updatedAt;
  num? waitDuration;
  num? waitingTimeLimit;
  String? startLatitude;
  String? startLongitude;
  String? startAddress;
  String? endLatitude;
  String? endLongitude;
  String? endAddress;
  CouponData? couponData;
  num? serviceId;
  String? description;
  ServiceListDataRequestData? requestData;

  ServicesListData(
      {this.adminCommission,
      this.baseFare,
      this.cancellationFee,
      this.capacity,
      this.commissionType,
      this.createdAt,
      this.discountAmount,
      this.distance,
      this.distancePrice,
      this.duration,
      this.fleetCommission,
      this.id,
      this.minimumDistance,
      this.minimumDistanceInKm,
      this.minimumFare,
      this.name,
      this.paymentMethod,
      this.perDistance,
      this.perMinuteDrive,
      this.perMinuteWait,
      this.pickupDuration,
      this.region,
      this.regionId,
      this.serviceImage,
      this.status,
      this.subtotal,
      this.timePrice,
      this.totalAmount,
      this.tax,
      this.updatedAt,
      this.waitDuration,
      this.waitingTimeLimit,
      this.couponData,
      this.endAddress,
      this.endLongitude,
      this.startAddress,
      this.startLatitude,
      this.startLongitude,
      this.serviceId,
      this.endLatitude,
      this.description,
      this.requestData,
      this.extraChargesAmount,
      this.finalAmount,
      this.couponDiscount});

  factory ServicesListData.fromJson(Map<String, dynamic> json) {
    return ServicesListData(
      adminCommission: json['admin_commission'],
      baseFare: json['base_fare'],
      extraChargesAmount: json['extra_charges_amount'],
      finalAmount: json['final_amount'],
      couponDiscount: json['coupon_discount'],
      cancellationFee: json['cancellation_fee'],
      capacity: json['capacity'],
      commissionType: json['commission_type'],
      createdAt: json['created_at'],
      discountAmount: json['discount_amount'],
      distance: json['distance'],
      distancePrice: json['distance_price'],
      duration: json['duration'],
      fleetCommission: json['fleet_commission'],
      id: json['id'],
      minimumDistance: json['minimum_distance'],
      minimumDistanceInKm: json['minimum_distance_in_km'],
      minimumFare: json['minimum_fare'],
      name: json['name'],
      paymentMethod: json['payment_method'],
      perDistance: json['per_distance'],
      perMinuteDrive: json['per_minute_drive'],
      perMinuteWait: json['per_minute_wait'],
      pickupDuration: json['pickup_duration'],
      region: json['region'] != null ? Region.fromJson(json['region']) : null,
      regionId: json['region_id'],
      serviceImage: json['service_image'],
      status: json['status'],
      subtotal: json['subtotal'],
      timePrice: json['time_price'],
      totalAmount: json['total_amount'],
      tax: json['tax'],
      updatedAt: json['updated_at'],
      waitDuration: json['wait_duration'],
      waitingTimeLimit: json['waiting_time_limit'],
      serviceId: json['service_id'],
      endAddress: json['end_address'],
      endLongitude: json['end_longitude'],
      startAddress: json['start_address'],
      startLatitude: json['start_latitude'],
      startLongitude: json['start_longitude'],
      endLatitude: json['end_latitude'],
      description: json['description'],
      couponData: json['coupon_data'] != null
          ? CouponData.fromJson(json['coupon_data'])
          : null,
      requestData: json['request_data'] != null
          ? ServiceListDataRequestData.fromJson(
              json['request_data'],
            )
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['admin_commission'] = adminCommission;
    data['base_fare'] = baseFare;
    data['cancellation_fee'] = cancellationFee;
    data['capacity'] = capacity;
    data['commission_type'] = commissionType;
    data['created_at'] = createdAt;
    data['discount_amount'] = discountAmount;
    data['distance'] = distance;
    data['distance_price'] = distancePrice;
    data['duration'] = duration;
    data['fleet_commission'] = fleetCommission;
    data['id'] = id;
    data['minimum_distance'] = minimumDistance;
    data['minimum_distance_in_km'] = minimumDistanceInKm;
    data['minimum_fare'] = minimumFare;
    data['name'] = name;
    data['payment_method'] = paymentMethod;
    data['per_distance'] = perDistance;
    data['per_minute_drive'] = perMinuteDrive;
    data['per_minute_wait'] = perMinuteWait;
    data['pickup_duration'] = pickupDuration;
    data['region_id'] = regionId;
    data['service_image'] = serviceImage;
    data['status'] = status;
    data['subtotal'] = subtotal;
    data['time_price'] = timePrice;
    data['total_amount'] = totalAmount;
    data['tax'] = tax;
    data['updated_at'] = updatedAt;
    data['wait_duration'] = waitDuration;
    data['waiting_time_limit'] = waitingTimeLimit;
    data['service_id'] = serviceId;
    data['start_address'] = startAddress;
    data['start_latitude'] = startLatitude;
    data['start_longitude'] = startLongitude;
    data['end_address'] = endAddress;
    data['end_latitude'] = endLatitude;
    data['end_longitude'] = endLongitude;
    data['service_id'] = serviceId;
    data['description'] = description;
    if (region != null) {
      data['region'] = region!.toJson();
    }
    if (couponData != null) {
      data['coupon_data'] = region!.toJson();
    }
    return data;
  }
}

class Region {
  String? createdAt;
  String? distanceUnit;
  int? id;
  String? name;
  int? status;
  String? timezone;
  String? updatedAt;

  Region(
      {this.createdAt,
      this.distanceUnit,
      this.id,
      this.name,
      this.status,
      this.timezone,
      this.updatedAt});

  factory Region.fromJson(Map<String, dynamic> json) {
    return Region(
      createdAt: json['created_at'],
      distanceUnit: json['distance_unit'],
      id: json['id'],
      name: json['name'],
      status: json['status'],
      timezone: json['timezone'],
      updatedAt: json['updated_at'],
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['created_at'] = createdAt;
    data['distance_unit'] = distanceUnit;
    data['id'] = id;
    data['name'] = name;
    data['status'] = status;
    data['timezone'] = timezone;
    data['updated_at'] = updatedAt;
    return data;
  }
}

class EstimatePriceData {
  num baseFare;
  num perDistanceCharge;
  num couponDiscount;
  num distance;
  num tax;
  num totalAmount;
  CouponData? couponData;
  bool isMinimumAmountApplies;
  num stripe;
  num airportCharges;
  num duration;

  EstimatePriceData({
    required this.baseFare,
    required this.perDistanceCharge,
    required this.couponDiscount,
    required this.distance,
    required this.tax,
    required this.totalAmount,
    this.couponData,
    required this.isMinimumAmountApplies,
    required this.stripe,
    required this.airportCharges,
      required this.duration,
  });

  factory EstimatePriceData.fromJson(Map<String, dynamic> json) {
    return EstimatePriceData(
      baseFare: json['base_fare'] as num,
      perDistanceCharge: json['per_distance_charge'] as num,
      couponDiscount: json['coupon_discount'] as num,
      distance: json['distance'] as num,
      tax: json['tax'] as num,
      totalAmount: json['total_amount'] as num,
      couponData: json['coupon_data'] != null
          ? CouponData.fromJson(json['coupon_data'])
          : null,
      isMinimumAmountApplies: json['is_minimum_amount_applies'] as bool,
      stripe: json['stripe'] as num,
      airportCharges: json['airport_charges'] as num,
      duration: json['duration'] as num,
    );
  }
}

class EstimatePriceResponse extends ApiBaseResponse<List<EstimatePriceData>> {
  EstimatePriceResponse(
      {required super.status, required super.message, required super.data});
  factory EstimatePriceResponse.fromJson(Map<String, dynamic> json) {
    List<EstimatePriceData> data = [];

    if (json["data"] != null) {
      if (json["data"] is List && json["data"][0]["status"] == false) {
        return EstimatePriceResponse(
            status: false,
            message: json["data"][0]["message"] ?? "Something went wrong",
            data: null);
      }
    }
    if (json['data'] != null && json['data'] is List) {
      for (var i = 0; i < json['data'].length; i++) {
        data.add(
          EstimatePriceData.fromJson(
            json['data'][i]['request_data'],
          ),
        );
      }
    }
    return EstimatePriceResponse(
      status: json['status'],
      message: json['message'] ?? "",
      data: data,
    );
  }
}

