import 'package:rider/global/models/response_model.dart';
import 'package:rider/global/models/ride_model.dart';

class CurrentRideResponseModel extends ResponseModel<RideModel> {
  CurrentRideResponseModel({
    required super.status,
    required String super.message,
    required super.data,
  });

  // Factory constructor for creating a new CurrentRideResponseModel instance from a map.
  factory CurrentRideResponseModel.fromJson(Map<String, dynamic> json) {
    return CurrentRideResponseModel(
      status: json['status'],
      message: json['message'],
      data: json['data'] != null ? RideModel.fromJson(json['data']) : null,
    );
  }
}
