import 'package:flutter/material.dart';
import 'package:rider/app_exports.dart';

class SaveRideModel {
  final String? holdPaymentId;
  final String? paymentCardId;
  final String riderId;
  final String serviceId;
  final String startLatitude;
  final String startLongitude;
  final String startAddress;
  final String endLatitude;
  final String endLongitude;
  final String endAddress;
  final String status;
  final String paymentType;
  final String? couponCode;
  final bool isRideForOther;
  final Map<String, dynamic>? otherRiderData;
  final num? baseFare;
  final num? minimumFare;
  final num? baseDistance;
  final num? perDistance;
  final num? perDistanceCharge;
  final num? perMinuteDriveCharge;
  final num totalAmount;
  final num extraCharges;
  final num tax;
  final num finalAmount;
  final num? subtotal;
  final num? extraChargesAmount;
  final num? couponDiscount;
  final CouponData? couponData;
  final num? distance;
  final num? perMinuteDrive;
  final num? perMinuteWaiting;
  final num? perMinuteWaitingCharge;
  final num? waitingTimeLimit;
  final bool isScheduledRide;
  final DateTime? scheduledDate;
  final TimeOfDay? scheduledTime;
  final num? debitWallet;
  final bool isOTPSharingRequired;
  final bool isPoolingRide;
  final bool isBusinessRide;
  final bool peak_ride;
  final int personCount;
  final bool isMinimumAmountApplies;
  final num stripe;
  final num airportCharges;
  final num duration;

  SaveRideModel({
    required this.debitWallet,
    required this.isPoolingRide,
    required this.isBusinessRide,
    required this.personCount,
    this.holdPaymentId,
    required this.paymentCardId,
    required this.riderId,
    required this.serviceId,
    required this.startLatitude,
    required this.startLongitude,
    required this.startAddress,
    required this.endLatitude,
    required this.endLongitude,
    required this.endAddress,
    required this.status,
    required this.paymentType,
    this.couponCode,
    required this.isRideForOther,
    required this.otherRiderData,
    required this.baseFare,
    required this.minimumFare,
    required this.baseDistance,
    required this.perDistance,
    required this.perDistanceCharge,
    required this.perMinuteDriveCharge,
    required this.totalAmount,
    required this.extraCharges,
    required this.tax,
    required this.finalAmount,
    required this.subtotal,
    required this.extraChargesAmount,
    this.couponDiscount,
    this.couponData,
    required this.distance,
    required this.perMinuteDrive,
    required this.perMinuteWaiting,
    required this.perMinuteWaitingCharge,
    required this.waitingTimeLimit,
    required this.isScheduledRide,
    required this.isOTPSharingRequired,
    this.scheduledDate,
    this.scheduledTime,
    required this.peak_ride,
    required this.isMinimumAmountApplies,
    required this.stripe,
    required this.airportCharges,
    required this.duration,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['hold_payment_id'] = holdPaymentId;
    data['peak_ride'] = peak_ride;

    data['is_pool'] = isPoolingRide;
    data['is_business_ride'] = isBusinessRide;
    data['person_count'] = personCount;
    data['debitwallet'] = debitWallet;

    data['payment_card_id'] = paymentCardId;
    data['rider_id'] = riderId;
    data['service_id'] = serviceId;
    data['start_latitude'] = startLatitude;
    data['start_longitude'] = startLongitude;
    data['start_address'] = startAddress;
    data['end_latitude'] = endLatitude;
    data['end_longitude'] = endLongitude;
    data['end_address'] = endAddress;
    data['is_otp_enable'] = isOTPSharingRequired;

    data['status'] = status;
    data['payment_type'] = paymentType;
    if (couponCode != null) {
      data['coupon_code'] = couponCode;
    }
    if (isRideForOther) {
      data['is_ride_for_other'] = isRideForOther;
      data['other_rider_data'] = otherRiderData;
    }
    data['base_fare'] = baseFare;
    data['minimum_fare'] = minimumFare;
    data['base_distance'] = baseDistance;
    data['per_distance'] = perDistance;
    data['per_distance_charge'] = perDistanceCharge;
    data['per_minute_drive_charge'] = perMinuteDriveCharge;
    data['total_amount'] = totalAmount;
    data['extra_charges'] = extraCharges;
    data['tax'] = tax;
    data['final_amount'] = finalAmount;
    data['subtotal'] = subtotal;
    data['extra_charges_amount'] = extraChargesAmount;
    data['coupon_discount'] = couponDiscount;
    data['coupon_data'] = couponData?.toJson();
    data['distance'] = distance;
    data['per_minute_drive'] = perMinuteDrive;
    data['per_minute_waiting'] = perMinuteWaiting;
    data['per_minute_waiting_charge'] = perMinuteWaitingCharge;
    data['waiting_time_limit'] = waitingTimeLimit;
    if (isScheduledRide) {
      data['is_schedule'] = '1';
      data['datetime'] = scheduledDate.toString();
    } else {
      data['is_schedule'] = '0';
      data['datetime'] = DateTime.now().toString();
    }
    data['is_minimum_amount_applies'] = isMinimumAmountApplies;
    data['stripe'] = stripe;
    data['airport_charges'] = airportCharges;
    data['duration'] = duration;
    return data;
  }

  factory SaveRideModel.fromJson(Map<String, dynamic> json) => SaveRideModel(
        holdPaymentId: json['hold_payment_id'],
        peak_ride: json['peak_ride'],
        paymentCardId: json['payment_card_id'],
        riderId: json['rider_id'],
        serviceId: json['service_id'],
        startLatitude: json['start_latitude'],
        startLongitude: json['start_longitude'],
        startAddress: json['start_address'],
        endLatitude: json['end_latitude'],
        endLongitude: json['end_longitude'],
        debitWallet: json['debitwallet'],
        isOTPSharingRequired: json['is_otp_enable'],
        endAddress: json['end_address'],
        status: json['status'],
        paymentType: json['payment_type'],
        couponCode: json['coupon_code'],
        isRideForOther: json['is_ride_for_other'],
        otherRiderData: json['other_rider_data'] != null
            ? Map<String, dynamic>.from(json['other_rider_data'])
            : {},
        baseFare: json['base_fare'],
        minimumFare: json['minimum_fare'],
        baseDistance: json['base_distance'],
        perDistance: json['per_distance'],
        perDistanceCharge: json['per_distance_charge'],
        perMinuteDriveCharge: json['per_minute_drive_charge'],
        totalAmount: json['total_amount'],
        extraCharges: json['extra_charges'],
        tax: json['tax'],
        finalAmount: json['final_amount'],
        subtotal: json['subtotal'],
        extraChargesAmount: json['extra_charges_amount'],
        couponDiscount: json['coupon_discount'],
        couponData: json['coupon_data'] == null
            ? null
            : CouponData.fromJson(json['coupon_data']),
        distance: json['distance'],
        perMinuteDrive: json['per_minute_drive'],
        perMinuteWaiting: json['per_minute_waiting'],
        isPoolingRide: json['is_pool'],
        isBusinessRide: json['is_business_ride'],
        personCount: json['person_count'],
        perMinuteWaitingCharge: json['per_minute_waiting_charge'],
        waitingTimeLimit: json['waiting_time_limit'],
        isScheduledRide: json['is_schedule'] == '1',
        scheduledDate: DateTime.parse(json['datetime']),
        scheduledTime: TimeOfDay.now(),
        isMinimumAmountApplies: json['is_minimum_amount_applies'],
        stripe: json['stripe'],
        airportCharges: json['airport_charges'],
        duration: json['duration'],
      );
}
