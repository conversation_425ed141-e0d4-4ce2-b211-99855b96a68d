import 'package:rider/features/verify_otp/models/validat_otp_response_model.dart';
import 'package:rider/network/network_utils.dart';

class VerifyOtpRepository {
  Future<ValidateOtpResponseModel> validateOtpApi(
      {required Map request}) async {
    return ValidateOtpResponseModel.fromJson(await handleResponse(
        await buildHttpResponse('validate-otp',
            request: request, method: HttpMethod.post)));
  }

  Future<ValidateOtpResponseModel> resendOtpApi({required Map request}) async {
    return ValidateOtpResponseModel.fromJson(await handleResponse(
        await buildHttpResponse('send-otp',
            request: request, method: HttpMethod.post)));
  }
//   Future<dynamic> resendOtpApi({required String phone}) async {
//   Response response = await buildHttpResponse('send-otp',
//       request: {'contact_number': phone, "user_type": "driver"},
//       method: HttpMethod.POST);

//   if ((response.statusCode >= 200 && response.statusCode <= 206)) {
//     if (response.body.isJson()) {
//       var json = jsonDecode(response.body);

//       return json;
//     }
//     return null;
//   } else {
//     return null;
//   }
// }
}
