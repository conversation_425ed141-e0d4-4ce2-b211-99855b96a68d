import 'package:rider/features/verify_otp/models/validate_otp_data_model.dart';

class ValidateOtpResponseModel {
  String? message;
  bool? status;
  ValidateOtpDataModel? data;

  ValidateOtpResponseModel({this.message, this.status, this.data});

  ValidateOtpResponseModel.fromJson(Map<String, dynamic> json) {
    message = json['message'];
    status = json['status'];
    data = json['data'] != null
        ? ValidateOtpDataModel.fromJson(json['data'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['message'] = message;
    data['status'] = status;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}
