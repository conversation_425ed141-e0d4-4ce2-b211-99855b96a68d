// class WebViewDataModel {
//   int id;
//   String title;
//   String ?description;

  

//   WebViewDataModel({
//     required this.id,
//     this.description,
//     required this.title,
//   });

//   factory WebViewDataModel.fromJson(Map<String, dynamic> json) {
//     return WebViewDataModel(
//       id: json['id'],
//       title: json['title'],
//       description: json['description'],
//     );
//   }
// }