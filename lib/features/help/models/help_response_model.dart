import 'package:rider/model/FAQ.dart';

import '../../../model/PaginationModel.dart';

class WebViewDataResponseModel {
  PaginationModel? pagination;
  List<WebViewDataModel>? data;
  String? message;

  WebViewDataResponseModel({this.data, this.pagination, this.message});

  factory WebViewDataResponseModel.fromJson(Map<String, dynamic> json) {
    return WebViewDataResponseModel(
        pagination: json["pagination"] != null
            ? PaginationModel.fromJson(json["pagination"])
            : null,
        data: json["data"] != null
            ? (json["data"] as List)
                .map((e) => WebViewDataModel.fromJson(e))
                .toList()
            : null,
        message: json["message"]);
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> datas = <String, dynamic>{};
    datas["pagination"] = pagination;
    datas["data"] = data;
    datas["message"] = message;
    return datas;
  }
}
