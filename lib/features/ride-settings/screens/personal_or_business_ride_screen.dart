import 'package:mapbox_maps_flutter/mapbox_maps_flutter.dart' as mp;
import 'package:rider/app_exports.dart';
import 'package:rider/features/ride-settings/models/business_profile.dart';
import 'package:rider/features/ride-settings/screens/business_profile_settings_screen.dart';

class PersonalOrBusinessRideScreen extends StatefulWidget {
  final mp.Position pickupLocation;
  final mp.Position destinlationLocation;
  final String sourceAddress;
  final String destinationAddress;
  final DateTime? scheduledTime;
  final bool isPoolingRide;
  final int personCount;
  const PersonalOrBusinessRideScreen({
    super.key,
    required this.pickupLocation,
    required this.destinlationLocation,
    required this.sourceAddress,
    required this.destinationAddress,
    required this.scheduledTime,
    required this.isPoolingRide,
    required this.personCount,
  });

  @override
  State<PersonalOrBusinessRideScreen> createState() =>
      _PersonalOrBusinessRideScreenState();
}

class _PersonalOrBusinessRideScreenState
    extends State<PersonalOrBusinessRideScreen> {
  BusinessProfile _businessProfile = BusinessProfile(
    name: "",
    email: "",
  );
  bool _isBusinessRide = false;

  @override
  void initState() {
    _initWork();
    super.initState();
  }

  @override
  void dispose() {
    hideAppActivity();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const RoooAppbar(title: "Ride type"),
      bottomNavigationBar: Visibility(
        visible: !isAppActivityRunning.value,
        child: BottomButton(
          text: "Next",
          onPressed: () {
            _goNext();
          },
        ),
      ),
      body: Stack(
        children: [
          ListView(
            padding: const EdgeInsets.all(Layout.scaffoldBodyPadding),
            children: [
              const Text(
                  "Is this ride intended for personal or business purposes?"),
              height10,
              _buildRideTypeToggle(),
              height10,
              _getBusinessRideView(),
            ],
          ),
          const ActivityIndicator(),
        ],
      ),
    );
  }

  Future<void> _initWork() async {
    showAppActivity();
    var response = await getRideSettings();
    if (!response.status) {
      toast(response.message);
      return;
    }
    hideAppActivity();
    if (response.data!.businessProfile != null) {
      _businessProfile = response.data!.businessProfile!;
    }
    setState(() {});
  }

  Widget _buildRideTypeToggle() {
    return Container(
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: Colors.grey.shade100,
      ),
      child: Row(
        children: [
          Expanded(
            child: _buildToggleOption(
              icon: Icons.person,
              label: "Personal",
              isSelected: !_isBusinessRide,
              onTap: () {
                setState(() {
                  _isBusinessRide = false;
                });
              },
            ),
          ),
          Expanded(
            child: _buildToggleOption(
              icon: Icons.business_center,
              label: "Business",
              isSelected: _isBusinessRide,
              onTap: () {
                if (_businessProfile.name.isEmpty) {
                  _askForBusinessDetails();
                } else {
                  setState(() {
                    _isBusinessRide = true;
                  });
                }
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildToggleOption({
    required IconData icon,
    required String label,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 12),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: isSelected
              ? Theme.of(context).primaryColor
              : Colors.transparent,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 24,
              color: isSelected
                  ? Colors.white
                  : Colors.grey.shade600,
            ),
            height8,
            Text(
              label,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: isSelected
                    ? Colors.white
                    : Colors.grey.shade600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _getBusinessRideView() {
    if (!_isBusinessRide) {
      return const SizedBox();
    }
    return Center(
      child: Card(
        child: Container(
          padding: const EdgeInsets.all(
            Layout.scaffoldBodyPadding,
          ),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10),
            color: Colors.grey.shade200,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                "Business Name",
                style: TextStyle(
                  color: Colors.black,
                ),
              ),
              height10,
              Text(
                _businessProfile.name,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 19,
                  color: Colors.black,
                ),
              ),
              height10,
              const Text(
                "Email",
                style: TextStyle(
                  color: Colors.black,
                ),
              ),
              height10,
              Text(
                _businessProfile.email,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 19,
                  color: Colors.black,
                ),
              ),
              height10,
              Center(
                child: AppButtonWidget(
                  fullWidth: false,
                  text: "Want to change?",
                  onTap: _openEditScreen,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _askForBusinessDetails() async {
    bool? granted = await showAppDialog(
      positiveButtonText: "Ok",
      onAccept: () {
        Navigator.of(context).pop(true);
      },
      dialogType: AppDialogType.confirmation,
      title:
          "Your business details are incomplete, Please complete your business details",
    );
    if (granted == true) {
      _openEditScreen();
    }
  }

  Future<void> _openEditScreen() async {
    BusinessProfile? profile = await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => BusinessProfileSettingsScreen(
          businessProfile: _businessProfile,
          isFromRideBooking: true,
        ),
      ),
    );
    if (profile != null) {
      setState(() {
        _businessProfile = profile;
        _isBusinessRide = true;
      });
    }
  }

  Future<void> _goNext() async {
    launchScreen(
      SelectServiceScreen(
        sourceAddress: widget.sourceAddress,
        destinationAddress: widget.destinationAddress,
        pickupLocation: widget.pickupLocation,
        destinlationLocation: widget.destinlationLocation,
        scheduledTime: widget.scheduledTime,
        isPoolingRide: false,
        personCount: widget.personCount,
        isBusinessRide: _isBusinessRide,
      ),
    );
  }
}
