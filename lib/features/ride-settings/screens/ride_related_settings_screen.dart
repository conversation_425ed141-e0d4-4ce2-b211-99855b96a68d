import 'package:rider/app_exports.dart';
import 'package:rider/features/ride-settings/models/business_profile.dart';
import 'package:rider/features/ride-settings/models/otp_settings_request.dart';
import 'package:rider/features/ride-settings/models/ride_related_settings.dart';
import 'package:rider/features/ride-settings/screens/business_profile_settings_screen.dart';
import 'package:rider/features/ride-settings/screens/login_setting_screen.dart';
import 'package:rider/features/ride-settings/screens/ride_otp_settings_screen.dart';

class RideRelatedSettingsScreen extends StatefulWidget {
  const RideRelatedSettingsScreen({super.key});

  @override
  State<RideRelatedSettingsScreen> createState() =>
      _RideRelatedSettingsScreenState();
}

class _RideRelatedSettingsScreenState extends State<RideRelatedSettingsScreen> {
  final RideRelatedSettings _rideSettings = RideRelatedSettings(
    otpSettings: RideOTPSetting(
      shareOTPDuringRide: true,
    ),
    businessProfile: BusinessProfile(
      name: "",
      email: "",
    ),
  );

  @override
  void initState() {
    _initWork();
    super.initState();
  }

  @override
  void dispose() {
    hideAppActivity();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: const RoooAppbar(title: "Ride Settings"),
        body: Stack(
          children: [
            ListView(
              padding: const EdgeInsets.all(
                Layout.scaffoldBodyPadding,
              ),
              children: [
                ListTile(
                  title: const Text(
                    "OTP Settings",
                    style: TextStyle(
                      color: Colors.black,
                    ),
                  ),
                  tileColor: Colors.grey.shade200,
                  onTap: () {
                    launchScreen(RideOtpSettingsScreen(
                      otpSettings: _rideSettings.otpSettings,
                    ));
                  },
                ),
                const SizedBox(
                  height: 10,
                ),
                ListTile(
                  title: const Text(
                    "Business Profile Settings",
                    style: TextStyle(
                      color: Colors.black,
                    ),
                  ),
                  tileColor: Colors.grey.shade200,
                  onTap: () {
                    launchScreen(BusinessProfileSettingsScreen(
                      businessProfile: _rideSettings.businessProfile,
                    ));
                  },
                ),

                  const SizedBox(
                  height: 10,
                ),
                ListTile(
                  title: const Text(
                    "Login Setting",
                    style: TextStyle(
                      color: Colors.black,
                    ),
                  ),
                  tileColor: Colors.grey.shade200,
                  onTap: () {
                    launchScreen(LoginSettingsScreen(
                    ));
                  },
                ),
              ],
            ),
            const ActivityIndicator(),
          ],
        ));
  }

  Future<void> _initWork() async {
    showAppActivity();
    var response = await getRideSettings();
    if (!response.status) {
      toast(response.message);
      return;
    }
    if (response.data!.otpSettings != null) {
      _rideSettings.otpSettings = response.data!.otpSettings;
    } else {
      _rideSettings.otpSettings!.shareOTPDuringRide = false;
    }

    if (response.data!.businessProfile != null) {
      _rideSettings.businessProfile = response.data!.businessProfile;
    }

    hideAppActivity();
    setState(() {
      // update
    });
  }
}
