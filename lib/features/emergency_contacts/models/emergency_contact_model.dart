class EmergencyContactModel {
  int? addedBy;
  String? contactNumber;
  String? createdAt;
  int? id;
  int? regionId;
  int? status;
  String? title;
  String? updatedAt;

  EmergencyContactModel({
    this.addedBy,
    this.contactNumber,
    this.createdAt,
    this.id,
    this.regionId,
    this.status,
    this.title,
    this.updatedAt,
  });

  factory EmergencyContactModel.fromJson(Map<String, dynamic> json) {
    return EmergencyContactModel(
      addedBy: json['added_by'],
      contactNumber: json['contact_number'],
      createdAt: json['created_at'],
      id: json['id'],
      regionId: json['region_id'],
      status: json['status'],
      title: json['title'],
      updatedAt: json['updated_at'],
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['added_by'] = addedBy;
    data['contact_number'] = contactNumber;
    data['created_at'] = createdAt;
    data['id'] = id;
    data['region_id'] = regionId;
    data['status'] = status;
    data['title'] = title;
    data['updated_at'] = updatedAt;
    return data;
  }
}
