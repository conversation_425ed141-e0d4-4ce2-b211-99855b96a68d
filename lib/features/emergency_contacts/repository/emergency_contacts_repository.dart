import '../../../network/network_utils.dart';
import '../models/emergency_contact_response_model.dart';

class EmergencyContactsRepository {
  Future<EmergencyContactResponseModel> saveSOS({required Map request}) async {
    return EmergencyContactResponseModel.fromJson(await handleResponse(
        await buildHttpResponse('save-sos',
            method: HttpMethod.post, request: request)));
  }

  Future<EmergencyContactResponseModel> getSosList(
      {required int currentPage}) async {
    return EmergencyContactResponseModel.fromJson(await handleResponse(
        await buildHttpResponse('sos-list', method: HttpMethod.get)));
  }

  Future<EmergencyContactResponseModel> deleteSosList({int? id}) async {
    return EmergencyContactResponseModel.fromJson(await handleResponse(
        await buildHttpResponse('sos-delete/$id', method: HttpMethod.post)));
  }
}
