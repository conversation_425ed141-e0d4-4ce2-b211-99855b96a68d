import 'package:rider/features/care/models/carelist_models.dart';
import 'package:rider/model/PaginationModel.dart';

class CareResponseModel {
  PaginationModel? pagination;
  List<CareModel>? data;
  String? message;

  CareResponseModel({this.data, this.pagination, this.message});

  factory CareResponseModel.fromJson(Map<String, dynamic> json) {
    return CareResponseModel(
        pagination: json["pagination"] != null
            ? PaginationModel.fromJson(json["pagination"])
            : null,
        data: json["data"] != null
            ? (json["data"] as List).map((e) => CareModel.fromJson(e)).toList()
            : null,
        message: json["message"]);
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> datas = <String, dynamic>{};
    datas["pagination"] = pagination;
    datas["data"] = data;
    datas["message"] = message;
    return datas;
  }
}
