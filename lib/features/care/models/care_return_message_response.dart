import 'package:rider/features/care/models/care_details_response_model.dart';

class CareReturnMessageResponse {
  String? message;
  CareComment? careComment;
  bool? status;

  CareReturnMessageResponse({this.message, this.careComment, this.status});

  CareReturnMessageResponse.fromJson(Map<String, dynamic> json) {
    message = json['message'];
    careComment =
        json['data'] != null ? CareComment.fromMap(json['data']) : null;
    status = json['status'];
  }
}

class Complaint {
  int? id;
  int? driverId;
  String? subject;
  String? message;
  int? status;
  String? createdBy;
  String? createdAt;
  String? updatedAt;
  Null deletedAt;

  Complaint(
      {this.id,
      this.driverId,
      this.subject,
      this.message,
      this.status,
      this.createdBy,
      this.createdAt,
      this.updatedAt,
      this.deletedAt});

  Complaint.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    driverId = json['driver_id'];
    subject = json['subject'];
    message = json['message'];
    status = json['status'];
    createdBy = json['created_by'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    deletedAt = json['deleted_at'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['driver_id'] = driverId;
    data['subject'] = subject;
    data['message'] = message;
    data['status'] = status;
    data['created_by'] = createdBy;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    data['deleted_at'] = deletedAt;
    return data;
  }
}
