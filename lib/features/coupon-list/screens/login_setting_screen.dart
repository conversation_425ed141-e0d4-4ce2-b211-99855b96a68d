import 'package:rider/app_exports.dart';
import 'package:rider/features/ride-settings/models/otp_settings_request.dart';
import 'package:rider/features/ride-settings/models/ride_related_settings.dart';

class LoginSettingsScreen extends StatefulWidget {
  const LoginSettingsScreen({
    super.key,
  });

  @override
  State<LoginSettingsScreen> createState() => _LoginSettingsScreenState();
}

class _LoginSettingsScreenState extends State<LoginSettingsScreen> {


  String _OTP="otp";
  String _PASSWORD="password";
    String _selectedSetting="password";



  @override
  void initState() {
_selectedSetting=Globals.user.login_screen??_PASSWORD;
    super.initState();
  }

  @override
  void dispose() {
    hideAppActivity();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        hideKeyboard();
      },
      child: Scaffold(
        appBar: const RoooAppbar(title: "Ride OTP Settings"),
        body: Stack(
          children: [
            ListView(
              padding: const EdgeInsets.all(
                Layout.scaffoldBodyPadding,
              ),
              children: [
   

                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text("Login with OTP / \nLogin with password",style: AppTextStyles.title,),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Switch(
                          
                          
                          inactiveTrackColor: Colors.yellow,
                          value:_selectedSetting== _OTP, onChanged: (v){
                        
                          if(_selectedSetting==_OTP){
                        
                            _selectedSetting=_PASSWORD;

                            _update(_selectedSetting);
                        
                          }else if(_selectedSetting==_PASSWORD){
                                                    _selectedSetting=_OTP;
                                                    _update(_selectedSetting);
                        
                          }

                          setState(() {
                            
                          });
                        
                        
                        }),

                        height10,
                        Text(_selectedSetting==_OTP?"OTP":"Password",style: AppTextStyles.header,)
                      ],
                    )

                  ],
                ),
       
              ],
            ),
            const ActivityIndicator()
          ],
        ),
      ),
    );
  }

  Future<void> _update(String setting) async {
    showAppActivity();
    Map<String,dynamic>request={"login_screen":setting};
    var response = await updateUserSettings(
        request:request);
    hideAppActivity();
    if (!response.status) {
      toast(response.message);
      return;
    }
    toast(response.message);
      Globals.user.login_screen=_selectedSetting;
    Navigator.pop(context);
  
    setState(() {
      // _otpSettings.shareOTPDuringRide = value;
    });
  }
}
