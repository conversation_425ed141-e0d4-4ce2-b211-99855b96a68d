import 'package:rider/app_exports.dart';
import 'package:rider/features/coupon-list/models/coupon_list_model.dart';
import 'package:rider/model/PaginationModel.dart';
import 'package:rider/screens/SimpleURLWebViewScreen.dart';

import 'package:flutter/services.dart';

class CouponListScreen extends StatefulWidget {
  const CouponListScreen({super.key});

  @override
  State<CouponListScreen> createState() => _CouponListScreenState();
}

class _CouponListScreenState extends State<CouponListScreen> {
  CouponModel couponData = CouponModel(
    data: [],
    pagination: PaginationModel(),
  );

  ScrollController scrollController = ScrollController();

  int nextPage = 1;
  int totalPage = 1;
  int riderId = 0;
  bool isMoreData = true;
  bool isNoData = false;
  String emptyDataMsg = '';

  @override
  void initState() {
    riderId = Globals.user.id;

    scrollController.addListener(() {
      if (scrollController.position.pixels ==
          scrollController.position.maxScrollExtent) {
        if (isMoreData) {
          log('nct->loading more');
          getData();
        } else {
          log('nct->no more data');
        }
      }
    });

    getData();
    super.initState();
  }

  @override
  void dispose() {
    hideAppActivity();
    super.dispose();
    if (Platform.isIOS) {
      SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle.dark);
    }
  }

  Future<void> getData() async {
    showAppActivity();
    getCouponListApi().then((value) {
      if (value != null) {
        totalPage = value.pagination!.totalPages!;

        if (nextPage == 1) {
          couponData = value;
        } else {
          couponData.data.addAll(
            value.data,
          );
        }
        nextPage = nextPage + 1;

        emptyDataMsg = couponData.message ?? '';
        setState(() {
          isMoreData = nextPage <= totalPage;
          isNoData = couponData.data.isEmpty;
        });
        hideAppActivity();
      } else {
        showErrorToast();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: RoooAppbar(
        title: "Promotional coupons",
        isDarkOverlay: false,
      ),
      body: Stack(
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Padding(
              //   padding: EdgeInsets.only(top: 20, left: 20),
              //   child: Text(
              //     'All Messages',
              //     style: TextStyle(color: Colors.grey),
              //   ),
              // ),

              Expanded(
                child: ListView.separated(
                  physics: const AlwaysScrollableScrollPhysics(),
                  controller: scrollController,
                  padding: const EdgeInsets.all(
                    Layout.scaffoldBodyPadding,
                  ),
                  itemBuilder: (context, index) {
                    CouponModelData data = couponData.data[index];
                    // if (!isMoreData && index == inboxData.data.length - 1) {
                    //   // return Padding(
                    //   //   padding: const EdgeInsets.only(bottom: 8.0),
                    //   //   child: Center(
                    //   //     child: Text(
                    //   //       Globals.language.noMoreData,
                    //   //       style: TextStyle(
                    //   //
                    //   //       ),
                    //   //     ),
                    //   //   ),
                    //   // );
                    // } else if (inboxData.data.isNotEmpty) {
                    return Container(
                      decoration: BoxDecoration(
                        border: Border.all(color:
                               Theme.of(context).primaryColor.withOpacity(.5)),
                          borderRadius: BorderRadius.circular(8),
                          // color:
                          //     Theme.of(context).primaryColor.withOpacity(.5)
                              ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                data.code,
                                style: TextStyle(
                                    color: Theme.of(context).brightness == Brightness.light ?Colors.black : Colors.white,
                                    fontSize: 25,
                                    fontWeight: FontWeight.bold),
                              ),

                            ],
                          ),
                                Divider(color: Theme.of(context).primaryColor.withOpacity(.5) ,),
                          height20,
                          Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: Column(
                              children: [

                                Row(
                                  children: [
                                    Text(
                                      "Discount: ",
                                      style: AppTextStyles.title,
                                    ),
                                    Text(
                                   Constants.currencySymbol+   data.discount.toStringAsFixed(2),
                                      style: AppTextStyles.subTitle,
                                    ),
                                  ],
                                ),
                                Row(
                                  children: [
                                    Text(
                                      "End date: ",
                                      style: AppTextStyles.title,
                                    ),
                                    Text(


                                      dateToInfoString(data.endDate),
                                      style: AppTextStyles.subTitle,
                                    ),
                                  ],
                                ),
                                height20,
                                AppButtonWidget(
                                  fullWidth: false,
                                  text: "Copy code",
                                  onTap: () {
                                    Clipboard.setData(
                                            ClipboardData(text: data.code))
                                        .then((_) {
                                      toast("Code copied");
                                      // Show a message once the text is copied
                                    });
                                  },
                                  icon: const Icon(Icons.copy, color: Colors.white),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    );
                    // }
                  },
                  separatorBuilder: (context, index) => const Divider(),
                  itemCount: couponData.data.length,
                ),
              ),
            ],
          ),
          const ActivityIndicator(),
          if (isNoData) emptyWidget(emptyDataMsg: emptyDataMsg)
        ],
      ),
    );
  }

  void openDetails(InboxData data, int index) {
    markReadInboxMsg(data.id);
    launchScreen(SimpleWebViewURLScreen(
        title: data.title,
        dataFetcher: getInboxMsgDetails(data.id),
        htmlDataKey: null));
  }

  void onDelete(InboxData data, int index) {
    showAppDialog(
      dialogType: AppDialogType.confirmation,
      title: Globals.language.areYouSureYouWantToDelete,
      onAccept: () async {
        Navigator.of(context).pop();
        showAppActivity();
        await deleteInboxMsg(data.id).then((value) {
          if (value == true) {
            couponData.data.removeAt(index);
            setState(() {
              isNoData = couponData.data.isEmpty;
            });
            toast(Globals.language.done);
          } else {
            showErrorToast();
          }
          hideAppActivity();
        });
      },
    );
  }
}

class InboxItem extends StatelessWidget {
  final InboxData item;
  final void Function() onTap;
  final void Function() onDelete;
  const InboxItem(
      {super.key,
      required this.item,
      required this.onTap,
      required this.onDelete});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Card(
        margin: EdgeInsets.zero,
        child: Container(
          height: 100,
          color: item.is_read
              ? null
              : (Globals.isDarkModeOn
                  ? Colors.black12.withOpacity(.010)
                  : const Color.fromRGBO(221, 255, 241, 1)),
          padding: const EdgeInsets.all(10),
          child: Row(
            children: [
              CachedNetworkImage(
                imageUrl: item.imageURL,
                placeholder: (context, url) => Container(
                  height: 40,
                  width: 40,
                  decoration: const BoxDecoration(
                    shape: BoxShape.circle,
                  ),
                  child: const Center(
                    child: CircularProgressIndicator(),
                  ),
                ),
                imageBuilder: (context, imageProvider) {
                  return Container(
                    height: 40,
                    width: 40,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      image: DecorationImage(
                        image: imageProvider,
                        fit: BoxFit.cover,
                      ),
                    ),
                  );
                },
                errorWidget: (context, url, error) => Container(
                  height: 40,
                  width: 40,
                  decoration: const BoxDecoration(
                    shape: BoxShape.circle,
                  ),
                  child: const Center(
                    child: Icon(
                      Icons.error,
                    ),
                  ),
                ),
              ),
              const SizedBox(
                width: 20,
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: Text(
                        item.title,
                        overflow: TextOverflow.ellipsis,
                        maxLines: 3,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    Text(
                      item.created_at,
                      style: const TextStyle(),
                    ),
                  ],
                ),
              ),
              InkWell(
                onTap: onDelete,
                child: const Card(
                  child: Padding(
                    padding: EdgeInsets.all(2.0),
                    child: Icon(
                      Icons.delete,
                      color: Colors.red,
                    ),
                  ),
                ),
              )
            ],
          ),
        ),
      ),
    );
  }
}
