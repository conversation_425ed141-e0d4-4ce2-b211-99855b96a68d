import 'package:rider/features/ride-settings/models/business_profile.dart';
import 'package:rider/features/ride-settings/models/otp_settings_request.dart';
import 'package:rider/features/ride_flow/screen/manage_ride_stops_logic.dart';

class RideRelatedSettings {
  RideOTPSetting? otpSettings;
  BusinessProfile? businessProfile;

  RideRelatedSettings({
    required this.otpSettings,
    required this.businessProfile,
  });

  Map<String, dynamic> toMap() {
    return {
      'otp_settings': otpSettings?.toMap(),
      'business_profile': businessProfile?.toMap(),
    };
  }

  factory RideRelatedSettings.fromMap(Map<String, dynamic> map) {
    return RideRelatedSettings(
      otpSettings: map['otp_settings'] != null
          ? RideOTPSetting.fromMap(map['otp_settings'])
          : null,
      businessProfile: map['business_profile'] != null
          ? BusinessProfile.fromMap(map['business_profile'])
          : null,
    );
  }
}

class RideRelatedSettingsResponse extends ApiBaseResponse<RideRelatedSettings> {
  RideRelatedSettingsResponse(
      {required super.status, required super.message, required super.data});

  factory RideRelatedSettingsResponse.fromMap(Map<String, dynamic> map) =>
      RideRelatedSettingsResponse(
        status: map['status'] as bool,
        message: map['message'] as String,
        data: RideRelatedSettings.fromMap(map['data']),
      );
}
