// ignore_for_file: public_member_api_docs, sort_constructors_first

import 'package:rider/model/PaginationModel.dart';

class CouponModel {
  PaginationModel? pagination;
  List<CouponModelData> data;
  String? message;
    bool? status;

  CouponModel({
    required this.pagination,
    required this.data,
     this.message,
    this.status,
  });

  factory CouponModel.fromJson(Map<String, dynamic> json) {
    List<CouponModelData> inboxData = [];
    for (var i = 0; i < json['data'].length; i++) {
      inboxData.add(
        CouponModelData.fromJson(
          json['data'][i],
        ),
      );
    }
    return CouponModel(
        pagination: PaginationModel.fromJson(
          json['pagination'],
        ),
        data: inboxData,
                status: json['status'],

        message: json['message']);
  }
}

class CouponModelData {
  int id;
  String code;
  String title;
  String couponType;
  String discountType;
  double discount;
  DateTime startDate;
  DateTime endDate;
  int status;
  String? description;
  List<String>? serviceIds;
  List<int>? regionIds;
  DateTime createdAt;
  DateTime updatedAt;
  int usageLimitPerRider;

  CouponModelData({
    required this.id,
    required this.code,
    required this.title,
    required this.couponType,
    required this.discountType,
    required this.discount,
    required this.startDate,
    required this.endDate,
    required this.status,
    this.description,
    required this.serviceIds,
    this.regionIds,
    required this.createdAt,
    required this.updatedAt,
    required this.usageLimitPerRider,
  });

  // fromJson method to convert JSON to Dart object
  factory CouponModelData.fromJson(Map<String, dynamic> json) {
    return CouponModelData(
      id: json['id'],
      code: json['code'],
      title: json['title'],
      couponType: json['coupon_type'],
      discountType: json['discount_type'],
      discount: json['discount'].toDouble(),
      startDate: DateTime.parse(json['start_date']),
      endDate: DateTime.parse(json['end_date']),
      status: json['status'],
      description: json['description'],
      serviceIds: json['service_ids'] == null ? null : List<String>.from(json['service_ids']),
      regionIds: json['region_ids'] != null
          ? List<int>.from(json['region_ids'])
          : null,
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
      usageLimitPerRider: json['usage_limit_per_rider'],
    );
  }

  // toJson method to convert Dart object to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'code': code,
      'title': title,
      'coupon_type': couponType,
      'discount_type': discountType,
      'discount': discount,
      'start_date': startDate.toIso8601String(),
      'end_date': endDate.toIso8601String(),
      'status': status,
      'description': description,
      'service_ids': serviceIds,
      'region_ids': regionIds,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'usage_limit_per_rider': usageLimitPerRider,
    };
  }
}

