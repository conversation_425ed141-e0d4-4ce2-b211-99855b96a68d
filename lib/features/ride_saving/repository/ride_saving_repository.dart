import 'package:rider/features/booking/models/current_ride_response_model.dart';
import 'package:rider/model/LDBaseResponse.dart';
import 'package:rider/network/network_utils.dart';

class RideSavingRepository {
  Future<LDBaseResponse> saveRideRequest({required Map request}) async {
    return LDBaseResponse.fromJson(await handleResponse(await buildHttpResponse(
        'save-riderequest',
        method: HttpMethod.post,
        request: request)));
  }

  Future<CurrentRideResponseModel> getCurrentRideRequest() async {
    return CurrentRideResponseModel.fromJson(await handleResponse(
        await buildHttpResponse('current-riderequest',
            method: HttpMethod.get)));
  }
}
