import 'package:mapbox_maps_flutter/mapbox_maps_flutter.dart' as mp;
import 'package:rider/app_exports.dart';

class ApplyCouponScreen extends StatefulWidget {
  final SelectServiceModel selectServiceModel;
  final mp.Position sourceLocation;
  final mp.Position destinationLocation;
  final String destinationAddress;
  final String sourceAddress;
  final DateTime? scheduledTime;
  final bool isPoolingRide;
  final int personCount;
  final bool isBusinessRide;
  final bool is_peak;

  const ApplyCouponScreen({
    super.key,
    required this.sourceLocation,
    required this.is_peak,
    required this.destinationLocation,
    required this.selectServiceModel,
    required this.destinationAddress,
    required this.sourceAddress,
    required this.scheduledTime,
    required this.isPoolingRide,
    required this.personCount,
    required this.isBusinessRide,
  });

  @override
  State<ApplyCouponScreen> createState() => _ApplyCouponScreenState();
}

class _ApplyCouponScreenState extends State<ApplyCouponScreen> {
  num _rideAmount = 0;
  num _couponAmount = 0.0;
  num _payableAmount = 0.0;
  // final bool _isRideForOther = false;
  final bool _enableOtp = Globals.user.share_otp_during_ride ?? true;
  num _distance = 0;
  String? _appliedCoupon;
  String? _appliedCouponCode;

  num? _appliedCouponDiscount;

  CouponData? _couponData;

  final GlobalKey<FormState> _couponFormKey = GlobalKey<FormState>();
  final TextEditingController _promoCodeController = TextEditingController();

  _applyCoupon() async {
    showAppActivity();
    Map request = {
      "pick_lat": widget.sourceLocation.lat,
      "pick_lng": widget.sourceLocation.lng,
      "drop_lat": widget.destinationLocation.lat,
      "drop_lng": widget.destinationLocation.lng,
      "id": widget.selectServiceModel.id,
      "is_scheduled_ride": widget.scheduledTime != null,
      if (_promoCodeController.text.trim().isNotEmpty)
        "coupon_code": _promoCodeController.text.trim(),
    };

    EstimatePriceResponse response = await estimatePriceList(request);
    if (!response.status) {
      toast(response.message);
    } else if ((response.data ?? []).isEmpty) {
      toast(Constants.errorMSG);
    } else {
      _rideAmount = response.data!.first.totalAmount;
      _couponAmount = response.data!.first.couponData?.discount ?? 0;

      _appliedCoupon = _promoCodeController.text;
      _appliedCouponCode = response.data?.first.couponData?.code ?? "";
      _appliedCouponDiscount = response.data?.first.couponData?.discount;
      _couponData = response.data?.first.couponData;
      if (_appliedCoupon!.isEmpty) {
        _appliedCoupon = null;
        _appliedCouponCode = null;
        _appliedCouponDiscount = null;
        _couponData = null;
      }
      _payableAmount =
          num.parse((_rideAmount - _couponAmount).toStringAsFixed(2));
      setState(() {
        _rideAmount = num.parse((_rideAmount).toStringAsFixed(2));
      });
    }

    hideAppActivity();
  }

  @override
  void initState() {
    _rideAmount =
        num.parse((widget.selectServiceModel.totalAmount).toStringAsFixed(2));
    _payableAmount = _rideAmount;
    _distance =
        num.parse(widget.selectServiceModel.distance.toStringAsFixed(2));
    super.initState();
  }

  @override
  dispose() {
    hideAppActivity();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      bottomNavigationBar: BottomButton(
        text: "Proceed to Payment",
        onPressed: () {
          HelperMethods.pushScreen(
            context: context,
            screen: PaymentSelectionScreen(
              couponCode: _appliedCouponCode,
              couponDiscount: _appliedCouponDiscount,
              is_peak: widget.is_peak,
              couponData: _couponData,
              scheduledTime: widget.scheduledTime,
              otherRiderData: null,
              isOTPEnable: _enableOtp,
              payableAmount: _payableAmount,
              priceData: widget.selectServiceModel,
              sourceLocation: widget.sourceLocation,
              destinationLocation: widget.destinationLocation,
              startAddress: widget.sourceAddress,
              endAddress: widget.destinationAddress,
              isPoolingRide: widget.isPoolingRide,
              personCount: widget.personCount,
              isBusinessRide: widget.isBusinessRide,
            ),
          );
        },
      ),
      appBar: const RoooAppbar(title: "Apply Coupon"),
      body: Stack(
        children: [
          Padding(
            padding: const EdgeInsets.all(Layout.scaffoldBodyPadding),
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Service Image and Details Card
                  Container(
                    decoration: BoxDecoration(
                      color: Theme.of(context).cardColor,
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.05),
                          blurRadius: 10,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Column(
                      children: [
                        ClipRRect(
                          borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
                          child: CachedNetworkImage(
                            imageUrl: widget.selectServiceModel.serviceImage,
                            imageBuilder: (context, imageProvider) => Container(
                              height: MediaQuery.sizeOf(context).width / 2,
                              decoration: BoxDecoration(
                                image: DecorationImage(
                                  image: imageProvider,
                                  fit: BoxFit.cover,
                                ),
                              ),
                            ),
                            placeholder: (context, url) => Container(
                              height: MediaQuery.sizeOf(context).width / 2,
                              decoration: BoxDecoration(
                                color: Theme.of(context).cardColor,
                              ),
                              child: const Center(
                                child: CircularProgressIndicator(),
                              ),
                            ),
                            errorWidget: (context, url, error) => Container(
                              height: MediaQuery.sizeOf(context).width / 2,
                              decoration: BoxDecoration(
                                color: Theme.of(context).cardColor,
                                image: const DecorationImage(
                                  image: AssetImage(Assets.profilePlaceholder),
                                  fit: BoxFit.cover,
                                ),
                              ),
                            ),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                widget.selectServiceModel.name,
                                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 8),
                              Row(
                                children: [
                                  Icon(
                                    Icons.access_time,
                                    size: 16,
                                    color: Theme.of(context).iconTheme.color?.withOpacity(0.7),
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    "${widget.selectServiceModel.duration.toStringAsFixed(0)} mins",
                                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                      color: Theme.of(context).textTheme.bodyMedium?.color?.withOpacity(0.7),
                                    ),
                                  ),
                                  const SizedBox(width: 16),
                                  Icon(
                                    Icons.people,
                                    size: 16,
                                    color: Theme.of(context).iconTheme.color?.withOpacity(0.7),
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    "${widget.selectServiceModel.capacity} seats",
                                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                      color: Theme.of(context).textTheme.bodyMedium?.color?.withOpacity(0.7),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 24),
                  
                  // Price Details Card
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Theme.of(context).cardColor,
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.05),
                          blurRadius: 10,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "Price Details",
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 16),
                        _buildPriceRow(
                          "Ride Amount",
                          Constants.currencySymbol + _rideAmount.toString(),
                        ),
                        const Divider(height: 24),
                        _buildPriceRow(
                          "Coupon Discount",
                          "-${Constants.currencySymbol}${_couponAmount.toString()}",
                          valueColor: Colors.green,
                        ),
                        const Divider(height: 24),
                        _buildPriceRow(
                          "Distance",
                          "$_distance km",
                        ),
                        const Divider(height: 24),
                        _buildPriceRow(
                          "Payable Amount",
                          Constants.currencySymbol + _payableAmount.toString(),
                          isTotal: true,
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 24),

                  // Coupon Section
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Theme.of(context).cardColor,
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.05),
                          blurRadius: 10,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "Apply Coupon",
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 16),
                        if (_appliedCoupon != null)
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                            decoration: BoxDecoration(
                              color: Theme.of(context).primaryColor.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(
                                color: Theme.of(context).primaryColor,
                              ),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.local_offer,
                                  size: 16,
                                  color: Theme.of(context).primaryColor,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  _appliedCoupon!,
                                  style: TextStyle(
                                    color: Theme.of(context).primaryColor,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(width: 8),
                                GestureDetector(
                                  onTap: () {
                                    _appliedCoupon = null;
                                    _promoCodeController.clear();
                                    _payableAmount += _couponAmount;
                                    _payableAmount = num.parse(_payableAmount.toStringAsFixed(2));
                                    _appliedCoupon = null;
                                    _appliedCouponCode = null;
                                    _appliedCouponDiscount = null;
                                    _couponData = null;
                                    setState(() {
                                      _couponAmount = 0.0;
                                    });
                                  },
                                  child: Icon(
                                    Icons.close,
                                    size: 16,
                                    color: Theme.of(context).primaryColor,
                                  ),
                                ),
                              ],
                            ),
                          )
                        else
                          AppButtonWidget(
                            fullWidth: false,
                            text: "Add Coupon",
                            onTap: () {
                              if (widget.isPoolingRide) {
                                toast("Coupons are not available for pooling rides");
                                return;
                              }
                              _showCouponDialog();
                            },
                          ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 24),
                ],
              ),
            ),
          ),
          const ActivityIndicator(),
        ],
      ),
    );
  }

  Widget _buildPriceRow(String label, String value, {Color? valueColor, bool isTotal = false}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Theme.of(context).textTheme.bodyMedium?.color?.withOpacity(0.7),
            fontWeight: isTotal ? FontWeight.bold : null,
          ),
        ),
        Text(
          value,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: valueColor ?? (isTotal ? Theme.of(context).primaryColor : null),
            fontWeight: isTotal ? FontWeight.bold : null,
          ),
        ),
      ],
    );
  }

  void _showCouponDialog() {
    showGeneralDialog(
      context: context,
      pageBuilder: (context, animation, secondaryAnimation) {
        return Container();
      },
      barrierLabel: '',
      barrierDismissible: true,
      transitionDuration: const Duration(milliseconds: 400),
      transitionBuilder: (_, animation, secondaryAnimation, child) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Container(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        "Apply Coupon",
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    IconButton(
                      onPressed: () => Navigator.of(context).pop(),
                      icon: const Icon(Icons.close),
                    ),
                  ],
                ),
                const Divider(),
                const SizedBox(height: 16),
                Form(
                  key: _couponFormKey,
                  child: Column(
                    children: [
                      AppTextField(
                        label: "Coupon Code",
                        isValidationRequired: true,
                        validator: (value) {
                          if (value!.length < 3) {
                            return "Please enter a valid coupon code";
                          }
                          return null;
                        },
                        controller: _promoCodeController,
                        textCapitalization: TextCapitalization.characters,
                        textFieldType: TextFieldType.NAME,
                      ),
                      const SizedBox(height: 16),
                      AppButtonWidget(
                        fullWidth: true,
                        text: "Apply Coupon",
                        onTap: () {
                          if (_couponFormKey.currentState!.validate()) {
                            Navigator.of(context).pop();
                            _applyCoupon();
                          }
                        },
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  keyValue({required String key, required String value}) {
    return Padding(
      padding: const EdgeInsets.all(10),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            key,
            style: AppTextStyles.subTitle,
          ),
          Text(
            value,
            style: AppTextStyles.title,
          )
        ],
      ),
    );
  }

  adresskeyValue({required String key, required String value}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          key,
          style: AppTextStyles.subTitle,
        ),
        Text(
          value,
          style: AppTextStyles.title,
        )
      ],
    );
  }
}
