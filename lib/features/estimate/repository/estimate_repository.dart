import 'package:rider/features/booking/models/estimate_price_model.dart';

import 'package:rider/network/network_utils.dart';

class EstimateRepository {
  Future<EstimatePriceResponseModel> getEstimatePriceTypeList() async {
    return EstimatePriceResponseModel.fromJson(await handleResponse(
        await buildHttpResponse('payment-gateway-list?status=1',
            method: HttpMethod.get)));
  }

  Future<EstimatePriceResponseModel> getEstimatePriceTimeApi(
      {required Map request}) async {
    return EstimatePriceResponseModel.fromJson(await handleResponse(
        await buildHttpResponse('estimate-price-time',
            method: HttpMethod.post, request: request)));
  }
}
