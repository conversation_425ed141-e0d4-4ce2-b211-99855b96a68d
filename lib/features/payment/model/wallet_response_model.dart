import 'package:rider/global/models/response_model.dart';
import 'package:rider/model/WalletInfoModel.dart';

class WalletResponseModel extends ResponseModel<WalletInfoModel> {
  WalletResponseModel({
    required super.status,
    required String super.message,
    required super.data,
  });

  // Factory constructor for creating a new CurrentRideResponseModel instance from a map.
  factory WalletResponseModel.fromJson(Map<String, dynamic> json) {
    return WalletResponseModel(
      status: json['status'],
      message: json['message'],
      data:
          json['data'] != null ? WalletInfoModel.fromJson(json['data']) : null,
    );
  }

  // Method to convert a CurrentRideResponseModel instance to a map.
  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'message': message,
      'data': data?.toJson(),
    };
  }
}
