import 'package:pay/pay.dart';
import 'package:rider/app_exports.dart';
import 'package:rider/features/payment/screens/apple_payment_for_additional_ride_charges_screen.dart';
import 'package:rider/features/payment/screens/google_payment_for_additional_ride_charges_screen.dart';
import 'package:rider/features/payment/screens/stripe_payment_for_additional_ride_charges_screen.dart';
import 'package:rider/features/ride_flow/screen/current_ride_screen.dart';

class SelectPaymentForAdditionalCharges extends StatefulWidget {
  final num payableAmount;
  final int rideId;
  final AdditionalChargesType chargesType;
  final TipAndWaitingData? tipAndWaitingData;

  const SelectPaymentForAdditionalCharges({
    super.key,
    required this.payableAmount,
    required this.rideId,
    required this.chargesType,
    this.tipAndWaitingData,
  });

  @override
  State<SelectPaymentForAdditionalCharges> createState() =>
      _SelectPaymentForAdditionalChargesState();
}

class _SelectPaymentForAdditionalChargesState
    extends State<SelectPaymentForAdditionalCharges> {
  final ValueNotifier<bool> _isWalletSelected = ValueNotifier(false);
  num _userWalletAmount = 0;
  num _amountToPay = 0;

  num _preauthAmount = 0;
  num _walletDeductionAmount = 0;
    final ValueNotifier<bool> _walletAmountAdjusted = ValueNotifier(false);


  void _calculateDeductionAmounts() {
    _walletAmountAdjusted.value = false;

    if (_isWalletSelected.value) {
      if (_userWalletAmount > _amountToPay) {
        _walletDeductionAmount = num.parse(
            ((_userWalletAmount + _amountToPay) - _userWalletAmount)
                .toStringAsFixed(2));
      } else {
        _walletDeductionAmount = _userWalletAmount;
      }
    } else {
      _walletDeductionAmount = 0;
    }

    _preauthAmount =
        num.parse((_amountToPay - _walletDeductionAmount).toStringAsFixed(2));

    if (_isWalletSelected.value && _preauthAmount > 0 && _preauthAmount < 1) {
      num adjustment = 1 - _preauthAmount;

      _walletDeductionAmount = num.parse((_walletDeductionAmount - adjustment).toStringAsFixed(2));

      _preauthAmount = 1;

      _walletAmountAdjusted.value = true;
    }
    setState(() {
      //
    });
  }

  _getWalletDetails() async {
    showAppActivity();
    var response = await getWalletDetails(userId: Globals.user.id);
    if (!response.status) {
      toast(response.message);
    } else {
      _userWalletAmount = response.data!.walletData!.totalAmount!;
      _calculateDeductionAmounts();
      setState(() {
        hideAppActivity();
      });
    }
  }

  _init() {
    _getWalletDetails();
    _amountToPay = widget.payableAmount;
    _preauthAmount = widget.payableAmount;
  }

  @override
  void initState() {
    super.initState();

    _init();
  }

  @override
  void dispose() {
    hideAppActivity();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const RoooAppbar(title: "Payment Options"),
      body: Stack(
        children: [
          Padding(
            padding: const EdgeInsets.all(Layout.scaffoldBodyPadding),
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Payment Summary Card
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Theme.of(context).cardColor,
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.05),
                          blurRadius: 10,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "Payment Summary",
                          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 16),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              "Amount to Pay",
                              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                color: Theme.of(context).textTheme.bodyMedium?.color?.withOpacity(0.7),
                              ),
                            ),
                            Text(
                              Constants.currencySymbol + _amountToPay.toString(),
                              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                color: Theme.of(context).primaryColor,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 24),

                  // Wallet Section
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Theme.of(context).cardColor,
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.05),
                          blurRadius: 10,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              "Wallet Balance",
                              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Transform.scale(
                              scale: 1.2,
                              child: Switch(
                                value: _isWalletSelected.value,
                                onChanged: (value) {
                                  _isWalletSelected.value = value;
                                  _calculateDeductionAmounts();
                                },
                                activeColor: Theme.of(context).primaryColor,
                                activeTrackColor: Theme.of(context).primaryColor.withOpacity(0.3),
                                inactiveTrackColor: Theme.of(context).dividerColor,
                                inactiveThumbColor: Theme.of(context).brightness == Brightness.dark 
                                  ? Colors.grey[300] 
                                  : Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              "Available Balance",
                              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                color: Theme.of(context).textTheme.bodyMedium?.color?.withOpacity(0.7),
                              ),
                            ),
                            Text(
                              Constants.currencySymbol + _userWalletAmount.toString(),
                              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                        if (_isWalletSelected.value && _walletDeductionAmount > 0) ...[
                          const SizedBox(height: 12),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                "Wallet Deduction",
                                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                  color: Theme.of(context).textTheme.bodyMedium?.color?.withOpacity(0.7),
                                ),
                              ),
                              Text(
                                Constants.currencySymbol + _walletDeductionAmount.toString(),
                                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                           const SizedBox(height: 12),
                        ValueListenableBuilder<bool>(
                          valueListenable: _walletAmountAdjusted,
                          builder: (context, isAdjusted, child) {
                            if (isAdjusted) {
                              return Container(
                                padding: const EdgeInsets.all(12),
                                decoration: BoxDecoration(
                                  color: Colors.amber.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(8),
                                  border: Border.all(
                                    color: Colors.amber.withOpacity(0.3),
                                  ),
                                ),
                                child: Row(
                                  children: [
                                    Icon(
                                      Icons.info_outline,
                                      color: Colors.amber.shade800,
                                      size: 20,
                                    ),
                                    const SizedBox(width: 12),
                                    Expanded(
                                      child: Text(
                                        "Wallet deduction adjusted to meet minimum payment requirement of 1 AUD for payment gateway",
                                        style: TextStyle(
                                          color: Colors.amber.shade900,
                                          fontSize: 13,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              );
                            }
                            return const SizedBox.shrink();
                          },
                        ),
                        ],
                      ],
                    ),
                  ),
                  const SizedBox(height: 24),
                  if (_preauthAmount > 0) ...[

                    _isWalletSelected.value ?
                     Padding(
                       padding: const EdgeInsets.only(bottom:8.0),
                       child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                const Text(
                                  "Remaining Amount",
                                  
                                ),
                                Text(
                                  Constants.currencySymbol + _preauthAmount.toString(),
                                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                     ): const SizedBox(),
                    Text(
                      "Payment Methods",
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Container(
                      decoration: BoxDecoration(
                        color: Theme.of(context).cardColor,
                        borderRadius: BorderRadius.circular(16),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.05),
                            blurRadius: 10,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Column(
                        children: [
                          if (Platform.isIOS)
                            ListTile(
                              onTap: () {
                                _calculateDeductionAmounts();
                                HelperMethods.pushScreen(
                                  context: context,
                                  screen: ApplePaymentForAdditionalRideChargesScreen(
                                    paymentItem: PaymentItem(
                                      label: "Ride Charges",
                                      amount: _preauthAmount.toString(),
                                      status: PaymentItemStatus.final_price,
                                    ),
                                    chargesType: widget.chargesType,
                                    rideId: widget.rideId,
                                    tipAndWaitingData: widget.tipAndWaitingData,
                                    payableAmount: _preauthAmount,
                                    walletAmount: _walletDeductionAmount > 0 ? _walletDeductionAmount : null,
                                  ),
                                );
                              },
                              leading: Container(
                                padding: const EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  color: Theme.of(context).primaryColor.withOpacity(0.1),
                                  shape: BoxShape.circle,
                                ),
                                child: Icon(
                                  Icons.payment,
                                  color: Theme.of(context).primaryColor,
                                ),
                              ),
                              title: const Text("Apple Pay"),
                              trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                            ),
                          if (!Platform.isIOS)
                            ListTile(
                              onTap: () {
                                _calculateDeductionAmounts();
                                HelperMethods.pushScreen(
                                  context: context,
                                  screen: GooglePaymentForAdditionalRideChargesScreen(
                                    paymentItem: PaymentItem(
                                      label: "Ride Charges",
                                      amount: _preauthAmount.toString(),
                                      status: PaymentItemStatus.final_price,
                                    ),
                                    rideId: widget.rideId,
                                    tipAndWaitingData: widget.tipAndWaitingData,
                                    payableAmount: _preauthAmount,
                                    walletAmount: _walletDeductionAmount > 0 ? _walletDeductionAmount : null,
                                    chargesType: widget.chargesType,
                                  ),
                                );
                              },
                              leading: Container(
                                padding: const EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  color: Theme.of(context).primaryColor.withOpacity(0.1),
                                  shape: BoxShape.circle,
                                ),
                                child: Icon(
                                  Icons.payment,
                                  color: Theme.of(context).primaryColor,
                                ),
                              ),
                              title: const Text("Google Pay"),
                              trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                            ),
                          ListTile(
                            onTap: () {
                              _calculateDeductionAmounts();
                              HelperMethods.pushScreen(
                                context: context,
                                screen: StripePaymentForAdditionalRideChargesScreen(
                                  payableAmount: _preauthAmount,
                                  walletAmount: _walletDeductionAmount > 0 ? _walletDeductionAmount : null,
                                  rideId: widget.rideId,
                                  chargesType: widget.chargesType,
                                  tipAndWaitingData: widget.tipAndWaitingData,
                                ),
                              );
                            },
                            leading: Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: Theme.of(context).primaryColor.withOpacity(0.1),
                                shape: BoxShape.circle,
                              ),
                              child: Icon(
                                Icons.credit_card,
                                color: Theme.of(context).primaryColor,
                              ),
                            ),
                            title: const Text("Credit/Debit Card"),
                            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                          ),
                        ],
                      ),
                    ),
                  ] else
                   ListTile(
                            onTap: () {
                              _payWithWallet();
                            },
                            leading: Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: Theme.of(context).primaryColor.withOpacity(0.1),
                                shape: BoxShape.circle,
                              ),
                              child: Icon(
                                Icons.account_balance_wallet,
                                color: Theme.of(context).primaryColor,
                              ),
                            ),
                            title: const Text("Pay with Wallet"),
                            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                          )
                ],
              ),
            ),
          ),
          const ActivityIndicator(),
        ],
      ),
    );
  }

  Future<void> _payWithWallet() async {
    showAppActivity();
    _calculateDeductionAmounts();
    if (widget.chargesType == AdditionalChargesType.tip) {
      await saveWaitingTimeAndTipPreAuth(
        holdPaymentId: null,
        paymentCardId: null,
        rideRequestId: widget.rideId,
        advanced_paid: widget.tipAndWaitingData!.advancedPaid,
        due_amount: widget.tipAndWaitingData!.dueAmount,
        refundable_amount: widget.tipAndWaitingData!.refundableAmount,
        tips: widget.tipAndWaitingData!.tips,
        waiting_charges: widget.tipAndWaitingData!.waitingCharges,
        service_id: widget.tipAndWaitingData!.serviceId,
        pre_auth_amount: null,
        walletAmount: _walletDeductionAmount,
      ).then((value) async {
        if (value.status) {
          launchScreen(const DashboardWrapperScreen(), isNewTask: true);
        } else {
          setState(() {});
          toast(value.message);
        }
      }).onError((error, stackTrace) {
        setState(() {});
        toast(Globals.language.errorMsg);
        handleError(error, stackTrace);
      });
    }
    else if(widget.chargesType == AdditionalChargesType.stopsChange)
    {
      await savePaymentDetailsForStop(
        holdPaymentId: null,
        paymentCardId: null,
        debitFromWallet: _walletDeductionAmount,
        rideId: widget.rideId,
        totalAmountToPay: widget.payableAmount,
      ).then((value) async {
        if (value.status) {
          launchScreen(const CurrentRideScreen(currentRide: null,), isNewTask: true);
        } else {
          setState(() {});
          toast(value.message);
        }
      }).onError((error, stackTrace) {
        setState(() {});
        toast(Globals.language.errorMsg);
        handleError(error, stackTrace);
      });
    }
    else if(widget.chargesType == AdditionalChargesType.destinationChange)
    {
      await savePaymentDetailsForDeatination(
        holdPaymentId: null,
        paymentCardId: null,
        debitFromWallet: _walletDeductionAmount,
        rideId: widget.rideId,
        totalAmountToPay: widget.payableAmount,
      ).then((value) async {
        if (value.status) {
          launchScreen(const CurrentRideScreen(currentRide: null,), isNewTask: true);
        } else {
          setState(() {});
          toast(value.message);
        }
      }).onError((error, stackTrace) {
        setState(() {});
        toast(Globals.language.errorMsg);
        handleError(error, stackTrace);
      });
    }

    hideAppActivity();
  }
}

enum AdditionalChargesType { stopsChange, destinationChange, tip }

class TipAndWaitingData {
  num? advancedPaid;
  num? dueAmount;
  num? refundableAmount;
  num? tips;
  num? waitingCharges;
  int serviceId;

  TipAndWaitingData({
    required this.advancedPaid,
    required this.dueAmount,
    required this.refundableAmount,
    required this.tips,
    required this.waitingCharges,
    required this.serviceId,
  });
}
