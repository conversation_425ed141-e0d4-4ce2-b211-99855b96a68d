import 'package:rider/app_exports.dart';
import 'package:rider/components/saved_payment_card.dart';
import 'package:rider/features/payment/screens/select_payment_for_additional_charges_screen.dart';
import 'package:rider/features/ride_flow/screen/current_ride_screen.dart';
import 'package:flutter_stripe/flutter_stripe.dart';
import 'package:rider/screens/account_screen.dart';
import 'package:rider/screens/add_new_card.dart';
import 'package:rider/services/stripe_service.dart';

class StripePaymentForAdditionalRideChargesScreen extends StatefulWidget {
  final num payableAmount;
  final num? walletAmount;
  final int rideId;
  final AdditionalChargesType chargesType;
  final TipAndWaitingData? tipAndWaitingData;

  const StripePaymentForAdditionalRideChargesScreen({
    super.key,
    required this.payableAmount,
    required this.walletAmount,
    required this.rideId,
    required this.chargesType,
    required this.tipAndWaitingData,
  });

  @override
  State<StripePaymentForAdditionalRideChargesScreen> createState() =>
      _StripePaymentForAdditionalRideChargesScreenState();
}

class _StripePaymentForAdditionalRideChargesScreenState
    extends State<StripePaymentForAdditionalRideChargesScreen> {
  final List<SavedCard> _saveCardList = [];
  String? _selectedSavedCardId;
  String? _holdPaymentId;

  Future<void> _getSavedCardListFromStripe() async {
    showAppActivity();
    var response = await StripeService.getSavedCard(
      Globals.user.stripeCustomerId,
    );
    if (response == null) {
      toast(Globals.language.errorMsg);
    } else {
      for (var card in response) {
        if (!HelperMethods.isStripeCardExpired(
            expiryMonth: card.exp_month, expiryYear: card.exp_year)) {
          _saveCardList.add(card);
        }
      }
    }
    setState(() {
      _selectedSavedCardId =
          _saveCardList.isNotEmpty ? _saveCardList[0].id : null;
    });
    hideAppActivity();
  }

  _payWithStripe() async {
    showAppActivity();
    _holdPaymentId = null;
    _selectedSavedCardId = null;

    late String paymentType;
    switch (widget.chargesType) {
      case AdditionalChargesType.tip:
        paymentType = PaymentType.rideWaitingAndTip;
        break;
      case AdditionalChargesType.destinationChange:
        paymentType = PaymentType.rideDestinationChange;
        break;
      case AdditionalChargesType.stopsChange:
        paymentType = PaymentType.rideStopChange;
        break;
    }
    var response = await StripeService.getPaymentObject(
      payableAmount: widget.payableAmount,
      customerId: Globals.user.stripeCustomerId,
      userId: Globals.user.id.toString(),
      paymentType: paymentType,
      walletDeductionAmount: widget.walletAmount,
    );
    if (response == null) {
      toast(Globals.language.errorMsg);
      hideAppActivity();
      return;
    }

    await Stripe.instance.initPaymentSheet(
      paymentSheetParameters: SetupPaymentSheetParameters(
        paymentIntentClientSecret: response.clientSecret,
        style: ThemeMode.light,
        allowsDelayedPaymentMethods: true,
        merchantDisplayName: 'Rooo',
      ),
    );
    hideAppActivity();
    await Stripe.instance.presentPaymentSheet();
    _holdPaymentId = response.id!;
    _savePayment();
  }

  void _savePayment() {
    switch (widget.chargesType) {
      case AdditionalChargesType.stopsChange:
        _saveStopsPayment();
        break;
      case AdditionalChargesType.destinationChange:
        _saveDestinationChangePayment();
        break;
      case AdditionalChargesType.tip:
        _saveWaitingTimeAndTipPayment();
        break;
      default:
    }
  }

  Future<void> _saveStopsPayment() async {
    showAppActivity();
    var response = await savePaymentDetailsForStop(
      holdPaymentId: _holdPaymentId,
      paymentCardId: _selectedSavedCardId,
      debitFromWallet: widget.walletAmount,
      rideId: widget.rideId,
      totalAmountToPay: widget.payableAmount,
    );
    if (!response.status) {
      toast(response.message);
    } else {
      launchScreen(
        const CurrentRideScreen(
          currentRide: null,
        ),
        isNewTask: true,
      );
    }
    hideAppActivity();
  }

  Future<void> _saveDestinationChangePayment() async {
    showAppActivity();
    var response = await savePaymentDetailsForDeatination(
      holdPaymentId: _holdPaymentId,
      paymentCardId: _selectedSavedCardId,
      debitFromWallet: widget.walletAmount,
      rideId: widget.rideId,
      totalAmountToPay: widget.payableAmount,
    );
    if (!response.status) {
      toast(response.message);
    } else {
      launchScreen(
        const CurrentRideScreen(
          currentRide: null,
        ),
        isNewTask: true,
      );
    }
    hideAppActivity();
  }

  Future<void> _saveWaitingTimeAndTipPayment() async {
    showAppActivity();
    var response = await saveWaitingTimeAndTipPreAuth(
      holdPaymentId: _holdPaymentId,
      paymentCardId: _selectedSavedCardId,
      rideRequestId: widget.rideId,
      advanced_paid: widget.tipAndWaitingData!.advancedPaid,
      due_amount: widget.tipAndWaitingData!.dueAmount,
      refundable_amount: widget.tipAndWaitingData!.refundableAmount,
      tips: widget.tipAndWaitingData!.tips,
      waiting_charges: widget.tipAndWaitingData!.waitingCharges,
      service_id: widget.tipAndWaitingData!.serviceId,
      pre_auth_amount: null,
      walletAmount: widget.walletAmount,
    );

    if (response.status) {
      launchScreen(const DashboardWrapperScreen(), isNewTask: true);
    } else {
      toast(response.message);
    }
    hideAppActivity();
  }

  void _init() async {
    if (Globals.user.stripeCustomerId.isEmpty) {
      await getHomePageData();
    }
    _getSavedCardListFromStripe();
  }

  @override
  void initState() {
    super.initState();
    _init();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const RoooAppbar(title: "Pay with Debit/Credit Card"),
      body: Stack(
        children: [
          Padding(
            padding: const EdgeInsets.all(Layout.scaffoldBodyPadding),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Payment Summary Card
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Theme.of(context).cardColor,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.05),
                        blurRadius: 10,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "Payment Summary",
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                      ),
                      const SizedBox(height: 16),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            "Amount to Pay",
                            style: Theme.of(context)
                                .textTheme
                                .bodyMedium
                                ?.copyWith(
                                  color: Theme.of(context)
                                      .textTheme
                                      .bodyMedium
                                      ?.color
                                      ?.withOpacity(0.7),
                                ),
                          ),
                          Text(
                            Constants.currencySymbol +
                                widget.payableAmount.toString(),
                            style: Theme.of(context)
                                .textTheme
                                .titleMedium
                                ?.copyWith(
                                  color: Theme.of(context).primaryColor,
                                  fontWeight: FontWeight.bold,
                                ),
                          ),
                        ],
                      ),
                      if (widget.walletAmount != null &&
                          widget.walletAmount! > 0) ...[
                        const SizedBox(height: 12),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              "Wallet Deduction",
                              style: Theme.of(context)
                                  .textTheme
                                  .bodyMedium
                                  ?.copyWith(
                                    color: Theme.of(context)
                                        .textTheme
                                        .bodyMedium
                                        ?.color
                                        ?.withOpacity(0.7),
                                  ),
                            ),
                            Text(
                              Constants.currencySymbol +
                                  widget.walletAmount.toString(),
                              style: Theme.of(context)
                                  .textTheme
                                  .titleMedium
                                  ?.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                            ),
                          ],
                        ),
                      ],
                    ],
                  ),
                ),
                const SizedBox(height: 24),

                // Saved Cards Section
                Text(
                  "Saved Cards",
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
                const SizedBox(height: 16),
                Expanded(
                  child: _saveCardList.isEmpty && !isAppActivityRunning.value
                      ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              InkWell(
                                onTap: _saveNewCard,
                                child: Container(
                                  width: 120,
                                  height: 120,
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    color: Theme.of(context)
                                        .primaryColor
                                        .withOpacity(0.1),
                                  ),
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Icon(
                                        Icons.add,
                                        size: 36,
                                        color: Theme.of(context).primaryColor,
                                      ),
                                      const SizedBox(height: 8),
                                      Text(
                                        "Add new card",
                                        style: TextStyle(
                                          color: Theme.of(context).primaryColor,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                        )
                      : ListView.builder(
                          itemCount: _saveCardList.length,
                          itemBuilder: (context, index) {
                            final card = _saveCardList[index];
                            final isSelected = _selectedSavedCardId == card.id;
                            return Container(
                              margin: const EdgeInsets.only(bottom: 12),
                              decoration: BoxDecoration(
                                color: Theme.of(context).cardColor,
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: isSelected
                                      ? Theme.of(context).primaryColor
                                      : Theme.of(context).dividerColor,
                                  width: isSelected ? 2 : 1,
                                ),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.05),
                                    blurRadius: 10,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: InkWell(
                                onTap: () {
                                  setState(() {
                                    if (_selectedSavedCardId == card.id) {
                                      _selectedSavedCardId = null;
                                    } else {
                                      _selectedSavedCardId = card.id;
                                    }
                                  });
                                },
                                borderRadius: BorderRadius.circular(12),
                                child: Padding(
                                  padding: const EdgeInsets.all(16),
                                  child: Row(
                                    children: [
                                      Container(
                                        padding: const EdgeInsets.all(8),
                                        decoration: BoxDecoration(
                                          color: Theme.of(context)
                                              .primaryColor
                                              .withOpacity(0.1),
                                          shape: BoxShape.circle,
                                        ),
                                        child: Icon(
                                          Icons.credit_card,
                                          color: Theme.of(context).primaryColor,
                                        ),
                                      ),
                                      const SizedBox(width: 16),
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              "•••• ${card.last4}",
                                              style: Theme.of(context)
                                                  .textTheme
                                                  .titleMedium
                                                  ?.copyWith(
                                                    fontWeight: FontWeight.bold,
                                                  ),
                                            ),
                                            const SizedBox(height: 4),
                                            Text(
                                              "Expires ${card.exp_month}/${card.exp_year}",
                                              style: Theme.of(context)
                                                  .textTheme
                                                  .bodySmall
                                                  ?.copyWith(
                                                    color: Theme.of(context)
                                                        .textTheme
                                                        .bodyMedium
                                                        ?.color
                                                        ?.withOpacity(0.7),
                                                  ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      if (isSelected)
                                        Container(
                                          padding: const EdgeInsets.all(8),
                                          decoration: BoxDecoration(
                                            color: Theme.of(context)
                                                .primaryColor
                                                .withOpacity(0.1),
                                            shape: BoxShape.circle,
                                          ),
                                          child: Icon(
                                            Icons.check,
                                            color:
                                                Theme.of(context).primaryColor,
                                            size: 20,
                                          ),
                                        ),
                                    ],
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                ),
                if (_saveCardList.isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.only(top: 16),
                    child: Center(
                      child: TextButton.icon(
                        onPressed: _saveNewCard,
                        icon: Icon(
                          Icons.add_circle_outline,
                          color: Theme.of(context).primaryColor,
                        ),
                        label: Text(
                          "Add another card",
                          style: TextStyle(
                            color: Theme.of(context).primaryColor,
                          ),
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          ),
          const ActivityIndicator(),
        ],
      ),
      bottomNavigationBar: BottomButton(
        text: _selectedSavedCardId != null
            ? "Pay with Selected Card"
            : "Pay with New Card",
        onPressed: () {
          if (_selectedSavedCardId != null) {
            _savePayment();
          } else {
            _payWithStripe();
          }
        },
      ),
    );
  }

  Future<void> _saveNewCard() async {
    bool? result = await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const AddNewCardScreen(
          previousCards: [],
        ),
      ),
    );

    if (result == true) {
      _getSavedCardListFromStripe();
    }
  }
}
