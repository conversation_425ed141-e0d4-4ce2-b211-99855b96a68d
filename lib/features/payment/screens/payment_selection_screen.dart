import 'package:mapbox_maps_flutter/mapbox_maps_flutter.dart';
import 'package:pay/pay.dart';
import 'package:rider/app_exports.dart';
import 'package:rider/features/booking/models/save_ride_model.dart';
import 'package:rider/features/payment/screens/apple_payment_screen.dart';
import 'package:rider/features/payment/screens/google_payment_screen.dart';
import 'package:rider/features/payment/screens/stripe_payment_screen.dart';
import 'package:rider/features/ride_flow/screen/current_ride_screen.dart';

class PaymentSelectionScreen extends StatefulWidget {
  final num payableAmount;
  final SelectServiceModel priceData;
  final Position sourceLocation;
  final Position destinationLocation;
  final String startAddress;
  final String endAddress;
  final dynamic otherRiderData;
  final bool isOTPEnable;
  final DateTime? scheduledTime;
  final CouponData? couponData;
  final bool isPoolingRide;
  final int personCount;
  final bool isBusinessRide;
  final bool is_peak;
  final String? couponCode;
  final num? couponDiscount;

  const PaymentSelectionScreen({
    super.key,
    required this.is_peak,
    required this.payableAmount,
    required this.priceData,
    required this.sourceLocation,
    required this.destinationLocation,
    required this.startAddress,
    required this.endAddress,
    required this.otherRiderData,
    required this.isOTPEnable,
    required this.scheduledTime,
    this.couponData,
    required this.isPoolingRide,
    required this.personCount,
    required this.isBusinessRide,
    this.couponCode,
    this.couponDiscount,
  });

  @override
  State<PaymentSelectionScreen> createState() => _PaymentSelectionScreenState();
}

class _PaymentSelectionScreenState extends State<PaymentSelectionScreen> {
  final ValueNotifier<bool> _isWalletSelected = ValueNotifier(false);
  num _userWalletAmount = 0;
  num _amountToPay = 0;
  num _preauthAmount = 0;
  num _walletDeductionAmount = 0;

  final ValueNotifier<bool> _walletAmountAdjusted = ValueNotifier(false);

  void _calculateDeductionAmounts() {
    _walletAmountAdjusted.value = false;

    if (_isWalletSelected.value) {
      if (_userWalletAmount > _amountToPay) {
        _walletDeductionAmount = num.parse(
            ((_userWalletAmount + _amountToPay) - _userWalletAmount)
                .toStringAsFixed(2));
      } else {
        _walletDeductionAmount = _userWalletAmount;
      }
    } else {
      _walletDeductionAmount = 0;
    }

    _preauthAmount =
        num.parse((_amountToPay - _walletDeductionAmount).toStringAsFixed(2));

    if (_isWalletSelected.value && _preauthAmount > 0 && _preauthAmount < 1) {
      num adjustment = 1 - _preauthAmount;

      _walletDeductionAmount = num.parse((_walletDeductionAmount - adjustment).toStringAsFixed(2));

      _preauthAmount = 1;

      _walletAmountAdjusted.value = true;
    }
    setState(() {
      //
    });
  }

  _getWalletDetails() async {
    showAppActivity();
    var response = await getWalletDetails(userId: Globals.user.id);
    if (!response.status) {
      toast(response.message);
    } else {
      _userWalletAmount = response.data!.walletData!.totalAmount!;
      _calculateDeductionAmounts();
      setState(() {
        hideAppActivity();
      });
    }
  }

  _init() {
    _amountToPay = widget.payableAmount;
    _getWalletDetails();
  }

  @override
  void initState() {
    super.initState();

    _init();
  }

  @override
  void dispose() {
    hideAppActivity();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const RoooAppbar(title: "Payment Options"),
      body: Stack(
        children: [
          Padding(
            padding: const EdgeInsets.all(Layout.scaffoldBodyPadding),
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Payment Summary Card
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Theme.of(context).cardColor,
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.05),
                          blurRadius: 10,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "Payment Summary",
                          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 16),
                        _buildPaymentRow(
                          "Total Amount",
                          Constants.currencySymbol + _amountToPay.toString(),
                          isTotal: true,
                        ),
                        const Divider(height: 24),
                        ValueListenableBuilder<bool>(
                          valueListenable: _isWalletSelected,
                          builder: (context, isWalletSelected, child) {
                            if (isWalletSelected) {
                              return Column(
                                children: [
                                  _buildPaymentRow(
                                    "Wallet Balance",
                                    Constants.currencySymbol + _userWalletAmount.toString(),
                                  ),
                                  const SizedBox(height: 12),
                                  _buildPaymentRow(
                                    "Wallet Deduction",
                                    "-${Constants.currencySymbol}${_walletDeductionAmount.toString()}",
                                    valueColor: Colors.green,
                                  ),
                                  const SizedBox(height: 12),
                                  _buildPaymentRow(
                                    "Amount to Pay",
                                    Constants.currencySymbol + _preauthAmount.toString(),
                                    isTotal: true,
                                  ),
                                ],
                              );
                            }
                            return const SizedBox.shrink();
                          },
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 24),

                  // Wallet Section
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Theme.of(context).cardColor,
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.05),
                          blurRadius: 10,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  Icons.account_balance_wallet,
                                  color: Theme.of(context).primaryColor,
                                ),
                                const SizedBox(width: 12),
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      "Wallet Balance",
                                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    Text(
                                      Constants.currencySymbol + _userWalletAmount.toString(),
                                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                        color: Theme.of(context).primaryColor,
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                            if(_userWalletAmount > 0)
                            ValueListenableBuilder<bool>(
                              valueListenable: _isWalletSelected,
                              builder: (context, value, child) {
                                return Switch(
                                  value: value,
                                  onChanged: (isSelected) {
                                    _isWalletSelected.value = isSelected;
                                    _calculateDeductionAmounts();
                                  },
                                  activeColor: Theme.of(context).primaryColor,
                                  activeTrackColor: Theme.of(context).primaryColor.withOpacity(0.5),
                                  inactiveTrackColor: Theme.of(context).dividerColor,
                                  inactiveThumbColor: Theme.of(context).brightness == Brightness.dark 
                                    ? Colors.grey[300] 
                                    : Colors.grey[600],
                                );
                              },
                            ),
                          ],
                        ),
                        const SizedBox(height: 12),
                        ValueListenableBuilder<bool>(
                          valueListenable: _walletAmountAdjusted,
                          builder: (context, isAdjusted, child) {
                            if (isAdjusted) {
                              return Container(
                                padding: const EdgeInsets.all(12),
                                decoration: BoxDecoration(
                                  color: Colors.amber.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(8),
                                  border: Border.all(
                                    color: Colors.amber.withOpacity(0.3),
                                  ),
                                ),
                                child: Row(
                                  children: [
                                    Icon(
                                      Icons.info_outline,
                                      color: Colors.amber.shade800,
                                      size: 20,
                                    ),
                                    const SizedBox(width: 12),
                                    Expanded(
                                      child: Text(
                                        "Wallet deduction adjusted to meet minimum payment requirement of 1 AUD for payment gateway",
                                        style: TextStyle(
                                          color: Colors.amber.shade900,
                                          fontSize: 13,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              );
                            }
                            return const SizedBox.shrink();
                          },
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 24),

                  // Payment Methods Section
                  Text(
                    "Payment Methods",
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Container(
                    decoration: BoxDecoration(
                      color: Theme.of(context).cardColor,
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.05),
                          blurRadius: 10,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Column(
                      children: [
                        if (_preauthAmount <= 0)
                          ListTile(
                            onTap: () {
                              _calculateDeductionAmounts();
                              _payWithWallet();
                            },
                            leading: Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: Theme.of(context).primaryColor.withOpacity(0.1),
                                shape: BoxShape.circle,
                              ),
                              child: Icon(
                                Icons.account_balance_wallet,
                                color: Theme.of(context).primaryColor,
                              ),
                            ),
                            title: const Text("Pay with Wallet"),
                            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                          )
                        else
                          Column(
                            children: [
                              if (Platform.isIOS)
                                ListTile(
                                  onTap: () {
                                    _calculateDeductionAmounts();
                                    HelperMethods.pushScreen(
                                      context: context,
                                      screen: ApplePaymentScreen(
                                        couponCode: widget.couponCode,
                                        couponDiscount: widget.couponDiscount,
                                        is_peak: widget.is_peak,
                                        paymentItem: PaymentItem(
                                          label: "ROOO Ride",
                                          amount: (_preauthAmount + 0.0).toString(),
                                          status: PaymentItemStatus.final_price,
                                        ),
                                        couponData: widget.couponData,
                                        scheduledRideTime: widget.scheduledTime,
                                        otherRiderData: widget.otherRiderData,
                                        isOTPEnable: widget.isOTPEnable,
                                        payableAmount: _preauthAmount,
                                        priceData: widget.priceData,
                                        sourceLocation: widget.sourceLocation,
                                        destinationLocation: widget.destinationLocation,
                                        startAddress: widget.startAddress,
                                        endAddress: widget.endAddress,
                                        isPoolingRide: widget.isPoolingRide,
                                        personCount: widget.personCount,
                                        isBusinessRide: widget.isBusinessRide,
                                        deductionAmount: _walletDeductionAmount > 0 ? _walletDeductionAmount : null,
                                      ),
                                    );
                                  },
                                  leading: Container(
                                    padding: const EdgeInsets.all(8),
                                    decoration: BoxDecoration(
                                      color: Colors.black.withOpacity(0.1),
                                      shape: BoxShape.circle,
                                    ),
                                    child: Icon(
                                      Icons.payment,
                                      color: Theme.of(context).brightness == Brightness.dark 
                                        ? Colors.white 
                                        : Colors.black,
                                    ),
                                  ),
                                  title: const Text("Apple Pay"),
                                  trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                                ),
                              if (Platform.isAndroid)
                                ListTile(
                                  onTap: () {
                                    _calculateDeductionAmounts();
                                    HelperMethods.pushScreen(
                                      context: context,
                                      screen: GooglePaymentScreen(
                                        is_peak: widget.is_peak,
                                        paymentItem: PaymentItem(
                                          label: "ROOO Ride",
                                          amount: (_preauthAmount + 0.0).toString(),
                                          status: PaymentItemStatus.final_price,
                                        ),
                                        couponData: widget.couponData,
                                        scheduledRideTime: widget.scheduledTime,
                                        otherRiderData: widget.otherRiderData,
                                        isOTPEnable: widget.isOTPEnable,
                                        payableAmount: _preauthAmount,
                                        priceData: widget.priceData,
                                        sourceLocation: widget.sourceLocation,
                                        destinationLocation: widget.destinationLocation,
                                        startAddress: widget.startAddress,
                                        endAddress: widget.endAddress,
                                        isPoolingRide: widget.isPoolingRide,
                                        personCount: widget.personCount,
                                        isBusinessRide: widget.isBusinessRide,
                                        deductionAmount: _walletDeductionAmount > 0 ? _walletDeductionAmount : null,
                                      ),
                                    );
                                  },
                                  leading: Container(
                                    padding: const EdgeInsets.all(8),
                                    decoration: BoxDecoration(
                                      color: Colors.black.withOpacity(0.1),
                                      shape: BoxShape.circle,
                                    ),
                                    child: Icon(
                                      Icons.payment,
                                      color: Theme.of(context).brightness == Brightness.dark 
                                        ? Colors.white 
                                        : Colors.black,
                                    ),
                                  ),
                                  title: const Text("Google Pay"),
                                  trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                                ),
                              ListTile(
                                onTap: () {
                                  _calculateDeductionAmounts();
                                  HelperMethods.pushScreen(
                                    context: context,
                                    screen: StripePaymentScreen(
                                      couponCode: widget.couponCode,
                                      couponDiscount: widget.couponDiscount,
                                      is_peak: widget.is_peak,
                                      couponData: widget.couponData,
                                      scheduledRideTime: widget.scheduledTime,
                                      otherRiderData: widget.otherRiderData,
                                      isOTPEnable: widget.isOTPEnable,
                                      payableAmount: _preauthAmount,
                                      priceData: widget.priceData,
                                      sourceLocation: widget.sourceLocation,
                                      destinationLocation: widget.destinationLocation,
                                      startAddress: widget.startAddress,
                                      endAddress: widget.endAddress,
                                      deductionAmount: _walletDeductionAmount > 0 ? _walletDeductionAmount : null,
                                      isPoolingRide: widget.isPoolingRide,
                                      personCount: widget.personCount,
                                      isBusinessRide: widget.isBusinessRide,
                                    ),
                                  );
                                },
                                leading: Container(
                                  padding: const EdgeInsets.all(8),
                                  decoration: BoxDecoration(
                                    color: Colors.black.withOpacity(0.1),
                                    shape: BoxShape.circle,
                                  ),
                                  child: Icon(
                                    Icons.credit_card,
                                    color: Theme.of(context).brightness == Brightness.dark 
                                      ? Colors.white 
                                      : Colors.black,
                                  ),
                                ),
                                title: const Text("Credit/Debit Card"),
                                trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                              ),
                            ],
                          ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 24),
                ],
              ),
            ),
          ),
          const ActivityIndicator(),
        ],
      ),
    );
  }

  Widget _buildPaymentRow(String label, String value, {Color? valueColor, bool isTotal = false}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Theme.of(context).textTheme.bodyMedium?.color?.withOpacity(0.7),
            fontWeight: isTotal ? FontWeight.bold : null,
          ),
        ),
        Text(
          value,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: valueColor ?? (isTotal ? Theme.of(context).primaryColor : null),
            fontWeight: isTotal ? FontWeight.bold : null,
          ),
        ),
      ],
    );
  }

  void _payWithWallet() async {
    showAppActivity();
    SaveRideModel request = SaveRideModel(
      couponData: widget.couponData,

      peak_ride: widget.is_peak,
      isRideForOther: widget.otherRiderData != null,
      otherRiderData: widget.otherRiderData,
      serviceId: widget.priceData.id.toString(),
      isOTPSharingRequired: widget.isOTPEnable,
      isPoolingRide: widget.isPoolingRide,
      isBusinessRide: widget.isBusinessRide,
      personCount: widget.personCount,
      debitWallet: _walletDeductionAmount,
      baseFare: widget.priceData.baseFare,
      baseDistance: widget.priceData.baseDistance,
      extraCharges: widget.priceData.extraCharges,
      extraChargesAmount: widget.priceData.extraChargesAmount,
      isScheduledRide: widget.scheduledTime != null,
      scheduledDate: widget.scheduledTime,
      minimumFare: widget.priceData.minimumFare,
      perDistance: widget.priceData.perDistance,
      perDistanceCharge: widget.priceData.perDistanceCharge,
      perMinuteDrive: widget.priceData.perMinuteDrive,
      perMinuteDriveCharge: widget.priceData.perMinuteDriveCharge,
      perMinuteWaiting: null,
      perMinuteWaitingCharge: null,
      // seatCount: "",
      subtotal: widget.priceData.subtotal,
      waitingTimeLimit: widget.priceData.waitingTimeLimit,
      totalAmount: widget.priceData.totalAmount,
      // vehicleId: "",
      couponCode: widget.couponCode,

      couponDiscount: widget.couponDiscount,
      status: RideStatus.newRideRequested,
      holdPaymentId: null,
      paymentCardId: null,
      riderId: Globals.user.id.toString(),
      startLatitude: widget.sourceLocation.lat.toString(),
      startLongitude: widget.sourceLocation.lng.toString(),
      startAddress: widget.startAddress,
      endLatitude: widget.destinationLocation.lat.toString(),
      endLongitude: widget.destinationLocation.lng.toString(),
      endAddress: widget.endAddress,
      paymentType: 'pre-authorized payment',
      tax: widget.priceData.tax,
      finalAmount: 0,
      distance: widget.priceData.distance,
      isMinimumAmountApplies: widget.priceData.isMinimumAmountApplies,
      stripe: widget.priceData.stripe,
      airportCharges: widget.priceData.airportCharges,
      duration: widget.priceData.duration,
    );

    LDBaseResponse response = await saveRideRequest(request.toJson());
    if (!(response.status ?? false)) {
      toast(response.message);
    } else {
      if (widget.scheduledTime != null) {
        showAppDialog(
          onAccept: () {
            launchScreen(
              const DashboardWrapperScreen(),
              isNewTask: true,
            );
          },
          dialogType: AppDialogType.info,
          title:
              "Your ride is scheduled for ${widget.scheduledTime!.hour}:${widget.scheduledTime!.minute}.\n\nWe will inform you when your ride is assigned to a driver.",
        );
        return;
      }

      /* Goto ride screen */

      launchScreen(
        const CurrentRideScreen(
          currentRide: null,
        ),
      );
    }
  }
}
