import 'package:flutter/services.dart';
import 'package:flutter_stripe/flutter_stripe.dart';
import 'package:pay/pay.dart';
import 'package:rider/app_exports.dart';
import 'package:rider/features/payment/screens/select_payment_for_additional_charges_screen.dart';
import 'package:rider/features/ride_flow/screen/current_ride_screen.dart';
import 'package:rider/services/stripe_service.dart';

class GooglePaymentForAdditionalRideChargesScreen extends StatefulWidget {
  final PaymentItem paymentItem;
  final num payableAmount;
  final num? walletAmount;
  final int rideId;
  final AdditionalChargesType chargesType;
  final TipAndWaitingData? tipAndWaitingData;

  const GooglePaymentForAdditionalRideChargesScreen({
    super.key,
    required this.paymentItem,
    required this.payableAmount,
    required this.chargesType,
    required this.rideId,
    required this.tipAndWaitingData,
    required this.walletAmount,
  });

  @override
  State<GooglePaymentForAdditionalRideChargesScreen> createState() =>
      _GooglePaymentForAdditionalRideChargesScreenState();
}

class _GooglePaymentForAdditionalRideChargesScreenState
    extends State<GooglePaymentForAdditionalRideChargesScreen> {
  String _holdPaymentId = "";

  late final Pay _payClient;

  @override
  void initState() {
    showAppActivity();

    super.initState();

    _startPayment();
  }

  @override
  dispose() {
    hideAppActivity();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      appBar: RoooAppbar(title: "Pay with Google pay"),
      body: Stack(
        children: [
          ActivityIndicator(),
        ],
      ),
    );
  }

  String getGooglePayData() {
    return jsonEncode({
      "provider": "google_pay",
      "data": {
        // "environment": "TEST",
        "environment": "production",
        "apiVersion": 2,
        "apiVersionMinor": 0,
        "allowedPaymentMethods": [
          {
            "type": "CARD",
            "tokenizationSpecification": {
              "type": "PAYMENT_GATEWAY",
              "parameters": {
                "gateway": "stripe",
                "stripe:version": "2023-10-16",
                "stripe:publishableKey": StripeService.publishableKey,
              }
            },
            "parameters": {
              "allowedCardNetworks": ["VISA"],
              "allowedAuthMethods": ["PAN_ONLY", "CRYPTOGRAM_3DS"],
              "billingAddressRequired": false,
              "billingAddressParameters": {
                "format": "FULL",
                "phoneNumberRequired": true
              }
            }
          }
        ],
        "merchantInfo": {
          "merchantId": "BCR2DN4TS6I5N2YJ",
          "merchantName": "ROOO"
        },
        "transactionInfo": {"countryCode": "AU", "currencyCode": "AUD"}
      }
    });
  }

  Future<void> _startPayment() async {
    try {
      var response = await StripeService.getPaymentTypeList();
      if (!response) {
        toast(Globals.language.errorMsg);
        return closeScreen();
      }

      _payClient = Pay(
        {
          PayProvider.google_pay: PaymentConfiguration.fromJsonString(
            getGooglePayData(),
          ),
        },
      );

      final result = await _payClient.showPaymentSelector(
        PayProvider.google_pay,
        [widget.paymentItem],
      );
      await _onGooglePayResult(result);
    } on PlatformException catch (e) {
      if (e.code.toString() == "paymentCanceled") {
        closeScreen();
        toast("You cancelled the payment");
      }
    } catch (e) {
      closeScreen();
      toast(Globals.language.errorMsg);
    }
  }

  void closeScreen() {
    Navigator.of(context).pop();
  }

  Future<void> _onGooglePayResult(Map<String, dynamic> result) async {
    late String paymentType;
    switch (widget.chargesType) {
      case AdditionalChargesType.tip:
        paymentType = PaymentType.rideWaitingAndTip;
        break;
      case AdditionalChargesType.destinationChange:
        paymentType = PaymentType.rideDestinationChange;
        break;
      case AdditionalChargesType.stopsChange:
        paymentType = PaymentType.rideStopChange;
        break;
    }
    var response = await StripeService.getPaymentObject(
      payableAmount: widget.payableAmount,
      customerId: Globals.user.stripeCustomerId,
      userId: Globals.user.id.toString(),
      paymentType: paymentType,
      walletDeductionAmount: widget.walletAmount,
    );

    if (response == null) {
      toast(Globals.language.errorMsg);
      hideAppActivity();
      return;
    }

    final token = result['paymentMethodData']['tokenizationData']['token'];
    final tokenJson = Map.castFrom(json.decode(token));

    final params = PaymentMethodParams.cardFromToken(
      paymentMethodData: PaymentMethodDataCardFromToken(
        token: tokenJson['id'],
      ),
    );

    await Stripe.instance.confirmPayment(
      paymentIntentClientSecret: response.clientSecret!,
      data: params,
    );
    _holdPaymentId = response.id!;

    await _savePayment();
  }

  Future<void> _savePayment() async {
    switch (widget.chargesType) {
      case AdditionalChargesType.stopsChange:
        _saveStopsPayment();
        break;
      case AdditionalChargesType.destinationChange:
        _saveDestinationChangePayment();
        break;
      case AdditionalChargesType.tip:
        _saveWaitingTimeAndTipPayment();
        break;
      default:
    }
  }

  Future<void> _saveStopsPayment() async {
     launchScreen(
        const CurrentRideScreen(
          currentRide: null,
        ),
        isNewTask: true,
      );
    // showAppActivity();
    // var response = await savePaymentDetailsForStop(
    //   holdPaymentId: _holdPaymentId,
    //   paymentCardId: null,
    //   debitFromWallet: widget.walletAmount,
    //   rideId: widget.rideId,
    // );
    // if (!response.status) {
    //   toast(response.message);
    // } else {
    //   /* Goto ride screen */
    //   launchScreen(
    //     const CurrentRideScreen(
    //       currentRide: null,
    //     ),
    //     isNewTask: true,
    //   );
    // }
    // hideAppActivity();
  }

  Future<void> _saveDestinationChangePayment() async {

      launchScreen(
        const CurrentRideScreen(
          currentRide: null,
        ),
        isNewTask: true,
      );
    // showAppActivity();
    // var response = await savePaymentDetailsForDeatination(
    //   holdPaymentId: _holdPaymentId,
    //   paymentCardId: null,
    //   debitFromWallet: widget.walletAmount,
    //   rideId: widget.rideId,
    // );
    // if (!response.status) {
    //   toast(response.message);
    // } else {
    //   /* Goto ride screen */
    //   launchScreen(
    //     const CurrentRideScreen(
    //       currentRide: null,
    //     ),
    //     isNewTask: true,
    //   );
    // }
    // hideAppActivity();
  }

  Future<void> _saveWaitingTimeAndTipPayment() async {
    showAppActivity();
    var response = await saveWaitingTimeAndTipPreAuth(
      holdPaymentId: _holdPaymentId,
      paymentCardId: null,
      rideRequestId: widget.rideId,
      advanced_paid: widget.tipAndWaitingData!.advancedPaid,
      due_amount: widget.tipAndWaitingData!.dueAmount,
      refundable_amount: widget.tipAndWaitingData!.refundableAmount,
      tips: widget.tipAndWaitingData!.tips,
      waiting_charges: widget.tipAndWaitingData!.waitingCharges,
      service_id: widget.tipAndWaitingData!.serviceId,
      pre_auth_amount: null,
      walletAmount: widget.walletAmount,
    );

    if (response.status) {
      launchScreen(const DashboardWrapperScreen(), isNewTask: true);
    } else {
      toast(response.message);
    }
    hideAppActivity();
  }
}
