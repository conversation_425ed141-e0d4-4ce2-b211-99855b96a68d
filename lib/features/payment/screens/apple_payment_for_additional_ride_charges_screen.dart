import 'package:flutter/services.dart';
import 'package:flutter_stripe/flutter_stripe.dart';
import 'package:pay/pay.dart';
import 'package:rider/app_exports.dart';
import 'package:rider/features/payment/screens/select_payment_for_additional_charges_screen.dart';
import 'package:rider/features/ride_flow/screen/current_ride_screen.dart';
import 'package:rider/services/stripe_service.dart';

class ApplePaymentForAdditionalRideChargesScreen extends StatefulWidget {
  final PaymentItem paymentItem;
  final num payableAmount;
  final num? walletAmount;
  final int rideId;
  final AdditionalChargesType chargesType;
  final TipAndWaitingData? tipAndWaitingData;

  const ApplePaymentForAdditionalRideChargesScreen({
    super.key,
    required this.paymentItem,
    required this.payableAmount,
    required this.chargesType,
    required this.rideId,
    required this.tipAndWaitingData,
    required this.walletAmount,
  });

  @override
  State<ApplePaymentForAdditionalRideChargesScreen> createState() =>
      _ApplePaymentForAdditionalRideChargesScreenState();
}

class _ApplePaymentForAdditionalRideChargesScreenState
    extends State<ApplePaymentForAdditionalRideChargesScreen> {
  String _holdPaymentId = "";

  late final Pay _payClient;

  @override
  void initState() {
    showAppActivity();

    super.initState();

    _startPayment();
  }

  @override
  dispose() {
    hideAppActivity();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      appBar: RoooAppbar(title: "Pay with Apple pay"),
      body: Stack(
        children: [
          ActivityIndicator(),
        ],
      ),
    );
  }

  String getApplePayData() {
    return jsonEncode({
      "provider": "apple_pay",
      "data": {
        "merchantIdentifier": "merchant.au.rooo",
        "merchantCapabilities": ["3DS"],
        "supportedNetworks": ["amex", "visa", "discover", "masterCard"],
        "countryCode": "AU",
        "currencyCode": "AUD",
      },
    });
  }

  Future<void> _startPayment() async {
    _payClient = Pay(
      {
        PayProvider.apple_pay: PaymentConfiguration.fromJsonString(
          getApplePayData(),
        ),
      },
    );
    bool canPay = await _payClient.userCanPay(PayProvider.apple_pay);
    if (!canPay) {
      toast("Apple pay is not supported on this device");
      closeScreen();
      return;
    }

    try {
      var response = await StripeService.getPaymentTypeList();
      if (!response) {
        toast(Globals.language.errorMsg);
        return closeScreen();
      }

      final result = await _payClient.showPaymentSelector(
        PayProvider.apple_pay,
        [widget.paymentItem],
      );
      await _onApplePayResult(result);
    } on PlatformException catch (e) {
      if (e.code.toString() == "paymentCanceled") {
        closeScreen();
        toast("You cancelled the payment");
      }
    } catch (e) {
      closeScreen();
      toast(Globals.language.errorMsg);
    }
  }

  Future<void> _onApplePayResult(Map<String, dynamic> result) async {
    late String paymentType;
    switch (widget.chargesType) {
      case AdditionalChargesType.tip:
        paymentType = PaymentType.rideWaitingAndTip;
        break;
      case AdditionalChargesType.destinationChange:
        paymentType = PaymentType.rideDestinationChange;
        break;
      case AdditionalChargesType.stopsChange:
        paymentType = PaymentType.rideStopChange;
        break;
    }
    var response = await StripeService.getPaymentObject(
      payableAmount: widget.payableAmount,
      customerId: Globals.user.stripeCustomerId,
      userId: Globals.user.id.toString(),
      paymentType: paymentType,
      walletDeductionAmount: widget.walletAmount,
    );

    if (response == null) {
      toast(Globals.language.errorMsg);
      hideAppActivity();
      return;
    }

    final token = await Stripe.instance.createApplePayToken(result);

    final params = PaymentMethodParams.cardFromToken(
      paymentMethodData: PaymentMethodDataCardFromToken(
        token: token.id,
      ),
    );

    await Stripe.instance.confirmPayment(
      paymentIntentClientSecret: response.clientSecret!,
      data: params,
    );
    _holdPaymentId = response.id!;

    await _savePayment();
  }

  Future<void> _savePayment() async {
    switch (widget.chargesType) {
      case AdditionalChargesType.stopsChange:
        _saveStopsPayment();
        break;
      case AdditionalChargesType.destinationChange:
        _saveDestinationChangePayment();
        break;
      case AdditionalChargesType.tip:
        _saveWaitingTimeAndTipPayment();
        break;
    }
  }

  void closeScreen() {
    Navigator.of(context).pop();
  }

  Future<void> _saveStopsPayment() async {

     launchScreen(
        const CurrentRideScreen(
          currentRide: null,
        ),
        isNewTask: true,
      );
    // showAppActivity();
    // var response = await savePaymentDetailsForStop(
    //   holdPaymentId: _holdPaymentId,
    //   paymentCardId: null,
    //   debitFromWallet: widget.walletAmount,
    //   rideId: widget.rideId,
    // );
    // if (!response.status) {
    //   toast(response.message);
    // } else {
    //   /* Goto ride screen */
    //   launchScreen(
    //     const CurrentRideScreen(
    //       currentRide: null,
    //     ),
    //     isNewTask: true,
    //   );
    // }
    // hideAppActivity();
  }

  Future<void> _saveDestinationChangePayment() async {
     launchScreen(
        const CurrentRideScreen(
          currentRide: null,
        ),
        isNewTask: true,
      );
    // showAppActivity();
    // var response = await savePaymentDetailsForDeatination(
    //   holdPaymentId: _holdPaymentId,
    //   paymentCardId: null,
    //   debitFromWallet: widget.walletAmount,
    //   rideId: widget.rideId,
    // );
    // if (!response.status) {
    //   toast(response.message);
    // } else {
    //   /* Goto ride screen */
    //   launchScreen(
    //     const CurrentRideScreen(
    //       currentRide: null,
    //     ),
    //     isNewTask: true,
    //   );
    // }
    // hideAppActivity();
  }

  Future<void> _saveWaitingTimeAndTipPayment() async {
    showAppActivity();
    var response = await saveWaitingTimeAndTipPreAuth(
      holdPaymentId: _holdPaymentId,
      paymentCardId: null,
      rideRequestId: widget.rideId,
      advanced_paid: widget.tipAndWaitingData!.advancedPaid,
      due_amount: widget.tipAndWaitingData!.dueAmount,
      refundable_amount: widget.tipAndWaitingData!.refundableAmount,
      tips: widget.tipAndWaitingData!.tips,
      waiting_charges: widget.tipAndWaitingData!.waitingCharges,
      service_id: widget.tipAndWaitingData!.serviceId,
      pre_auth_amount: null,
      walletAmount: widget.walletAmount,
    );

    if (response.status) {
      launchScreen(const DashboardWrapperScreen(), isNewTask: true);
    } else {
      toast(response.message);
    }
    hideAppActivity();
  }
}
