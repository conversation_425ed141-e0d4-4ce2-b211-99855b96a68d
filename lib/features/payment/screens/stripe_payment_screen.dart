import 'package:mapbox_maps_flutter/mapbox_maps_flutter.dart';
import 'package:rider/app_exports.dart';
import 'package:rider/components/saved_payment_card.dart';
import 'package:rider/features/booking/models/save_ride_model.dart';
import 'package:rider/features/ride_flow/screen/current_ride_screen.dart';

import 'package:flutter_stripe/flutter_stripe.dart';
import 'package:rider/screens/account_screen.dart';
import 'package:rider/screens/add_new_card.dart';

import 'package:rider/services/stripe_service.dart';

class StripePaymentScreen extends StatefulWidget {
  final num payableAmount;
  final num? deductionAmount;
  final SelectServiceModel priceData;
  final dynamic otherRiderData;
  final bool isOTPEnable;
  final Position sourceLocation;
  final Position destinationLocation;
  final String startAddress;
  final String endAddress;
  final DateTime? scheduledRideTime;
  final CouponData? couponData;
  final bool isPoolingRide;
  final int personCount;
  final bool isBusinessRide;
  final bool is_peak;
  final String? couponCode;
  final num? couponDiscount;

  const StripePaymentScreen({
    super.key,
    required this.is_peak,
    required this.payableAmount,
    required this.deductionAmount,
    required this.priceData,
    required this.sourceLocation,
    required this.destinationLocation,
    required this.startAddress,
    required this.endAddress,
    required this.otherRiderData,
    required this.isOTPEnable,
    required this.scheduledRideTime,
    this.couponData,
    required this.isPoolingRide,
    required this.personCount,
    required this.isBusinessRide,
    this.couponCode,
    this.couponDiscount,
  });

  @override
  State<StripePaymentScreen> createState() => _StripePaymentScreenState();
}

class _StripePaymentScreenState extends State<StripePaymentScreen> {
  final List<SavedCard> _saveCardList = [];
  String? _selectedSavedCardId;
  String? _holdPaymentId;

  Future<void> _getSavedCardListFromStripe() async {
    showAppActivity();
    var response = await StripeService.getSavedCard(
      Globals.user.stripeCustomerId,
    );
    if (response == null) {
      toast(Globals.language.errorMsg);
    } else {
      for (var card in response) {
        if (!HelperMethods.isStripeCardExpired(
            expiryMonth: card.exp_month, expiryYear: card.exp_year)) {
          _saveCardList.add(card);
        }
      }
    }
    setState(() {
      _selectedSavedCardId = _saveCardList.isNotEmpty ? _saveCardList[0].id : null;
    });
    hideAppActivity();
  }

  _payWithStripe() async {
    _holdPaymentId = null;
    _selectedSavedCardId = null;
    showAppActivity();
    var response = await StripeService.getPaymentObject(
        payableAmount: widget.payableAmount,
        customerId: Globals.user.stripeCustomerId,
        userId: Globals.user.id.toString(),
        paymentType: PaymentType.rideBooking,
        walletDeductionAmount: widget.deductionAmount);
    if (response == null) {
      toast(Globals.language.errorMsg);
      hideAppActivity();
      return;
    }

    await Stripe.instance.initPaymentSheet(
      paymentSheetParameters: SetupPaymentSheetParameters(
        // googlePay: gpay,
        paymentIntentClientSecret: response.clientSecret,
        style: ThemeMode.light,
        // applePay: false,
        // googlePay: false,
        // testEnv: isTestType,
        allowsDelayedPaymentMethods: true,
        // merchantCountryCode: Globals.appStore.currencyName.toUpperCase(),
        merchantDisplayName: 'Rooo',
        // setupIntentClientSecret: res.clientSecret.validate(),
        // customerId: Platform.isIOS ? null : STRIPE_CUSTOMER_ID,
        // customerEphemeralKeySecret: Platform.isIOS ? null : STRIPE_CUSTOMER_KEY,
      ),
    );
    hideAppActivity();
    await Stripe.instance.presentPaymentSheet(
        // ignore: deprecated_member_use
        // parameters: PresentPaymentSheetParameters(
        //     clientSecret: res.clientSecret!, confirmPayment: true),
        );
    _holdPaymentId = response.id!;
    _saveRide();
  }

  Future<void> _saveRide() async {
    showAppActivity();
    SaveRideModel request = SaveRideModel(
      couponData: widget.couponData,
      peak_ride: widget.is_peak,
      isRideForOther: widget.otherRiderData != null,
      otherRiderData: widget.otherRiderData,
      serviceId: widget.priceData.id.toString(),
      isOTPSharingRequired: widget.isOTPEnable,
      isPoolingRide: widget.isPoolingRide,
      isBusinessRide: widget.isBusinessRide,
      personCount: widget.personCount,
      debitWallet: widget.deductionAmount,
      baseFare: widget.priceData.baseFare,
      baseDistance: widget.priceData.baseDistance,
      extraCharges: widget.priceData.extraCharges,
      extraChargesAmount: widget.priceData.extraChargesAmount,
      isScheduledRide: widget.scheduledRideTime != null,
      scheduledDate: widget.scheduledRideTime,
      minimumFare: widget.priceData.minimumFare,
      perDistance: widget.priceData.perDistance,
      perDistanceCharge: widget.priceData.perDistanceCharge,
      perMinuteDrive: widget.priceData.perMinuteDrive,
      perMinuteDriveCharge: widget.priceData.perMinuteDriveCharge,
      perMinuteWaiting: null,
      perMinuteWaitingCharge: null,
      // seatCount: "",
      subtotal: widget.priceData.subtotal,
      waitingTimeLimit: widget.priceData.waitingTimeLimit,
      totalAmount: widget.priceData.totalAmount,
      // vehicleId: "",
      couponCode: widget.couponCode,
      couponDiscount: widget.couponDiscount,
      status: RideStatus.newRideRequested,
      holdPaymentId: _holdPaymentId,
      paymentCardId: _selectedSavedCardId,
      riderId: Globals.user.id.toString(),
      startLatitude: widget.sourceLocation.lat.toString(),
      startLongitude: widget.sourceLocation.lng.toString(),
      startAddress: widget.startAddress,
      endLatitude: widget.destinationLocation.lat.toString(),
      endLongitude: widget.destinationLocation.lng.toString(),
      endAddress: widget.endAddress,
      paymentType: 'pre-authorized payment',
      tax: widget.priceData.tax,
      finalAmount: widget.payableAmount,
      distance: widget.priceData.distance,
      isMinimumAmountApplies: widget.priceData.isMinimumAmountApplies,
      stripe: widget.priceData.stripe,
      airportCharges: widget.priceData.airportCharges,
      duration: widget.priceData.duration,
    );

    LDBaseResponse response = await saveRideRequest(request.toJson());
    if (!(response.status ?? false)) {
      toast(response.message);
      hideAppActivity();
    } else {
      if (widget.scheduledRideTime != null) {
        showAppDialog(
          onAccept: () {
            launchScreen(
              const DashboardWrapperScreen(),
              isNewTask: true,
            );
          },
          dialogType: AppDialogType.info,
          title:
              "Your ride is scheduled for ${widget.scheduledRideTime!.hour}:${widget.scheduledRideTime!.minute}.\n\nWe will inform you when your ride is assigned to a driver.",
        );
        return;
      }

      /* Goto ride screen */

      launchScreen(
        const CurrentRideScreen(
          currentRide: null,
        ),
      );
    }
  }

  _init() {
    _getSavedCardListFromStripe();
  }

  @override
  void initState() {
    super.initState();

    _init();
  }

  @override
  dispose() {
    hideAppActivity();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      bottomNavigationBar: BottomButton(
        text: "Pay",
        onPressed: () {
          if (_selectedSavedCardId == null) {
            _payWithStripe();
          } else {
            _holdPaymentId = null;
            _saveRide();
          }
          // _getEstimatedPriceTime();
        },
      ),
      appBar: const RoooAppbar(title: "Pay with Debit/Credit Card"),
      body: Stack(
        children: [
          Padding(
            padding: const EdgeInsets.all(Layout.scaffoldBodyPadding),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Payment Summary Card
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Theme.of(context).cardColor,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.05),
                        blurRadius: 10,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "Payment Summary",
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            "Amount to Pay",
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Theme.of(context).textTheme.bodyMedium?.color?.withOpacity(0.7),
                            ),
                          ),
                          Text(
                            Constants.currencySymbol + widget.payableAmount.toString(),
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              color: Theme.of(context).primaryColor,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 24),

                // Saved Cards Section
                Text(
                  "Saved Cards",
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                Expanded(
                  child: _saveCardList.isEmpty && !isAppActivityRunning.value
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            InkWell(
                              onTap: _saveNewCard,
                              child: Container(
                                width: 120,
                                height: 120,
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  color: Theme.of(context).primaryColor.withOpacity(0.1),
                                ),
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(
                                      Icons.add,
                                      size: 36,
                                      color: Theme.of(context).primaryColor,
                                    ),
                                    const SizedBox(height: 8),
                                    Text(
                                      "Add new card",
                                      style: TextStyle(
                                        color: Theme.of(context).primaryColor,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      )
                    : ListView.builder(
                        itemCount: _saveCardList.length,
                        itemBuilder: (context, index) {
                          final card = _saveCardList[index];
                          final isSelected = _selectedSavedCardId == card.id;
                          return Container(
                            margin: const EdgeInsets.only(bottom: 12),
                            decoration: BoxDecoration(
                              color: Theme.of(context).cardColor,
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: isSelected 
                                  ? Theme.of(context).primaryColor 
                                  : Theme.of(context).dividerColor,
                                width: isSelected ? 2 : 1,
                              ),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withOpacity(0.05),
                                  blurRadius: 10,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                            child: InkWell(
                              onTap: () {
                                setState(() {
                                  if (_selectedSavedCardId == card.id) {
                                    _selectedSavedCardId = null;
                                  } else {
                                    _selectedSavedCardId = card.id;
                                  }
                                });
                              },
                              borderRadius: BorderRadius.circular(12),
                              child: Padding(
                                padding: const EdgeInsets.all(16),
                                child: Row(
                                  children: [
                                    Container(
                                      padding: const EdgeInsets.all(8),
                                      decoration: BoxDecoration(
                                        color: Theme.of(context).primaryColor.withOpacity(0.1),
                                        shape: BoxShape.circle,
                                      ),
                                      child: Icon(
                                        Icons.credit_card,
                                        color: Theme.of(context).primaryColor,
                                      ),
                                    ),
                                    const SizedBox(width: 16),
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            "•••• ${card.last4}",
                                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                          const SizedBox(height: 4),
                                          Text(
                                            "Expires ${card.exp_month}/${card.exp_year}",
                                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                              color: Theme.of(context).textTheme.bodyMedium?.color?.withOpacity(0.7),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    if (isSelected)
                                      Container(
                                        padding: const EdgeInsets.all(8),
                                        decoration: BoxDecoration(
                                          color: Theme.of(context).primaryColor.withOpacity(0.1),
                                          shape: BoxShape.circle,
                                        ),
                                        child: Icon(
                                          Icons.check,
                                          color: Theme.of(context).primaryColor,
                                          size: 20,
                                        ),
                                      ),
                                  ],
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                ),
                if (_saveCardList.isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.only(top: 16),
                    child: Center(
                      child: TextButton.icon(
                        onPressed: _saveNewCard,
                        icon: Icon(
                          Icons.add_circle_outline,
                          color: Theme.of(context).primaryColor,
                        ),
                        label: Text(
                          "Add another card",
                          style: TextStyle(
                            color: Theme.of(context).primaryColor,
                          ),
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          ),
          const ActivityIndicator(),
        ],
      ),
    );
  }

  Future<void> _saveNewCard() async {
    bool? result = await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const AddNewCardScreen(
          previousCards: [],
        ),
      ),
    );

    if (result == true) {
      _getSavedCardListFromStripe();
    }
  }
}
