import 'package:rider/app_exports.dart';
import 'package:rider/components/saved_payment_card.dart';
import 'package:flutter_stripe/flutter_stripe.dart';
import 'package:rider/screens/account_screen.dart';
import 'package:rider/screens/add_new_card.dart';
import 'package:rider/services/stripe_service.dart';

class WalletStripePayScreen extends StatefulWidget {
  final int payableAmount;

  const WalletStripePayScreen({
    super.key,
    required this.payableAmount,
  });

  @override
  State<WalletStripePayScreen> createState() => _WalletStripePayScreenState();
}

class _WalletStripePayScreenState extends State<WalletStripePayScreen> {
  final List<SavedCard> _saveCardList = [];
  String? _selectedSavedCardId;
  // String? _holdPaymentId;

  Future<void> _getSavedCardListFromStripe() async {
    showAppActivity();
    var response = await StripeService.getSavedCard(
      Globals.user.stripeCustomerId,
    );
    if (response == null) {
      toast(Globals.language.errorMsg);
    } else {
      for (var card in response) {
        if (!HelperMethods.isStripeCardExpired(
            expiryMonth: card.exp_month, expiryYear: card.exp_year)) {
          _saveCardList.add(card);
        }
      }
    }
    setState(() {
      _selectedSavedCardId =
          _saveCardList.isNotEmpty ? _saveCardList[0].id : null;
    });
    hideAppActivity();
  }

  _payWithStripe() async {
    // _holdPaymentId = null;
    _selectedSavedCardId = null;
    showAppActivity();
    var response = await StripeService.getWalletPaymentObject(
      payableAmount: widget.payableAmount,
      customerId: Globals.user.stripeCustomerId,
      userId: Globals.user.id.toString(),
      paymentType: PaymentType.wallet,
    );
    if (response == null) {
      toast(Globals.language.errorMsg);
      hideAppActivity();
      return;
    }

    await Stripe.instance.initPaymentSheet(
      paymentSheetParameters: SetupPaymentSheetParameters(
        // googlePay: gpay,
        paymentIntentClientSecret: response.clientSecret,
        style: ThemeMode.light,
        // applePay: false,
        // googlePay: false,
        // testEnv: isTestType,
        allowsDelayedPaymentMethods: true,
        // merchantCountryCode: Globals.appStore.currencyName.toUpperCase(),
        merchantDisplayName: 'Rooo',
        // setupIntentClientSecret: res.clientSecret.validate(),
        // customerId: Platform.isIOS ? null : STRIPE_CUSTOMER_ID,
        // customerEphemeralKeySecret: Platform.isIOS ? null : STRIPE_CUSTOMER_KEY,
      ),
    );
    hideAppActivity();
    await Stripe.instance.presentPaymentSheet();
    // _holdPaymentId = response.id!;
    closeScreen(isDone: true);
    // _saveRide();
  }

  Future<void> _payWithCard() async {
    showAppActivity();
    var response = await addWalletAmountWithStripeCard(
      paymentCardId: _selectedSavedCardId!,
      walletAmount: widget.payableAmount,
    );
    if (!response.status) {
      toast(Globals.language.errorMsg);
      hideAppActivity();
      return;
    } else {
      closeScreen(isDone: true);
    }
    hideAppActivity();
  }

  _init() {
    _getSavedCardListFromStripe();
  }

  @override
  void initState() {
    super.initState();

    _init();
  }

  @override
  dispose() {
    hideAppActivity();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      bottomNavigationBar: BottomButton(
        text: "Pay",
        onPressed: () {
          if (_selectedSavedCardId == null) {
            _payWithStripe();
          } else {
            // _holdPaymentId = null;
            _payWithCard();
          }
          // _getEstimatedPriceTime();
        },
      ),
      appBar: const RoooAppbar(title: "Pay with Stripe "),
      body: Stack(
        children: [
          _saveCardList.isEmpty && !isAppActivityRunning.value
              ? Center(
                  child: InkWell(
                  onTap: _saveNewCard,
                  child: Container(
                    width: 120,
                    height: 120,
                    decoration: const BoxDecoration(
                      shape: BoxShape.circle,
                      color: AppColors.lightThemePrimaryColor,
                    ),
                    child: const Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.add,
                          size: 36,
                        ),
                        Text("Add new card")
                      ],
                    ),
                  ),
                ))
              : Padding(
                  padding: const EdgeInsets.all(
                    Layout.scaffoldBodyPadding,
                  ),
                  child: CollapseableWidgets<SavedCard>(
                      data: _saveCardList,
                      onWidgetTap: (data) {
                        if (_selectedSavedCardId == data.id) {
                          _selectedSavedCardId = null;
                        } else {
                          _selectedSavedCardId = data.id;
                        }
                        setState(() {
                          //
                        });
                      },
                      widgets: _saveCardList
                          .map(
                            (data) => SavedCardForPayment(
                              card: data,
                              isSelected: _selectedSavedCardId == data.id,
                            ),
                          )
                          .toList()),
                ),

          // AnimationLimiter(
          //     child: ListView.separated(
          //       padding: const EdgeInsets.all(
          //         Layout.scaffoldBodyPadding,
          //       ),
          //       shrinkWrap: true,
          //       separatorBuilder: (context, index) => height10,
          //       itemCount: _saveCardList.length,
          //       itemBuilder: (BuildContext context, int index) {
          //         SavedCard data = _saveCardList[index];

          //         return AnimationConfiguration.staggeredList(
          //           position: index,
          //           duration: const Duration(milliseconds: 1000),
          //           child: SlideAnimation(
          //             verticalOffset: 50.0,
          //             child: FadeInAnimation(
          //               child: SavedCardForPayment(
          //                 card: data,
          //                 isSelected: _selectedSavedCardId == data.id,
          //                 // onSelect: () {
          //                 //   if (_selectedSavedCardId == data.id) {
          //                 //     _selectedSavedCardId = null;
          //                 //   } else {
          //                 //     _selectedSavedCardId = data.id;
          //                 //   }
          //                 //   setState(() {
          //                 //     //
          //                 //   });
          //                 // },
          //               ),
          //             ),
          //           ),
          //         );
          //       },
          //     ),
          //   ),
          // if (!isAppActivityRunning.value && _saveCardList.isEmpty)
          //   emptyWidget(emptyDataMsg: "No saved card found"),
          const ActivityIndicator(),
        ],
      ),
    );
  }

  Future<void> _saveNewCard() async {
    bool? result = await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const AddNewCardScreen(
          previousCards: [],
        ),
      ),
    );

    if (result == true) {
      _getSavedCardListFromStripe();
    }
  }

  void closeScreen({bool? isDone}) {
    Navigator.of(context).pop();
    if (isDone == true) {
      Navigator.of(context).pop(true);
    }
  }
}
