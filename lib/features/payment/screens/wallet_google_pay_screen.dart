import 'package:flutter/services.dart';
import 'package:flutter_stripe/flutter_stripe.dart';
import 'package:pay/pay.dart';
import 'package:rider/app_exports.dart';
import 'package:rider/services/stripe_service.dart';

class WalletGooglePayScreen extends StatefulWidget {
  final PaymentItem paymentItem;

  const WalletGooglePayScreen({
    super.key,
    required this.paymentItem,
  });

  @override
  State<WalletGooglePayScreen> createState() => _WalletGooglePayScreenState();
}

class _WalletGooglePayScreenState extends State<WalletGooglePayScreen> {
  // String _holdPaymentId = "";

  late final Pay _payClient;

  @override
  void initState() {
    showAppActivity();

    super.initState();

    _startPayment();
  }

  @override
  dispose() {
    hideAppActivity();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      appBar: RoooAppbar(title: "Pay with Google pay"),
      body: Stack(
        children: [
          ActivityIndicator(),
        ],
      ),
    );
  }

  String getGooglePayData() {
    return jsonEncode({
      "provider": "google_pay",
      "data": {
        // "environment": "TEST",
        "environment": "production",
        "apiVersion": 2,
        "apiVersionMinor": 0,
        "allowedPaymentMethods": [
          {
            "type": "CARD",
            "tokenizationSpecification": {
              "type": "PAYMENT_GATEWAY",
              "parameters": {
                "gateway": "stripe",
                "stripe:version": "2023-10-16",
                "stripe:publishableKey": StripeService.publishableKey,
              }
            },
            "parameters": {
              "allowedCardNetworks": ["VISA"],
              "allowedAuthMethods": ["PAN_ONLY", "CRYPTOGRAM_3DS"],
              "billingAddressRequired": false,
              "billingAddressParameters": {
                "format": "FULL",
                "phoneNumberRequired": true
              }
            }
          }
        ],
        "merchantInfo": {
          "merchantId": "BCR2DN4TS6I5N2YJ",
          "merchantName": "ROOO"
        },
        "transactionInfo": {"countryCode": "AU", "currencyCode": "AUD"}
      }
    });
  }

  Future<void> _startPayment() async {
    try {
      var response = await StripeService.getPaymentTypeList();
      if (!response) {
        toast(Globals.language.errorMsg);
        return closeScreen();
      }

      _payClient = Pay(
        {
          PayProvider.google_pay: PaymentConfiguration.fromJsonString(
            getGooglePayData(),
          ),
        },
      );

      final result = await _payClient.showPaymentSelector(
        PayProvider.google_pay,
        [widget.paymentItem],
      );
      await _onGooglePayResult(result);
    } on PlatformException catch (e) {
      if (e.code.toString() == "paymentCanceled") {
        closeScreen();
        toast("You cancelled the payment");
      }
    } catch (e) {
      closeScreen();
      toast(Globals.language.errorMsg);
    }
  }

  Future<void> _onGooglePayResult(Map<String, dynamic> result) async {
    var response = await StripeService.getWalletPaymentObject(
      payableAmount: int.parse(widget.paymentItem.amount),
      customerId: Globals.user.stripeCustomerId,
      userId: Globals.user.id.toString(),
      paymentType: PaymentType.wallet,
    );

    if (response == null) {
      toast(Globals.language.errorMsg);
      hideAppActivity();
      return;
    }

    final token = result['paymentMethodData']['tokenizationData']['token'];
    final tokenJson = Map.castFrom(json.decode(token));

    final params = PaymentMethodParams.cardFromToken(
      paymentMethodData: PaymentMethodDataCardFromToken(
        token: tokenJson['id'],
      ),
    );

    await Stripe.instance.confirmPayment(
      paymentIntentClientSecret: response.clientSecret!,
      data: params,
    );
    // _holdPaymentId = response.id!;
    closeScreen(isDone: true);
    // await _saveRide();
  }

  void closeScreen({bool? isDone}) {
    Navigator.of(context).pop();
    if (isDone == true) {
      Navigator.of(context).pop(true);
    }
  }
}
