import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl_phone_field/intl_phone_field.dart';
import 'package:pinput/pinput.dart';
import 'package:rider/app_exports.dart';
import 'package:rider/components/rooo_appbar.dart';
import 'package:rider/features/edit_profile/cubit/edit_profile_cubit.dart';
import 'package:rider/global/widgets/bottom_button.dart';
import 'package:rider/global/widgets/screen_body.dart';
import 'package:rider/globals.dart';
import 'package:rider/utils/Common.dart';
import 'package:rider/utils/Extensions/app_common.dart';

class ChangePhoneNumberScreen extends StatefulWidget {
  final String mobileNumber;

  const ChangePhoneNumberScreen({super.key, required this.mobileNumber});

  @override
  State<ChangePhoneNumberScreen> createState() =>
      _ChangePhoneNumberScreenState();
}

class _ChangePhoneNumberScreenState extends State<ChangePhoneNumberScreen> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  final TextEditingController _codeController = TextEditingController();

  final FocusNode _codeFocus = FocusNode();
  final FocusNode _mobileFocus = FocusNode();
  String _mobileNumber = '';
  final String _countryCode = '+61';
  ValueNotifier<bool> isSendOTP = ValueNotifier(false);

  void _sendVerificationCode() {
    hideKeyboard();
    if (_mobileNumber.isEmpty) {
      toast(Globals.language.enterYourMobileNumber);
      return;
    } else if (_countryCode + _mobileNumber.trim() == widget.mobileNumber) {
      toast("It is your current mobile number");
      return;
    }

    if (_formKey.currentState!.validate()) {
      _formKey.currentState!.save();

      Map request = {
        'contact_number2': _countryCode + _mobileNumber,
      };
      BlocProvider.of<EditProfileCubit>(context)
          .sendVerificationCode(request: request);
    }
  }

  _changeMobileNumber() {
    hideKeyboard();
    if (_formKey.currentState!.validate()) {
      _formKey.currentState!.save();
      if (_codeController.text.trim().length != 6) {
        toast(Globals.language.enterOTP);
      } else {
        Map request = {
          'mobile': _countryCode + _mobileNumber,
          'otp': _codeController.text.trim(),
        };
        BlocProvider.of<EditProfileCubit>(context)
            .changeMobileNumber(request: request);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      bottomNavigationBar: BlocBuilder<EditProfileCubit, EditProfileState>(
        builder: (context, state) {
          return ValueListenableBuilder<bool>(
            valueListenable: isSendOTP,
            builder: (context, value, child) {
              if (value) {
                return BottomButton(
                    text: Globals.language.changeMobile,
                    onPressed: _changeMobileNumber,
                    notVisible: state is EditProfileLoadingState);
              } else {
                return BottomButton(
                    text: "Verify phone",
                    onPressed: _sendVerificationCode,
                    notVisible: state is EditProfileLoadingState);
              }
            },
          );
        },
      ),
      appBar: const RoooAppbar(title: "Change Mobile Number"),
      body: GestureDetector(
        onTap: () {
          hideKeyboard();
        },
        child: BlocConsumer<EditProfileCubit, EditProfileState>(
          listener: (context, state) {
            if (state is EditProfileErrorState) {
              toast(Globals.language.errorMsg);
              isSendOTP.value = false;
            } else if (state is EditProfileVerificationSentState) {
              if (state.response.status == false) {
                toast(state.response.message.toString());
              } else if (state.response.status == true) {
                isSendOTP.value = true;
                toast(Globals.language.enterOTP);
              }
            } else if (state is EditProfileMobileNumberChangedState) {
              if (state.response.status == true) {
                showAppDialog(
                    onAccept: () {
                      Navigator.of(context).pop();
                      Navigator.of(context).pop();
                    },
                    dialogType: AppDialogType.info,
                    title: state.response.message);
              } else if (state.response.status == false) {
                toast(state.response.message.toString());

                _codeController.clear();
              } else {
                toast(
                  Globals.language.errorMsg,
                );
              }
            }
          },
          builder: (context, state) {
            return ScreenBody(
              isLoading: state is EditProfileLoadingState,
              isEmpty: false,
              emptyMessage: "",
              child: Form(
                key: _formKey,
                child: Padding(
                  padding: Constants.screenPadding,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      TextFormField(
                        autofocus: true,
                        maxLength: 10,
                        validator: (value) {
                          if ((value?.length ?? 0) < 9) {
                            return "Please enter a valid mobile number";
                          }
                          return null;
                        },
                        onChanged: (value) {
                          _mobileNumber = value;
                          isSendOTP.value = false;
                        },
                        keyboardType: TextInputType.phone,
                        inputFormatters: [
                          FilteringTextInputFormatter.digitsOnly,
                        ],
                        decoration: InputDecoration(
                          counterText: "",
                            prefix: Padding(
                              padding: const EdgeInsets.only(right: 8),
                              child: Text(
                                "+61",
                                style: AppTextStyles.title,
                              ),
                            ),
                            border: const OutlineInputBorder()),
                      ),
                      ValueListenableBuilder<bool>(
                        valueListenable: isSendOTP,
                        builder: (context, value, child) {
                          return Visibility(
                            visible: value,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const SizedBox(height: 20),
                                Text(
                                  Globals.language.verifyOTPHeading,
                                  style: const TextStyle(),
                                ),
                                height10,
                                Pinput(
                                  controller: _codeController,
                                  length: 6,
                                  onCompleted: (pin) {
                                    // _otp = pin;
                                  },
                                ),
                              ],
                            ),
                          );
                        },
                      )
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
