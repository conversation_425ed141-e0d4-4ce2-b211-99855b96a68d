import 'dart:io';
import 'package:http/http.dart';
import 'package:rider/features/edit_profile/model/region_response_model.dart';
import 'package:rider/global/models/status_message_model.dart';
import 'package:rider/model/login_response.dart';
import 'package:rider/network/network_utils.dart';
import 'package:rider/network/rest_apis.dart';
import 'package:rider/screens/register_screen.dart';

class EditProfileRepository {
  Future<UserDetailsResponse> getUserDetail({required int? userId}) async {
    return UserDetailsResponse.fromJson(await handleResponse(
        await buildHttpResponse('user-detail?id=$userId',
            method: HttpMethod.get)));
  }

  Future<RegionResponseModel> getRegionListAPi() async {
    return RegionResponseModel.fromJson(
        await handleResponse(await buildHttpResponse(
      'region-list',
      method: HttpMethod.get,
    )));
  }

  Future<ProvinceModelResponse> getProvinceListAPi() async {
    return ProvinceModelResponse.fromJson(
        await handleResponse(await buildHttpResponse(
      'province-list',
      method: HttpMethod.get,
    )));
  }

  Future<StatusMessageModel> updateDriverProfileApi(
      {required Map request}) async {
    return StatusMessageModel.fromJson(await handleResponse(
        await buildHttpResponse('update-profile',
            method: HttpMethod.post, request: request)));
  }

  Future<StatusMessageModel> updateGenderApi({required Map request}) async {
    return StatusMessageModel.fromJson(await handleResponse(
        await buildHttpResponse('update-user-status',
            method: HttpMethod.post, request: request)));
  }

  Future<StatusMessageModel> verifyEmailApi() async {
    return StatusMessageModel.fromJson(
        await handleResponse(await buildHttpResponse(
      'sent-verification-email',
      method: HttpMethod.get,
    )));
  }

  Future<StatusMessageModel> changeEmailApi({required Map request}) async {
    return StatusMessageModel.fromJson(await handleResponse(
        await buildHttpResponse('update-email',
            method: HttpMethod.post, request: request)));
  }

  Future<StatusMessageModel> sendVerificationCodeApi(
      {required Map request}) async {
    return StatusMessageModel.fromJson(await handleResponse(
        await buildHttpResponse('update-contact-number',
            method: HttpMethod.post, request: request)));
  }

  Future<StatusMessageModel> changeMobileNumberApi(
      {required Map request}) async {
    return StatusMessageModel.fromJson(await handleResponse(
        await buildHttpResponse('validate-otp-contact2',
            method: HttpMethod.post, request: request)));
  }

  Future<StreamedResponse?> changeProfileImage({
    required File file,
  }) async {
    return await updateProfileImage(file: file);
  }

  Future<StatusMessageModel> updateRegionIdProvinceIdApi(
      {required Map request}) async {
    return StatusMessageModel.fromJson(await handleResponse(
        await buildHttpResponse('update-profile',
            method: HttpMethod.post, request: request)));
  }
}
