import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rider/app_exports.dart';
import 'package:rider/features/edit_profile/repository/edit_profile_repository.dart';
import 'package:rider/global/models/status_message_model.dart';
import 'package:rider/model/app_setting_model.dart';

abstract class EditProfileState {}

class EditProfileLoadedState extends EditProfileState {}

class EditProfileInitialState extends EditProfileState {}

class EditProfileLoadingState extends EditProfileState {}

class EditProfileErrorState extends EditProfileState {
  final String? handledErrorMessage;
  final String? unHandledErrorMessage;

  EditProfileErrorState({this.handledErrorMessage, this.unHandledErrorMessage});
}

class EditProfileUserLoadedState extends EditProfileState {
  final UserModel userDetail;

  EditProfileUserLoadedState({required this.userDetail});
}

class EditProfileNameRequestSentState extends EditProfileState {
  final String message;

  EditProfileNameRequestSentState({required this.message});
}

class EmailVerificationLinkSent extends EditProfileState {
  EmailVerificationLinkSent();
}

class EditProfileEmailRequestSentState extends EditProfileState {
  final String message;
  EditProfileEmailRequestSentState({required this.message});
}

class EditProfileVerificationSentState extends EditProfileState {
  final StatusMessageModel response;

  EditProfileVerificationSentState({required this.response});
}

class EditProfileMobileNumberChangedState extends EditProfileState {
  final StatusMessageModel response;

  EditProfileMobileNumberChangedState({required this.response});
}

class RegionIdUpdatedState extends EditProfileState {
  String message;
  RegionIdUpdatedState({required this.message});
}

class GenderUpdatedState extends EditProfileState {
  GenderUpdatedState();
}

class EditProfileImageRequestSentState extends EditProfileState {
  final String message;

  EditProfileImageRequestSentState({required this.message});
}

class EditProfileRegionListLoadedState extends EditProfileState {
  final List<RegionModel> regionList;

  EditProfileRegionListLoadedState({required this.regionList});
}

class EditProfileProvinceListLoadedState extends EditProfileState {
  final List<ProvinceModel> provinceList;

  EditProfileProvinceListLoadedState({required this.provinceList});
}

class EditProfileCubit extends Cubit<EditProfileState> {
  EditProfileRepository _editProfileRepository = EditProfileRepository();
  EditProfileCubit() : super(EditProfileInitialState());

  getUserDetails({required int userId}) async {
    emit(EditProfileLoadingState());
    await _editProfileRepository.getUserDetail(userId: userId).then((value) {
      if (!value.status) {
        emit(EditProfileErrorState(unHandledErrorMessage: value.message));
      } else {
        Globals.user = value.data!;
        emit(EditProfileUserLoadedState(userDetail: value.data!));
      }
    }).onError((error, stackTrace) {
      emit(EditProfileErrorState(unHandledErrorMessage: error.toString()));
    });
  }

  getRegionList({required int userId}) async {
    emit(EditProfileLoadingState());
    await _editProfileRepository.getRegionListAPi().then((value) {
      if (value.data != null) {
        emit(EditProfileRegionListLoadedState(regionList: value.data!));
      } else {
        emit(EditProfileErrorState(
            unHandledErrorMessage: Constants.serverErrorMessage));
      }
      // if (value.status) {
      //     RegionListLoadedState(regionList: value.data!);
      // } else {
      //   emit(EditProfileErrorState(handledErrorMessage: value.message));
      // }
    }).onError((error, stackTrace) {
      emit(EditProfileErrorState(unHandledErrorMessage: error.toString()));
    });
  }

  getprovinceList() async {
    emit(EditProfileLoadingState());
    await _editProfileRepository.getProvinceListAPi().then((value) {
      if (value.data != null) {
        emit(EditProfileProvinceListLoadedState(provinceList: value.data!));
      } else {
        emit(EditProfileErrorState(
            unHandledErrorMessage: Constants.serverErrorMessage));
      }
      // if (value.status) {
      // } else {
      //   emit(EditProfileErrorState(handledErrorMessage: value.message));
      // }
    }).onError((error, stackTrace) {
      emit(EditProfileErrorState(unHandledErrorMessage: error.toString()));
    });
  }

  updateRegionProvinceId({required Map<String, dynamic> request}) async {
    emit(EditProfileLoadingState());
    await _editProfileRepository
        .updateDriverProfileApi(request: request)
        .then((value) {
      if (value.status) {
        emit(RegionIdUpdatedState(message: value.message));
      } else {
        emit(EditProfileErrorState(handledErrorMessage: value.message));
      }
    }).onError((e, _) {
      emit(EditProfileErrorState(unHandledErrorMessage: e.toString()));
    });
  }

  updateGender({required Map<String, dynamic> request}) async {
    emit(EditProfileLoadingState());
    await _editProfileRepository
        .updateGenderApi(request: request)
        .then((value) {
      if (value.status) {
        emit(GenderUpdatedState());
      } else {
        emit(EditProfileErrorState(handledErrorMessage: value.message));
      }
    }).onError((e, _) {
      emit(EditProfileErrorState(unHandledErrorMessage: e.toString()));
    });
  }

  editName({required Map request}) async {
    emit(EditProfileLoadingState());
    await _editProfileRepository
        .updateDriverProfileApi(request: request)
        .then((value) {
      if (value.status) {
        emit(
            EditProfileNameRequestSentState(message: value.message.toString()));
      } else {
        emit(EditProfileErrorState(handledErrorMessage: value.message));
      }
    }).onError((error, stackTrace) {
      emit(EditProfileErrorState(unHandledErrorMessage: error.toString()));
    });
  }

  verifyEmail() async {
    emit(EditProfileLoadingState());
    await _editProfileRepository.verifyEmailApi().then((value) {
      if (value.status) {
        emit(EmailVerificationLinkSent());
      } else {
        emit(EditProfileErrorState(handledErrorMessage: value.message));
      }
    }).onError((error, stackTrace) {
      emit(EditProfileErrorState(unHandledErrorMessage: error.toString()));
    });
  }

  Future<void> editEmail({
    required String email,
    required String otp,
  }) async {
    emit(EditProfileLoadingState());

    var request = {
      "email2": email,
      "otp": otp,
      "user_type": "rider",
    };
    await _editProfileRepository.changeEmailApi(request: request).then((value) {
      if (value.status) {
        emit(EditProfileEmailRequestSentState(
            message: value.message.toString()));
      } else {
        emit(EditProfileErrorState(handledErrorMessage: value.message));
      }
    }).onError((error, stackTrace) {
      emit(EditProfileErrorState(handledErrorMessage: "Something went wrong"));
    });
  }

  sendVerificationCode({required Map request}) async {
    emit(EditProfileLoadingState());
    await _editProfileRepository
        .sendVerificationCodeApi(request: request)
        .then((value) {
      emit(EditProfileVerificationSentState(response: value));
    }).onError((error, stackTrace) {
      emit(EditProfileErrorState(unHandledErrorMessage: error.toString()));
    });
  }

  changeMobileNumber({required Map request}) async {
    emit(EditProfileLoadingState());
    await _editProfileRepository
        .changeMobileNumberApi(request: request)
        .then((value) {
      emit(EditProfileMobileNumberChangedState(response: value));
    }).onError((error, stackTrace) {
      emit(EditProfileErrorState(unHandledErrorMessage: error.toString()));
    });
  }

  updateProfileImage({required File file}) async {
    emit(EditProfileLoadingState());

    _editProfileRepository.changeProfileImage(file: file).then((response) {
      if (response == null) {
        emit(EditProfileErrorState(
            handledErrorMessage: Globals.language.errorMsg));
      } else {
        ///

        try {
          response.stream.transform(utf8.decoder).listen((value) async {
            var json = jsonDecode(value);
            StatusMessageModel res = StatusMessageModel.fromJson(json);
            if (res.status) {
              emit(EditProfileImageRequestSentState(message: res.message));
            } else {
              emit(EditProfileErrorState(handledErrorMessage: res.message));
            }
          });
        } catch (e) {
          emit(EditProfileErrorState(
              handledErrorMessage: Globals.language.errorMsg));
        }
      }
    }).onError((e, _) {
      emit(EditProfileErrorState(unHandledErrorMessage: e.toString()));
    });

    // MultipartRequest multiPartRequest =
    //     await getMultiPartRequest('update-driver-profile');

    // multiPartRequest.files
    //     .add(await MultipartFile.fromPath('profile_image', file.path));
    // multiPartRequest.fields['type'] = 'profile_picture';
    // await send(
    //   multiPartRequest,
    // ).then((value) {
    //   if (value != null) {
    //     if (value.status) {
    //       emit(EditProfileImageRequestSentState(
    //           message: value.message.toString()));
    //     } else {
    //       emit(EditProfileErrorState(handledErrorMessage: value.message));
    //     }
    //   } else {
    //     emit(EditProfileErrorState());
    //   }
    // }).onError((e, _) {
    //   emit(EditProfileErrorState(unHandledErrorMessage: e.toString()));
    // });
  }
}
