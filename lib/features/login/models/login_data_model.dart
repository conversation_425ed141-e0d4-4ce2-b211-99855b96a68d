class LoginData {


  
  // Null? user;
  String? key;
  bool? isUserExists;

  LoginData({this.key, this.isUserExists});

  LoginData.fromJson(Map<String, dynamic> json) {
    // user = json['user'];
    key = json['key'];
    isUserExists = json['is_user_exists'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    // data['user'] = this.user;
    data['key'] = key;
    data['is_user_exists'] = isUserExists;
    return data;
  }
}
