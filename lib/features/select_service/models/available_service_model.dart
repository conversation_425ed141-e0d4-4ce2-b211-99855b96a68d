class AvailableService {
  int id;
  String userType;
  String playerId;
  String latitude;
  String longitude;
  num distance;

  AvailableService({
    required this.id,
    required this.userType,
    required this.playerId,
    required this.latitude,
    required this.longitude,
    required this.distance,
  });

  factory AvailableService.fromJson(Map<String, dynamic> json) {
    return AvailableService(
      id: json['id'],
      userType: json['user_type'],
      playerId: json['player_id'] ?? "",
      latitude: json['latitude'],
      longitude: json['longitude'],
      distance: json['distance'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_type': userType,
      'player_id': playerId,
      'latitude': latitude,
      'longitude': longitude,
      'distance': distance,
    };
  }
}
