import 'package:rider/app_exports.dart';
import 'package:rider/features/select_service/models/available_service_model.dart';

class SelectServiceModel {
  int id;
  num serviceId;
  bool isPeakArea;
  String name;
  num regionId;
  String distanceUnit;
  num dropoffDistanceInKm;
  num duration;
  int capacity;
  num baseFare;
  num minimumFare;
  num? minimumDistance;
  num perDistance;
  num perMinuteDrive;
  num cancellationFee;
  String paymentMethod;
  String serviceImage;
  num status;
  String createdAt;
  String updatedAt;
  String? description;
  num extraCharges;
  List<AvailableService> drivers;
  num? baseDistance;
  num subtotalForCoupon;
  num perDistanceCharge;
  num perMinuteDriveCharge;
  num subtotal;
  num totalAmount;
  num extraChargesAmount;
  num couponDiscount;
  String? couponCode;
  CouponData? couponData;
  num tax;
  num waitingTimeLimit;
  num distance;
  num price;
  bool isPooling;
  bool isMinimumAmountApplies;
  num stripe;
  num airportCharges;

  SelectServiceModel({
    required this.id,
    required this.serviceId,
    required this.isPeakArea,
    required this.name,
    required this.regionId,
    required this.distanceUnit,
    required this.dropoffDistanceInKm,
    required this.duration,
    required this.capacity,
    required this.baseFare,
    required this.minimumFare,
    this.minimumDistance,
    required this.perDistance,
    required this.perMinuteDrive,
    required this.cancellationFee,
    required this.paymentMethod,
    required this.serviceImage,
    required this.status,
    required this.createdAt,
    required this.updatedAt,
    this.description,
    required this.extraCharges,
    required this.drivers,
    this.baseDistance,
    required this.subtotalForCoupon,
    required this.perDistanceCharge,
    required this.perMinuteDriveCharge,
    required this.subtotal,
    required this.totalAmount,
    required this.extraChargesAmount,
    required this.couponDiscount,
    this.couponCode,
    this.couponData,
    required this.tax,
    required this.waitingTimeLimit,
    required this.distance,
    required this.price,
    required this.isPooling,
    required this.isMinimumAmountApplies,
    required this.stripe,
    required this.airportCharges,
  });

  factory SelectServiceModel.fromJson(Map<String, dynamic> json) {
    return SelectServiceModel(
      id: json['id'],
      serviceId: json['service_id'],
      isPeakArea: json['is_peak'] ?? false,
      name: json['name'],
      regionId: json['region_id'],
      distanceUnit: json['distance_unit'],
      dropoffDistanceInKm: json['dropoff_distance_in_km'],
      capacity: json['capacity'],
      baseFare: json['base_fare'],
      minimumFare: json['minimum_fare'] ?? 0,
      minimumDistance: json['minimum_distance'],
      perDistance: json['per_distance'],
      perMinuteDrive: json['per_minute_drive'],
      cancellationFee: json['cancellation_fee'],
      paymentMethod: json['payment_method'],
      serviceImage: json['service_image'],
      status: json['status'],
      createdAt: json['created_at'],
      updatedAt: json['updated_at'],
      description: json['description'],
      extraCharges: json['extra_charges'],
      drivers: json['drivers'] == null
          ? []
          : (json['drivers'] as List)
              .map((driver) => AvailableService.fromJson(driver))
              .toList(),
      baseDistance: json['base_distance'],
      subtotalForCoupon: json['subtotalForCoupon'],
      perDistanceCharge: json['per_distance_charge'],
      perMinuteDriveCharge: json['per_minute_drive_charge'],
      subtotal: json['subtotal'],
      totalAmount: json['total_amount'],
      extraChargesAmount: json['extra_charges_amount'],
      couponDiscount: json['coupon_discount'],
      couponCode: json['coupon_code'],
      couponData: json['coupon_data'] == null
          ? null
          : CouponData.fromJson(json['coupon_data']),
      tax: json['tax'],
      waitingTimeLimit: json['waiting_time_limit'],
      distance: json['distance'],
      price: json['price'],
      isPooling: json['is_pooling'],
      isMinimumAmountApplies: json['is_minimum_amount_applies'],
      stripe: json['stripe'],
      airportCharges: json['airport_charges'],
      duration: json['duration'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'service_id': serviceId,
      'name': name,
      'region_id': regionId,
      'distance_unit': distanceUnit,
      'dropoff_distance_in_km': dropoffDistanceInKm,
      'duration': duration,
      'capacity': capacity,
      'base_fare': baseFare,
      'minimum_fare': minimumFare,
      'minimum_distance': minimumDistance,
      'per_distance': perDistance,
      'per_minute_drive': perMinuteDrive,
      'cancellation_fee': cancellationFee,
      'payment_method': paymentMethod,
      'service_image': serviceImage,
      'status': status,
      'created_at': createdAt,
      'updated_at': updatedAt,
      'description': description,
      'extra_charges': extraCharges,
      'drivers': drivers.map((driver) => driver.toJson()).toList(),
      'base_distance': baseDistance,
      'subtotalForCoupon': subtotalForCoupon,
      'per_distance_charge': perDistanceCharge,
      'per_minute_drive_charge': perMinuteDriveCharge,
      'subtotal': subtotal,
      'total_amount': totalAmount,
      'extra_charges_amount': extraChargesAmount,
      'coupon_discount': couponDiscount,
      'coupon_code': couponCode,
      'coupon_data': couponData?.toJson(),
      'tax': tax,
      'waiting_time_limit': waitingTimeLimit,
      'distance': distance,
      'price': price,
      'is_pooling': isPooling,
      'is_minimum_amount_applies': isMinimumAmountApplies,
      'stripe': stripe,
      'airport_charges': airportCharges,
    };
  }
}
