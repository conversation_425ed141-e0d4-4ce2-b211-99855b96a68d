import 'package:rider/features/select_service/models/select_service_model.dart';

class SelectServiceResponseModel {
  bool status;
  List<SelectServiceModel> data;
  String? message;

  SelectServiceResponseModel({
    required this.status,
    required this.data,
    this.message,
  });

  factory SelectServiceResponseModel.fromJson(Map<String, dynamic> json) {
    List<SelectServiceModel> data = [];
    if (json['data'] != null && json['data'] is List) {
      for (var i = 0; i < json['data'].length; i++) {
        data.add(
          SelectServiceModel.fromJson(
            json['data'][i]['request_data'],
          ),
        );
      }
    }

    return SelectServiceResponseModel(
      status: json['status'],
      data: data,
      message: json['message'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'data': data.map((data) => data.toJson()).toList(),
      'message': message,
    };
  }
}
