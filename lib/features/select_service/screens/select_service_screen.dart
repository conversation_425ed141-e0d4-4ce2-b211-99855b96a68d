import 'package:flutter/services.dart';
import 'package:mapbox_maps_flutter/mapbox_maps_flutter.dart' as mp;
import 'package:rider/app_exports.dart';
import 'package:rider/global/widgets/map_box_wrapper.dart';
import 'package:rider/main.dart';

import 'package:http/http.dart' as http;
import 'dart:ui' as ui;

class SelectServiceScreen extends StatefulWidget {
  final mp.Position pickupLocation;
  final mp.Position destinlationLocation;
  final String sourceAddress;
  final String destinationAddress;
  final DateTime? scheduledTime;
  final bool isPoolingRide;
  final int personCount;
  final bool isBusinessRide;
  const SelectServiceScreen({
    super.key,
    required this.pickupLocation,
    required this.destinlationLocation,
    required this.sourceAddress,
    required this.destinationAddress,
    required this.scheduledTime,
    required this.isPoolingRide,
    required this.personCount,
    required this.isBusinessRide,
  });

  @override
  State<SelectServiceScreen> createState() => _SelectServiceScreenState();
}

class _SelectServiceScreenState extends State<SelectServiceScreen> {
  List<SelectServiceModel> _selectServiceList = [];
  bool _showBottomButton = false;
  int _selectedServiceId = -1;
  bool isThereAnyPeakAreaService = false;

  void _addCarMarkers({required List<SelectServiceModel> data}) {
    List<AvailableService> availableService = [];
    for (var element in data) {
      for (var element in element.drivers) {
        availableService.add(element);

        _createMarkerPoint(
            iconSize: .3,
            location: mp.Position(double.parse(element.longitude),
                double.parse(element.latitude)),
            icon: carIcon);
      }
    }
  }

  void _addSourceMarker() {
    _createMarkerPoint(
        iconSize: .5, location: widget.pickupLocation, icon: sourceIcon);
  }

  void _addDestinationMarker() {
    _createMarkerPoint(
        iconSize: .09,
        location: widget.destinlationLocation,
        icon: destinationIcon);
  }

  _filterMarkers(
      {required List<SelectServiceModel> data,
      required int selectedServiceId}) {
    Globals.pointAnnotationManager?.deleteAll();
    _addDestinationMarker();
    _addSourceMarker();

    for (var element in data) {
      if (element.id == selectedServiceId) {
        for (var element in element.drivers) {
          _createMarkerPoint(
              iconSize: .3,
              location: mp.Position(double.parse(element.longitude),
                  double.parse(element.latitude)),
              icon: carIcon);
        }
      }
    }
  }

  _getServicesList() async {
    showAppActivity();
    Map request = {
      "pick_lat": widget.pickupLocation.lat,
      "pick_lng": widget.pickupLocation.lng,
      "drop_lat": widget.destinlationLocation.lat,
      "drop_lng": widget.destinlationLocation.lng,
      "is_pool": widget.isPoolingRide,
      "is_scheduled_ride": widget.scheduledTime != null,
    };

    SelectServiceResponseModel response =
        await getServicesList(request: request);

    if (!response.status) {
      toast(response.message);
    } else {
      _selectServiceList = response.data;
/* filter services according to person count */
      _selectServiceList = _selectServiceList
          .where((element) => element.capacity >= widget.personCount)
          .toList();

      isThereAnyPeakAreaService =
          _selectServiceList.any((element) => element.isPeakArea);

      if (_selectServiceList.isEmpty) {
        Navigator.of(context).pop();
        showAppDialog(
          onAccept: () {
            Navigator.of(navigatorKey.currentContext!).pop();
          },
          dialogType: AppDialogType.info,
          title: "No service found",
        );
      }

      // await _prepareMarkers(context);

      _setPolyLines(
          initialLat: widget.pickupLocation.lat.toDouble(),
          initialLang: widget.pickupLocation.lng.toDouble(),
          finalLat: widget.destinlationLocation.lat.toDouble(),
          finalLang: widget.destinlationLocation.lng.toDouble());

      _addCarMarkers(data: response.data);
      _addDestinationMarker();
      _addSourceMarker();

      setState(() {});
    }

    hideAppActivity();
  }

  _createMarkerPoint(
      {required mp.Position location,
      required String icon,
      required double iconSize}) async {
    Globals.pointAnnotationManager =
        await Globals.mapbox!.annotations.createPointAnnotationManager();
    final ByteData bytes = await rootBundle.load(icon);
    final Uint8List list = bytes.buffer.asUint8List();

    Globals.pointAnnotationManager!.create(mp.PointAnnotationOptions(
        image: list,
        iconSize: iconSize,
        geometry: mp.Point(coordinates: location)));
  }

  Future<List<mp.Position>> getRouteCoordinates(
      mp.Position origin, mp.Position destination) async {
    // Replace with your token
    final url = Uri.parse(
        'https://api.mapbox.com/directions/v5/mapbox/driving/${origin.lng},${origin.lat};${destination.lng},${destination.lat}?geometries=geojson&overview=full&steps=true&access_token=${AppCred.mapBoxPublicTokenKey}');

    final response = await http.get(url);

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      final route = data['routes'][0];
      final geometry = route['geometry'];
      final coordinates = geometry['coordinates'];

      List<mp.Position> polylineCoordinates = [];
      for (var coord in coordinates) {
        polylineCoordinates.add(mp.Position(coord[0], coord[1]));
      }

      return polylineCoordinates;
    } else {
      throw Exception('Failed to load route');
    }
  }

  _createPolyline(
      {required mp.Position startLocation,
      required mp.Position endLocation,
      required int colors}) async {
    List<mp.Position> coordinates =
        await getRouteCoordinates(startLocation, endLocation);

    // coordinates.forEach((v){
    //   createMarkerPoint(currentLocation: v);

    // });

    Globals.polylineAnnotationManager!.create(mp.PolylineAnnotationOptions(
        lineOpacity: 1,
        lineWidth: 4,
        lineBorderWidth: 4,
        lineBorderColor: colors,
        geometry: mp.LineString(coordinates: coordinates)));
  }

  Future<void> _setPolyLines({
    required double initialLat,
    required double initialLang,
    required double finalLat,
    required double finalLang,
  }) async {
    // Globals.polylineAnnotationManager?.deleteAll();
    _createPolyline(
        startLocation: mp.Position(initialLang, initialLat),
        endLocation: mp.Position(finalLang, finalLat),
        colors: Colors.black.value);

    // _polylineCoordinates.clear();
    // _polyLines.clear();

    // await _polylinePoints
    //     .getRouteBetweenCoordinates(
    //   request: PolylineRequest(
    //     origin: PointLatLng(initialLat, initialLang),
    //     destination: PointLatLng(finalLat, finalLang),
    //     mode: TravelMode.driving,
    //   ),
    //   googleApiKey: Constants.googleMapAPIKey,
    // )
    //     .then((value) {
    //   if (value.points.isNotEmpty) {
    //     _polylineCoordinates = [];
    //     for (var element in value.points) {
    //       _polylineCoordinates.add(LatLng(element.latitude, element.longitude));
    //     }
    //     _polyLines.add(
    //       Polyline(
    //         width: 5,
    //         polylineId: const PolylineId('poly'),
    //         color: const Color.fromARGB(255, 40, 122, 198),
    //         points: _polylineCoordinates,
    //       ),
    //     );
    //     setState(() {});
    //   }
    // }).onError((error, stackTrace) {
    //   toast(error.toString());
    // });
  }

  _init() {
    _getServicesList();
  }

  _onPullToRefresh() {
    _init();
  }

  @override
  void initState() {
    super.initState();

    _init();
  }

  @override
  dispose() {
    hideAppActivity();
    super.dispose();

    Globals.pointAnnotationManager = null;
    Globals.polylineAnnotationManager = null;
  }

  @override
  Widget build(BuildContext context) {
    SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
      statusBarBrightness: Brightness.light,
      statusBarIconBrightness: Brightness.dark,
    ));

    return Scaffold(
      appBar: const RoooAppbar(title: "Select Service"),
      bottomNavigationBar: _showBottomButton && _selectedServiceId != -1
          ? Container(
              padding: const EdgeInsets.all(Layout.scaffoldBodyPadding),
              decoration: BoxDecoration(
                color: Theme.of(context).scaffoldBackgroundColor,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 10,
                    offset: const Offset(0, -5),
                  ),
                ],
              ),
              child: AppButtonWidget(
                width: double.infinity,
                text: "Proceed",
                onTap: () {
                  var service = _selectServiceList.firstWhere(
                      (element) => element.id == _selectedServiceId);
                  HelperMethods.pushScreen(
                    context: context,
                    screen: ApplyCouponScreen(
                      is_peak: service.isPeakArea,
                      isPoolingRide: service.isPooling,
                      personCount: widget.personCount,
                      scheduledTime: widget.scheduledTime,
                      sourceAddress: widget.sourceAddress,
                      destinationAddress: widget.destinationAddress,
                      sourceLocation: widget.pickupLocation,
                      destinationLocation: widget.destinlationLocation,
                      selectServiceModel: service,
                      isBusinessRide: widget.isBusinessRide,
                    ),
                  );
                },
              ),
            )
          : const SizedBox(),
      body: Stack(
        children: [
          const MapBoxWrapperWidget(
            isDashboardScreen: false,
            key: ValueKey("map"),
            zoom: 8,
          ),
          Positioned(
            top: 20,
            right: 0,
            child: CurrentLocationButton(animate_map: () async {
              Globals.mapbox!.flyTo(
                  mp.CameraOptions(
                      zoom: 16,
                      center: mp.Point(coordinates: widget.pickupLocation)),
                  mp.MapAnimationOptions());
              await Future.delayed(const Duration(seconds: 2));
            }),
          ),
          SlidingUpPanel(
            color: Theme.of(context).scaffoldBackgroundColor,
            onPanelSlide: (position) {
              if (position >= 0.4) {
                _showBottomButton = true;
              } else {
                _showBottomButton = false;
              }
              setState(() {});
            },
            defaultPanelState: PanelState.CLOSED,
            minHeight: 210,
            maxHeight: MediaQuery.sizeOf(context).height - 200,
            panelBuilder: (sc) {
              var selectedService = _selectServiceList
                  .where((element) => element.id == _selectedServiceId)
                  .toList();

              return _selectServiceList.isEmpty
                  ? const SizedBox()
                  : SingleChildScrollView(
                      controller: sc,
                      child: Padding(
                        padding: const EdgeInsets.fromLTRB(16, 24, 16, 16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Center(
                              child: Container(
                                alignment: Alignment.center,
                                margin: const EdgeInsets.only(bottom: 16),
                                height: 5,
                                width: 70,
                                decoration: BoxDecoration(
                                  color: checkIfDarkModeIsOn(context)
                                      ? Colors.white
                                      : Colors.black,
                                  // borderRadius: BorderRadius.circular(defaultRadius),
                                ),
                              ),
                            ),
                            if (selectedService.isNotEmpty)
                              Container(
                                padding: const EdgeInsets.all(16),
                                decoration: BoxDecoration(
                                  color: Theme.of(context).cardColor,
                                  borderRadius: BorderRadius.circular(16),
                                  border: Border.all(
                                    color: Theme.of(context).primaryColor,
                                    width: 2,
                                  ),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.black.withOpacity(0.05),
                                      blurRadius: 10,
                                      offset: const Offset(0, 2),
                                    ),
                                  ],
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      "Selected Service",
                                      style: Theme.of(context)
                                          .textTheme
                                          .titleMedium
                                          ?.copyWith(
                                            fontWeight: FontWeight.bold,
                                          ),
                                    ),
                                    const SizedBox(height: 12),
                                    Row(
                                      children: [
                                        Container(
                                          width: 60,
                                          height: 60,
                                          decoration: BoxDecoration(
                                            borderRadius:
                                                BorderRadius.circular(12),
                                            image: DecorationImage(
                                              image: NetworkImage(
                                                  selectedService[0]
                                                      .serviceImage),
                                              fit: BoxFit.cover,
                                            ),
                                          ),
                                        ),
                                        const SizedBox(width: 12),
                                        Expanded(
                                          child: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Row(
                                                children: [
                                                  Expanded(
                                                    child: Text(
                                                      selectedService[0].name,
                                                      style: Theme.of(context)
                                                          .textTheme
                                                          .titleMedium
                                                          ?.copyWith(
                                                            fontWeight:
                                                                FontWeight.bold,
                                                          ),
                                                    ),
                                                  ),
                                                  if (selectedService[0]
                                                      .isPeakArea)
                                                    Container(
                                                      padding: const EdgeInsets
                                                          .symmetric(
                                                        horizontal: 8,
                                                        vertical: 4,
                                                      ),
                                                      decoration: BoxDecoration(
                                                        color: Colors.red
                                                            .withOpacity(0.1),
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(8),
                                                      ),
                                                      child: Text(
                                                        "Peak",
                                                        style: TextStyle(
                                                          color: Colors.red,
                                                          fontSize: 12,
                                                          fontWeight:
                                                              FontWeight.bold,
                                                        ),
                                                      ),
                                                    ),
                                                ],
                                              ),
                                              const SizedBox(height: 4),
                                              Row(
                                                children: [
                                                  Icon(
                                                    Icons.access_time,
                                                    size: 16,
                                                    color: Theme.of(context)
                                                        .iconTheme
                                                        .color
                                                        ?.withOpacity(0.7),
                                                  ),
                                                  const SizedBox(width: 4),
                                                  Text(
                                                    "${selectedService[0].duration.toStringAsFixed(0)} mins",
                                                    style: Theme.of(context)
                                                        .textTheme
                                                        .bodyMedium
                                                        ?.copyWith(
                                                          color:
                                                              Theme.of(context)
                                                                  .textTheme
                                                                  .bodyMedium
                                                                  ?.color
                                                                  ?.withOpacity(
                                                                      0.7),
                                                        ),
                                                  ),
                                                  const SizedBox(width: 16),
                                                  Icon(
                                                    Icons.people,
                                                    size: 16,
                                                    color: Theme.of(context)
                                                        .iconTheme
                                                        .color
                                                        ?.withOpacity(0.7),
                                                  ),
                                                  const SizedBox(width: 4),
                                                  Text(
                                                    "${selectedService[0].capacity} seats",
                                                    style: Theme.of(context)
                                                        .textTheme
                                                        .bodyMedium
                                                        ?.copyWith(
                                                          color:
                                                              Theme.of(context)
                                                                  .textTheme
                                                                  .bodyMedium
                                                                  ?.color
                                                                  ?.withOpacity(
                                                                      0.7),
                                                        ),
                                                  ),
                                                ],
                                              ),
                                            ],
                                          ),
                                        ),
                                        Text(
                                          "${Constants.currencySymbol}${selectedService[0].totalAmount.toStringAsFixed(2)}",
                                          style: Theme.of(context)
                                              .textTheme
                                              .titleMedium
                                              ?.copyWith(
                                                fontWeight: FontWeight.bold,
                                                color: Theme.of(context)
                                                    .primaryColor,
                                              ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                            if (selectedService.isNotEmpty)
                              const SizedBox(height: 20),
                            if (isThereAnyPeakAreaService)
                              Container(
                                padding: const EdgeInsets.all(12),
                                decoration: BoxDecoration(
                                  color: Colors.red.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(12),
                                  border: Border.all(
                                    color: Colors.red.withOpacity(0.3),
                                  ),
                                ),
                                child: Row(
                                  children: [
                                    const Icon(
                                      Icons.warning_rounded,
                                      size: 24,
                                      color: Colors.red,
                                    ),
                                    const SizedBox(width: 12),
                                    Expanded(
                                      child: Text(
                                        "Some services are currently in peak hours, resulting in elevated pricing",
                                        style: TextStyle(
                                          color: Theme.of(context).brightness ==
                                                  Brightness.dark
                                              ? Colors.white
                                              : Colors.red.shade900,
                                          fontSize: 14,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            const SizedBox(height: 20),
                            Padding(
                              padding: const EdgeInsets.only(bottom: 16),
                              child: Text(
                                "Available Services",
                                style: Theme.of(context)
                                    .textTheme
                                    .titleLarge
                                    ?.copyWith(
                                      fontWeight: FontWeight.bold,
                                    ),
                              ),
                            ),
                            ListView.separated(
                              physics: const NeverScrollableScrollPhysics(),
                              shrinkWrap: true,
                              itemBuilder: (context, index) {
                                SelectServiceModel data =
                                    _selectServiceList[index];
                                bool isSelected = data.id == _selectedServiceId;

                                return AnimatedContainer(
                                  duration: const Duration(milliseconds: 200),
                                  decoration: BoxDecoration(
                                    color: isSelected
                                        ? Theme.of(context)
                                            .primaryColor
                                            .withOpacity(0.1)
                                        : Theme.of(context).cardColor,
                                    borderRadius: BorderRadius.circular(16),
                                    border: Border.all(
                                      color: isSelected
                                          ? Theme.of(context).primaryColor
                                          : Theme.of(context).dividerColor,
                                      width: isSelected ? 2 : 1,
                                    ),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black.withOpacity(0.05),
                                        blurRadius: 10,
                                        offset: const Offset(0, 2),
                                      ),
                                    ],
                                  ),
                                  child: InkWell(
                                    onTap: () {
                                      _filterMarkers(
                                          data: _selectServiceList,
                                          selectedServiceId:
                                              _selectedServiceId);
                                      setState(() {
                                        _selectedServiceId = data.id;
                                      });
                                    },
                                    borderRadius: BorderRadius.circular(16),
                                    child: Padding(
                                      padding: const EdgeInsets.all(16),
                                      child: Row(
                                        children: [
                                          Container(
                                            width: 80,
                                            height: 80,
                                            decoration: BoxDecoration(
                                              borderRadius:
                                                  BorderRadius.circular(12),
                                              image: DecorationImage(
                                                image: NetworkImage(
                                                    data.serviceImage),
                                                fit: BoxFit.cover,
                                              ),
                                            ),
                                          ),
                                          const SizedBox(width: 16),
                                          Expanded(
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Row(
                                                  children: [
                                                    Expanded(
                                                      child: Text(
                                                        data.name,
                                                        style: Theme.of(context)
                                                            .textTheme
                                                            .titleMedium
                                                            ?.copyWith(
                                                              fontWeight:
                                                                  FontWeight
                                                                      .bold,
                                                            ),
                                                      ),
                                                    ),
                                                    if (data.isPeakArea)
                                                      Container(
                                                        padding:
                                                            const EdgeInsets
                                                                .symmetric(
                                                          horizontal: 8,
                                                          vertical: 4,
                                                        ),
                                                        decoration:
                                                            BoxDecoration(
                                                          color: Colors.red
                                                              .withOpacity(0.1),
                                                          borderRadius:
                                                              BorderRadius
                                                                  .circular(8),
                                                        ),
                                                        child: Text(
                                                          "Peak",
                                                          style: TextStyle(
                                                            color: Colors.red,
                                                            fontSize: 12,
                                                            fontWeight:
                                                                FontWeight.bold,
                                                          ),
                                                        ),
                                                      ),
                                                  ],
                                                ),
                                                const SizedBox(height: 8),
                                                Row(
                                                  children: [
                                                    Icon(
                                                      Icons.access_time,
                                                      size: 16,
                                                      color: Theme.of(context)
                                                          .iconTheme
                                                          .color
                                                          ?.withOpacity(0.7),
                                                    ),
                                                    const SizedBox(width: 4),
                                                    Text(
                                                      "${data.duration.toStringAsFixed(0)} mins",
                                                      style: Theme.of(context)
                                                          .textTheme
                                                          .bodyMedium
                                                          ?.copyWith(
                                                            color: Theme.of(
                                                                    context)
                                                                .textTheme
                                                                .bodyMedium
                                                                ?.color
                                                                ?.withOpacity(
                                                                    0.7),
                                                          ),
                                                    ),
                                                    const SizedBox(width: 16),
                                                    Icon(
                                                      Icons.people,
                                                      size: 16,
                                                      color: Theme.of(context)
                                                          .iconTheme
                                                          .color
                                                          ?.withOpacity(0.7),
                                                    ),
                                                    const SizedBox(width: 4),
                                                    Text(
                                                      "${data.capacity} seats",
                                                      style: Theme.of(context)
                                                          .textTheme
                                                          .bodyMedium
                                                          ?.copyWith(
                                                            color: Theme.of(
                                                                    context)
                                                                .textTheme
                                                                .bodyMedium
                                                                ?.color
                                                                ?.withOpacity(
                                                                    0.7),
                                                          ),
                                                    ),
                                                  ],
                                                ),
                                                const SizedBox(height: 8),
                                                Row(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment
                                                          .spaceBetween,
                                                  children: [
                                                    Text(
                                                      "${Constants.currencySymbol}${data.totalAmount.toStringAsFixed(2)}",
                                                      style: Theme.of(context)
                                                          .textTheme
                                                          .titleMedium
                                                          ?.copyWith(
                                                            fontWeight:
                                                                FontWeight.bold,
                                                            color: Theme.of(
                                                                    context)
                                                                .primaryColor,
                                                          ),
                                                    ),
                                                    if (isSelected)
                                                      Container(
                                                        padding:
                                                            const EdgeInsets
                                                                .all(8),
                                                        decoration:
                                                            BoxDecoration(
                                                          color:
                                                              Theme.of(context)
                                                                  .primaryColor,
                                                          shape:
                                                              BoxShape.circle,
                                                        ),
                                                        child: const Icon(
                                                          Icons.check,
                                                          color: Colors.white,
                                                          size: 16,
                                                        ),
                                                      ),
                                                  ],
                                                ),
                                              ],
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                );
                              },
                              separatorBuilder: (context, index) =>
                                  const SizedBox(height: 12),
                              itemCount: _selectServiceList.length,
                            ),
                            const SizedBox(height: 20),
                          ],
                        ),
                      ),
                    );
            },
          ),
          const ActivityIndicator(),
        ],
      ),
    );
  }
}
