import 'package:rider/features/ride_flow/screen/manage_ride_stops_logic.dart';

class PushNotificationTypes {
  int id;
  String name;
  bool isActive;
  PushNotificationTypes(
      {required this.id, required this.name, required this.isActive});

  factory PushNotificationTypes.fromMap(Map<String, dynamic> json) {
    return PushNotificationTypes(
      id: json['id'],
      name: json['name'],
      isActive: json['is_active'],
    );
  }
}

class PushNotificationTypesResponse
    extends ApiBaseResponse<List<PushNotificationTypes>> {
  PushNotificationTypesResponse(
      {required super.status, required super.message, required super.data});

  factory PushNotificationTypesResponse.fromMap(Map<String, dynamic> json) {
    return PushNotificationTypesResponse(
      status: json['status'],
      message: json['message'],
      data: json['data'] == null
          ? null
          : (json['data'] as List<dynamic>)
              .map((e) => PushNotificationTypes.fromMap(e))
              .toList(),
    );
  }
}
