import 'package:rider/app_exports.dart';

class SelectLocationRepository {
  Future<GoogleMapSearchModel> searchAddressRequest(
      {required String searchText}) async {
    return GoogleMapSearchModel.fromJson(await handleResponse(
        await buildHttpResponse(
            "https://maps.googleapis.com/maps/api/place/autocomplete/json?input=$searchText&key=${Constants.googleMapAPIKey}&components=country:${Globals.riderRegionCode}",
            method: HttpMethod.get)));
  }
}
