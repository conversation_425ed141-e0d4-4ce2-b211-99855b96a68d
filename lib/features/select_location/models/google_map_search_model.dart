class GoogleMapSearchModel {
  List<Prediction>? predictions;
  String? status;

  GoogleMapSearchModel({this.predictions, this.status});

  factory GoogleMapSearchModel.fromJson(Map<String, dynamic> json) {
    return GoogleMapSearchModel(
      predictions: json['predictions'] != null
          ? (json['predictions'] as List)
              .map((i) => Prediction.fromJson(i))
              .toList()
          : null,
      status: json['status'],
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    if (predictions != null) {
      data['predictions'] = predictions!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Prediction {
  String? description;
  String? placeId;
  String? reference;
  List<String>? types;

  Prediction({this.description, this.placeId, this.reference, this.types});

  factory Prediction.fromJson(Map<String, dynamic> json) {
    return Prediction(
      description: json['description'],
      placeId: json['place_id'],
      reference: json['reference'],
      types: json['types'] != null ? List<String>.from(json['types']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['description'] = description;
    data['place_id'] = placeId;
    data['reference'] = reference;
    if (types != null) {
      data['types'] = types;
    }
    return data;
  }
}

// Main Model Class
class MapBoxSearchResponse {
  List<MapBoxSuggestion> suggestions;

  MapBoxSearchResponse({
    required this.suggestions,
  });

  factory MapBoxSearchResponse.fromJson(Map<String, dynamic> json) {
    return MapBoxSearchResponse(
      suggestions: List<MapBoxSuggestion>.from(
          json['suggestions'].map((x) => MapBoxSuggestion.fromJson(x))),
    );
  }
}

// Feature Class
class MapBoxSuggestion {
  String mapBoxId;
  String name;
  String fullAddress;

  MapBoxSuggestion({
    required this.mapBoxId,
    required this.name,
    required this.fullAddress,
  });

  factory MapBoxSuggestion.fromJson(Map<String, dynamic> json) {
    return MapBoxSuggestion(
      mapBoxId: json['mapbox_id'],
      name: json['name'],
      fullAddress: json['full_address'] ?? "",
    );
  }
}

class MapBoxLocation {
  String name;
  String fullAddress;
  double latitude;
  double longitude;

  MapBoxLocation({
    required this.name,
    required this.fullAddress,
    required this.latitude,
    required this.longitude,
  });

  factory MapBoxLocation.fromJson(Map<String, dynamic> json) {
    return MapBoxLocation(
      name: json['name'],
      fullAddress: json['full_address'] ?? json['name'],
      latitude: json['coordinates']?['latitude'] ?? json['latitude'],
      longitude: json['coordinates']?['longitude'] ?? json['longitude'],
    );
  }
}

// Geometry Class
class Geometry {
  String type;
  List<double> coordinates;

  Geometry({
    required this.type,
    required this.coordinates,
  });

  factory Geometry.fromJson(Map<String, dynamic> json) {
    return Geometry(
      type: json['type'],
      coordinates:
          List<double>.from(json['coordinates'].map((x) => x.toDouble())),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'type': type,
      'coordinates': List<dynamic>.from(coordinates.map((x) => x)),
    };
  }
}

// Context Class
class Context {
  String id;
  String? mapboxId;
  String? wikidata;
  String? shortCode;
  String text;

  Context({
    required this.id,
    this.mapboxId,
    this.wikidata,
    this.shortCode,
    required this.text,
  });

  factory Context.fromJson(Map<String, dynamic> json) {
    return Context(
      id: json['id'],
      mapboxId: json['mapbox_id'] == null ? null : json['mapbox_id'],
      wikidata: json['wikidata'],
      shortCode: json['short_code'],
      text: json['text'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'mapbox_id': mapboxId,
      'wikidata': wikidata,
      'short_code': shortCode,
      'text': text,
    };
  }
}





class LastCompletedRidesResponse
{
  bool status;
  String message;
  List<MapBoxLocation>? data;

  LastCompletedRidesResponse({
    required this.status,
    required this.message,
    required this.data,
  });

  factory LastCompletedRidesResponse.fromMap(Map<String, dynamic> map) {
    return LastCompletedRidesResponse(
      status: map['status'] as bool,
      message: map['message'] as String,
      data: map['data'] != null
          ? List<MapBoxLocation>.from(
              (map['data'] as List).map((x) => MapBoxLocation.fromJson(x)))
          : null,
    );
  }
}
