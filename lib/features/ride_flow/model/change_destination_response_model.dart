import 'package:rider/global/models/response_model.dart';
import 'package:rider/global/models/ride_model.dart';

class ChangeDestinationResponseModel extends ResponseModel<OnRideRequest> {
  ChangeDestinationResponseModel({
    required super.status,
    required String super.message,
    required super.data,
  });

  // Factory constructor for creating a new UserResponseModel instance from a map.
  factory ChangeDestinationResponseModel.fromJson(Map<String, dynamic> json) {
    return ChangeDestinationResponseModel(
      status: json['status'],
      message: json['message'],
      data: json['data'] != null ? OnRideRequest.fromJson(json['data']) : null,
    );
  }
}
