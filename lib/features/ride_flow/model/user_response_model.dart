import 'package:rider/global/models/response_model.dart';
import 'package:rider/model/login_response.dart';

class UserResponseModel extends ResponseModel<UserModel> {
  UserResponseModel({
    required super.status,
    required String super.message,
    required super.data,
  });

  // Factory constructor for creating a new UserResponseModel instance from a map.
  factory UserResponseModel.fromJson(Map<String, dynamic> json) {
    return UserResponseModel(
      status: json['status'],
      message: json['message'],
      data: json['data'] != null ? UserModel.fromJson(json['data']) : null,
    );
  }

  // Method to convert a UserResponseModel instance to a map.
  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'message': message,
      'data': data?.toJson(),
    };
  }
}
