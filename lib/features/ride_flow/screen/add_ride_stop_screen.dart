import 'package:geocoding/geocoding.dart';
import 'package:mapbox_maps_flutter/mapbox_maps_flutter.dart' as mb;
import 'package:rider/app_exports.dart';
import 'package:rider/features/payment/screens/select_payment_for_additional_charges_screen.dart';
import 'package:rider/features/ride_flow/screen/manage_ride_stops_logic.dart';
import 'package:location/location.dart';
import 'package:location/location.dart' as nlp;
import 'package:rider/features/select_location/models/mapbox_location_model.dart';
import 'package:uuid/uuid.dart';

class AddRideStopScreen extends StatefulWidget {
  final int rideId;
  const AddRideStopScreen({super.key, required this.rideId});

  @override
  State<AddRideStopScreen> createState() => _AddRideStopScreenState();
}

class _AddRideStopScreenState extends State<AddRideStopScreen> {
  Timer? _debounceTimer;

  List<MapBoxSuggestion> _addressList = [];
  bool _isSearching = false;

  final TextEditingController _searchController = TextEditingController();
  final FocusNode _focusNode = FocusNode();

  final RideStop _rideStop = RideStop(
    id: -1,
    latitude: 0.0,
    longitude: 0.0,
    title: "",
    rideId: null,
    currentLatitude: 0,
    currentLongitude: 0,
    currentAddress: "",
    isThePriceIncreasedAfterThisStop: true,
    debitWallet: null,
    paymentCardId: null,
    isArrived: null,
    payablePrice: null,
  );

  final String _sessionToken = const Uuid().v4();

  late mb.Position _currentLocation;

  @override
  void initState() {
    super.initState();

    _focusNode.addListener(() {
      if (_focusNode.hasFocus) {
        _searchController.selection = TextSelection(
          baseOffset: 0,
          extentOffset: _searchController.text.length,
        );
      }
    });

    _fillPickUpLocation();
    _rideStop.rideId = widget.rideId;
  }

  @override
  void dispose() {
    super.dispose();
    hideAppActivity();
  }

  Future<void> _fillPickUpLocation() async {
    try {
      if(Globals.currentLocation != null){
        _currentLocation = Globals.currentLocation!;
        return;
      }
      showAppActivity();
      nlp.Location location = nlp.Location();

      await location.getLocation().then((locationData) async {
        _currentLocation =
            mb.Position(locationData.longitude!, locationData.latitude!);
        setState(() {
          hideAppActivity();
        });
      });
    } catch (e) {
      toast(Constants.errorMSG);
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        hideKeyboard();
      },
      child: Scaffold(
        appBar: const RoooAppbar(title: "Add ride stop"),
        body: Stack(
          children: [
            Padding(
              padding: const EdgeInsets.all(
                Layout.scaffoldBodyPadding,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    "Search for a place",
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(
                    height: 10,
                  ),
                  TextFormField(
                    controller: _searchController,
                    focusNode: _focusNode,
                    decoration: InputDecoration(
                      border: OutlineInputBorder(
                        borderSide: BorderSide(
                          color: Theme.of(context).primaryColor,
                        ),
                      ),
                      suffixIcon: _isSearching
                          ? const SizedBox(
                              width: 20,
                              child: RooLoader(
                                size: 20,
                              ),
                            )
                          : const Icon(
                              Icons.search,
                            ),
                    ),
                    onChanged: (value) {
                      if (value.length >= 3) {
                        _onSearching(context, value);
                      } else {
                        setState(() {
                          _addressList = [];
                        });
                      }
                    },
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      TextButton(
                          onPressed: _pickFromMap,
                          child: const Row(
                            children: [
                              Text("From map"),
                              Icon(Icons.location_on)
                            ],
                          )),
                      TextButton(
                          onPressed: _pickFromSavedPlaces,
                          child: const Row(
                            children: [Text("Favorites"), Icon(Icons.flag)],
                          )),
                    ],
                  ),
                  Expanded(
                    child: Visibility(
                      visible: _addressList.isNotEmpty,
                      child: Card(
                        child: ListView.builder(
                          shrinkWrap: true,
                          itemCount: _addressList.length,
                          itemBuilder: (context, index) {
                            MapBoxSuggestion data = _addressList[index];
                            return ListTile(
                              onTap: () async {
                                _onSelectPickup(data: data);
                              },
                              trailing: const Icon(Icons.my_location_outlined),
                              title: Text(data.name.toString()),
                              subtitle: Text(data.fullAddress.toString()),
                            );
                          },
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(
                    height: 20,
                  ),
                  Center(
                    child: AppButtonWidget(
                      fullWidth: false,
                      text: "Save",
                      onTap: _onRideStopSave,
                    ),
                  ),
                ],
              ),
            ),
            const ActivityIndicator(),
          ],
        ),
      ),
    );
  }

  Future<void> _onRideStopSave() async {
    if (_rideStop.latitude == 0.0) {
      toast("Please enter a valid location");
      return;
    }
    hideKeyboard();
    showAppActivity();

    nlp.Location location = nlp.Location();

    LocationData locationData = await location.getLocation();
    if (locationData.latitude != null) {
      _rideStop.currentLatitude = locationData.latitude!;
      _rideStop.currentLongitude = locationData.longitude!;

      List<Placemark> places = await placemarkFromCoordinates(
          locationData.latitude!, locationData.longitude!);

      if (places.isNotEmpty) {
        Placemark place = places[0];
        _rideStop.currentAddress =
            "${place.name ?? place.subThoroughfare}, ${place.subLocality}, ${place.locality}, ${place.administrativeArea} ${place.postalCode}, ${place.country}";
      }
    }

    var result = await ManageRideStopsLogic.add(_rideStop);
    if (result == null) {
      toast(Globals.language.errorMsg);
      return;
    } else if (result.status == false) {
      if ((result.data?.isThePriceIncreasedAfterThisStop ?? false) == true) {
        if (result.data != null) {
          _rideStop.payablePrice = result.data!.payablePrice;
        }
        _askForPriceIncrease(result.message);
      } else {
        toast(result.message);
      }
    } else if (result.status) {
      closeScreen(result.data!);
      toast("Ride stop added");
    }
    hideAppActivity();
  }

  void closeScreen(RideStop stop) {
    Navigator.of(context).pop(stop);
  }

  Future<void> _askForPriceIncrease(String msg) async {
    showAppDialog(
      barrierDismissible: false,
      onAccept: () {
        if ((_rideStop.payablePrice ?? 0) > 0) {
          launchScreen(
            SelectPaymentForAdditionalCharges(
              payableAmount: _rideStop.payablePrice!,
              rideId: widget.rideId,
              chargesType: AdditionalChargesType.stopsChange,
            ),
            isNewTask: true,
          );
        } else {
          Navigator.of(context).pop();
          _rideStop.isThePriceIncreasedAfterThisStop = false;
          _onRideStopSave();
        }
      },
      dialogType: AppDialogType.confirmation,
      title: msg,
    );
  }

  Future<void> _onSearching(BuildContext context, String searchText) async {
    setState(() {
      _isSearching = true;
    });
    // Cancel the previous debounce timer if it exists to prevent extra calls
    if (_debounceTimer != null && _debounceTimer!.isActive) {
      _debounceTimer!.cancel();
    }

    // Start a new debounce timer
    _debounceTimer = Timer(const Duration(milliseconds: 500), () async {
      _getSearchResults(searchText: searchText);
      //Make API call or do something
    });
  }

  _getSearchResults({required String searchText}) {
    searchWithMapBox(
      search: searchText,
      sessionToken: _sessionToken,
      proximity: _currentLocation,
    ).then((value) {
      if (value == null) {
        toast(Globals.language.errorMsg);
        return;
      }

      if (_searchController.text.length >= 3) {
        _addressList = [];
        for (var element in value.suggestions) {
          if (element.fullAddress.isNotEmpty) {
            _addressList.add(element);
          }
        }
        setState(() {
          _isSearching = false;
        });
      }
    });
  }

  _onSelectPickup({required MapBoxSuggestion data}) async {
    showAppActivity();
    await getMapBoxLocation(
            mapBoxId: data.mapBoxId, sessionToken: _sessionToken)
        .then((value) {
      if (value == null) {
        toast(Globals.language.errorMsg);
        return;
      }
      _rideStop.latitude = value.latitude;
      _rideStop.longitude = value.longitude;

      _rideStop.title = data.name.toString();
      _searchController.text = _rideStop.title;
      setState(() {
        _addressList = [];
      });
    });
    hideKeyboard();
    hideAppActivity();
  }

  Future<void> _pickFromMap() async {
    hideKeyboard();
    setState(() {
      _searchController.text = "";
    });
    MapBoxLocationModel? selectedPlace =
        await launchScreen(const MapPickupScreen());
    if (selectedPlace != null) {
      _rideStop.latitude = selectedPlace.point.latitude!;
      _rideStop.longitude = selectedPlace.point!.longitude;

      _rideStop.title = selectedPlace.address.toString();
      setState(() {
        _searchController.text = _rideStop.title;
      });
    }
  }

  Future<void> _pickFromSavedPlaces() async {
    hideKeyboard();
    setState(() {
      _searchController.text = "";
    });
    SavedPlace? savedPlace =
        await launchScreen(const SavedPlacesScreen(), context: context);
    if (savedPlace != null) {
      _rideStop.latitude = double.parse(savedPlace.latitude);
      _rideStop.longitude = double.parse(savedPlace.longitude);

      _rideStop.title = savedPlace.title;
      setState(() {
        _searchController.text = _rideStop.title;
      });
    }
  }
}
