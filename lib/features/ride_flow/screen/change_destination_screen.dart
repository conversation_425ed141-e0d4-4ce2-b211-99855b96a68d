import 'package:geocoding/geocoding.dart';
import 'package:rider/app_exports.dart';
import 'package:rider/features/payment/screens/select_payment_for_additional_charges_screen.dart';
import 'package:rider/features/ride_flow/screen/manage_ride_stops_logic.dart';
import 'package:location/location.dart';
import 'package:location/location.dart' as nlp;
import 'package:rider/features/select_location/models/mapbox_location_model.dart';
import 'package:uuid/uuid.dart';
import 'package:mapbox_maps_flutter/mapbox_maps_flutter.dart' as mb;

class ChangeDestinationScreen extends StatefulWidget {
  final int rideId;
  const ChangeDestinationScreen({super.key, required this.rideId});

  @override
  State<ChangeDestinationScreen> createState() =>
      _ChangeDestinationScreenState();
}

class _ChangeDestinationScreenState extends State<ChangeDestinationScreen> {
  Timer? _debounceTimer;
  List<MapBoxSuggestion> _addressList = [];
  bool _isSearching = false;
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  final String _sessionToken = const Uuid().v4();
  late mb.Position _currentLocation;

  RideDestination _rideDestination = RideDestination(
    id: -1,
    latitude: 0.0,
    longitude: 0.0,
    title: "",
    rideId: null,
    currentLatitude: 0,
    currentLongitude: 0,
    currentAddress: "",
    isThePriceIncreasedAfterThisStop: true,
  );

  @override
  void initState() {
    super.initState();
    _focusNode.addListener(() {
      if (_focusNode.hasFocus) {
        _searchController.selection = TextSelection(
          baseOffset: 0,
          extentOffset: _searchController.text.length,
        );
      }
    });
    _fillPickUpLocation();
    _rideDestination.rideId = widget.rideId;
  }

  @override
  void dispose() {
    hideAppActivity();
    super.dispose();
  }

  Future<void> _fillPickUpLocation() async {
    try {
      if (Globals.currentLocation != null) {
        _currentLocation = Globals.currentLocation!;
        return;
      }
      showAppActivity();
      nlp.Location location = nlp.Location();

      await location.getLocation().then((locationData) async {
        _currentLocation =
            mb.Position(locationData.longitude!, locationData.latitude!);
        setState(() {
          hideAppActivity();
        });
      });
    } catch (e) {
      toast(Constants.errorMSG);
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        hideKeyboard();
      },
      child: Scaffold(
        appBar: const RoooAppbar(title: "Change destination"),
        body: Stack(
          children: [
            Padding(
              padding: const EdgeInsets.all(Layout.scaffoldBodyPadding),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Destination Location Section
                  Container(
                    decoration: BoxDecoration(
                      color: Globals.isDarkModeOn
                          ? Colors.grey[900]
                          : Colors.grey[100],
                      borderRadius: BorderRadius.circular(12),
                    ),
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "New Destination",
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: Globals.isDarkModeOn
                                ? Colors.white
                                : Colors.black87,
                          ),
                        ),
                        const SizedBox(height: 12),
                        TextField(
                          controller: _searchController,
                          focusNode: _focusNode,
                          onChanged: (value) {
                            if (value.length >= 3) {
                              _onSearching(context, value);
                            } else {
                              setState(() {
                                _isSearching = false;
                                _addressList = [];
                              });
                            }
                          },
                          decoration: InputDecoration(
                            hintText: "Enter new destination",
                            prefixIcon: Icon(
                              Icons.location_on,
                              color: Theme.of(context).primaryColor,
                            ),
                            suffixIcon: _isSearching
                                ? const SizedBox(
                                    width: 20,
                                    child: RooLoader(size: 20),
                                  )
                                : const Icon(Icons.search),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                              borderSide: BorderSide.none,
                            ),
                            filled: true,
                            fillColor: Globals.isDarkModeOn
                                ? Colors.grey[800]
                                : Colors.white,
                            contentPadding: const EdgeInsets.symmetric(
                                horizontal: 16, vertical: 12),
                          ),
                        ),
                        const SizedBox(height: 8),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            TextButton.icon(
                              onPressed: _pickFromMap,
                              icon: Icon(
                                Icons.map,
                                color: Theme.of(context).primaryColor,
                              ),
                              label: Text(
                                "From map",
                                style: TextStyle(
                                  color: Theme.of(context).primaryColor,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              style: TextButton.styleFrom(
                                backgroundColor: Globals.isDarkModeOn
                                    ? Colors.grey[800]
                                    : Colors.white,
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 16, vertical: 12),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                            ),
                            TextButton.icon(
                              onPressed: _pickFromSavedPlaces,
                              icon: Icon(
                                Icons.flag,
                                color: Theme.of(context).primaryColor,
                              ),
                              label: Text(
                                "Favorites",
                                style: TextStyle(
                                  color: Theme.of(context).primaryColor,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              style: TextButton.styleFrom(
                                backgroundColor: Globals.isDarkModeOn
                                    ? Colors.grey[800]
                                    : Colors.white,
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 16, vertical: 12),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Location Suggestions List
                  if (_addressList.isNotEmpty)
                    Expanded(
                      child: Container(
                        decoration: BoxDecoration(
                          color: Globals.isDarkModeOn
                              ? Colors.grey[900]
                              : Colors.white,
                          borderRadius: BorderRadius.circular(12),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.1),
                              blurRadius: 8,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: ListView.builder(
                          shrinkWrap: true,
                          itemCount: _addressList.length,
                          itemBuilder: (context, index) {
                            MapBoxSuggestion data = _addressList[index];
                            return ListTile(
                              onTap: () async {
                                _onSelectPickup(data: data);
                              },
                              leading: Icon(
                                Icons.location_on_outlined,
                                color: Theme.of(context).primaryColor,
                              ),
                              title: Text(
                                data.name,
                                style: TextStyle(
                                  color: Globals.isDarkModeOn
                                      ? Colors.white
                                      : Colors.black87,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              subtitle: Text(
                                data.fullAddress,
                                style: TextStyle(
                                  color: Globals.isDarkModeOn
                                      ? Colors.white70
                                      : Colors.black54,
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                    ),
                ],
              ),
            ),
            const ActivityIndicator(),
          ],
        ),
        bottomNavigationBar: BottomButton(
          text: "Save",
          onPressed: _onDestinationSave,
        ),
      ),
    );
  }

  Future<void> _onDestinationSave() async {
    if (_rideDestination.latitude == 0.0) {
      toast("Please enter a valid location");
      return;
    }
    hideKeyboard();
    showAppActivity();

    nlp.Location location = nlp.Location();
    LocationData locationData = await location.getLocation();
    if (locationData.latitude != null) {
      _rideDestination.currentLatitude = locationData.latitude!;
      _rideDestination.currentLongitude = locationData.longitude!;
      List<Placemark> places = await placemarkFromCoordinates(
          locationData.latitude!, locationData.longitude!);

      if (places.isNotEmpty) {
        Placemark place = places[0];
        _rideDestination.currentAddress =
            "${place.name ?? place.subThoroughfare}, ${place.subLocality}, ${place.locality}, ${place.administrativeArea} ${place.postalCode}, ${place.country}";
      }
    }

    var result = await addRideDestination(request: _rideDestination);
    if (result.status == false) {
      if ((result.data?.isThePriceIncreasedAfterThisStop ?? false) == true) {
        if (result.data != null) {
          _rideDestination.payablePrice = result.data!.payablePrice;
        }
        _askForPriceIncrease(result.message);
      } else {
        toast(result.message);
      }
    } else {
      closeScreen();
      toast("Destination changed");
    }
    hideAppActivity();
  }

  void closeScreen() {
    Navigator.of(context).pop(true);
  }

  Future<void> _askForPriceIncrease(String msg) async {
    showAppDialog(
      barrierDismissible: false,
      onAccept: () {
        if ((_rideDestination.payablePrice ?? 0) > 0) {
          launchScreen(
            SelectPaymentForAdditionalCharges(
              payableAmount: _rideDestination.payablePrice!,
              rideId: widget.rideId,
              chargesType: AdditionalChargesType.destinationChange,
            ),
            isNewTask: true,
          );
        } else {
          Navigator.of(context).pop();
          _rideDestination.isThePriceIncreasedAfterThisStop = false;
          _onDestinationSave();
        }
      },
      dialogType: AppDialogType.confirmation,
      title: msg,
    );
  }

  Future<void> _onSearching(BuildContext context, String searchText) async {
    setState(() {
      _isSearching = true;
    });
    if (_debounceTimer != null && _debounceTimer!.isActive) {
      _debounceTimer!.cancel();
    }
    _debounceTimer = Timer(const Duration(milliseconds: 500), () async {
      _getSearchResults(searchText: searchText);
    });
  }

  _getSearchResults({required String searchText}) {
    searchWithMapBox(
      search: searchText,
      sessionToken: _sessionToken,
      proximity: _currentLocation,
    ).then((value) {
      if (value == null) {
        toast(Globals.language.errorMsg);
        return;
      }

      if (_searchController.text.length >= 3) {
        _addressList = [];
        for (var element in value.suggestions) {
          if (element.fullAddress.isNotEmpty) {
            _addressList.add(element);
          }
        }
        setState(() {
          _isSearching = false;
        });
      }
    });
  }

  _onSelectPickup({required MapBoxSuggestion data}) async {
    showAppActivity();
    await getMapBoxLocation(
            mapBoxId: data.mapBoxId, sessionToken: _sessionToken)
        .then((value) {
      if (value == null) {
        toast(Globals.language.errorMsg);
        return;
      }
      _rideDestination.latitude = value.latitude;
      _rideDestination.longitude = value.longitude;
      _rideDestination.title = data.name.toString();
      _searchController.text = _rideDestination.title;
      setState(() {
        _addressList = [];
      });
    });
    hideKeyboard();
    hideAppActivity();
  }

  Future<void> _pickFromMap() async {
    hideKeyboard();
    setState(() {
      _searchController.text = "";
    });
    MapBoxLocationModel? selectedPlace =
        await launchScreen(const MapPickupScreen());
    if (selectedPlace != null) {
      _rideDestination.latitude = selectedPlace.point!.latitude;
      _rideDestination.longitude = selectedPlace.point.longitude;
      _rideDestination.title = selectedPlace.address.toString();
      setState(() {
        _searchController.text = _rideDestination.title;
      });
    }
  }

  Future<void> _pickFromSavedPlaces() async {
    hideKeyboard();
    setState(() {
      _searchController.text = "";
    });
    SavedPlace? savedPlace =
        await launchScreen(const SavedPlacesScreen(), context: context);
    if (savedPlace != null) {
      _rideDestination.latitude = double.parse(savedPlace.latitude);
      _rideDestination.longitude = double.parse(savedPlace.longitude);
      _rideDestination.title = savedPlace.title;
      setState(() {
        _searchController.text = _rideDestination.title;
      });
    }
  }
}
