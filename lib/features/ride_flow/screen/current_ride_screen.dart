import 'package:animated_marker/animated_marker.dart';
import 'package:flutter/services.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:mapbox_maps_flutter/mapbox_maps_flutter.dart' as mp;
import 'package:mqtt_client/mqtt_client.dart';
import 'package:rider/app_exports.dart';
import 'package:rider/components/booking_widget.dart';
import 'package:rider/features/payment/screens/select_payment_for_additional_charges_screen.dart';
import 'package:rider/features/ride_flow/screen/change_destination_screen.dart';
import 'package:rider/features/ride_flow/screen/driver_estimated_time.dart';
import 'package:rider/features/ride_flow/screen/manage_ride_stops_logic.dart';
import 'package:rider/global/models/ride_model.dart';
import 'package:rider/global/widgets/map_box_wrapper.dart';
import 'package:rider/interactive_onboarding.dart';
import 'package:rider/main.dart';
import 'package:rider/screens/AlertScreen.dart';
import 'package:rider/screens/chat_screen.dart';
import 'package:rider/screens/notification_denied_screen.dart';
import 'package:rider/screens/review_screen.dart';
import 'package:rider/screens/waiting_charges_and_tip_screen.dart';
import 'package:rider/services/ads_service.dart';
import 'package:rider/services/zego_voice_call_service.dart';
import 'dart:ui' as ui;
import 'package:http/http.dart' as http;

import 'package:share_plus/share_plus.dart';
import 'package:zego_uikit_prebuilt_call/zego_uikit_prebuilt_call.dart';

class CurrentRideScreen extends StatefulWidget {
  final RideModel? currentRide;

  const CurrentRideScreen({super.key, required this.currentRide});

  @override
  CurrentRideScreenState createState() => CurrentRideScreenState();
}

class CurrentRideScreenState extends State<CurrentRideScreen> {
  late RideModel _currentRide;
  late OnRideRequest _currentRideData;
  late DateTime _rideRequestedTime;
  bool _areStopMarkersAdded = false;

  Timer? _routeUpdater;
  bool _isSourceToDestinationPolylineCreated = false;
  bool _isDriverToSourcePolylineCreated = false;

  bool _showDriverOutOfRouteWarning = true;

  // final Completer<GoogleMapController> _controller = Completer();
  // Set<Polyline> _polyLines = <Polyline>{};
  // List<LatLng> _polylinePoints = [];

  // MarkerId sourceMarkerId = const MarkerId('Source');
  // MarkerId destinationMarkerId = const MarkerId('Destination');
  // MarkerId driverMarkerId = const MarkerId('Driver');
  // MarkerId riderWithDriverMarkerId = const MarkerId('You and Driver');

  // late final Uint8List driverMapMarker;
  // late final Uint8List destinationMapMarker;
  // late final Uint8List sourceMapMarker;
  // // late final Uint8List riderMapMarker;
  // late final Uint8List driverWithRiderMapMarker;
  // late final Uint8List stopMapMarker;
  bool _rideCanBeCancelled = true;

  // Set<Marker> _staticMarkers = {};
  // Set<Marker> _animatedMarkers = {};
  // final Set<Marker> _stopMarkers = {};
  mp.PointAnnotation? _driverAnnotation;
  mp.PointAnnotation? _driverWithRiderAnnotation;
  bool _isAlreadyNotifiedDashCamInfo = false;

  @override
  void initState() {
    showAppActivity();

    ZegoVoiceCallService.init(
      navigatorKey: navigatorKey,
      appId: Constants.zegoAppId,
      appSign: Constants.zegoAppSign,
      callerId: Globals.user.id.toString(),
      callerName: Globals.user.firstName!,
    );

    Globals.currentRideScreenUpdator = () {
      afterBuildCreated(() {
        getCurrentRide(showLoading: false);
      });
    };
    if (Globals.isMqttConnected) {
      mqttForCurrentRide();
    }
    super.initState();
    // if (widget.currentRide != null) {
    //   _currentRide = widget.currentRide!;
    //   _currentRideData = _currentRide.onRideRequest!;
    //   if (_currentRideData.status == RideStatus.newRideRequested) {
    //   } else {
    //     hideAppActivity();
    //   }
    // }

    if (_routeUpdater == null || !(_routeUpdater!.isActive)) {
      _routeUpdater = Timer.periodic(
          const Duration(
            seconds: 5,
          ), (timer) {
        if (!isAppActivityRunning.value &&
            _currentRideData.status != RideStatus.newRideRequested) {
          getUserDetailLocation();
        }
      });
    }

    init();
  }

  Future<void> _checkNotificationPermission() async {
    var result =
        await OneSignalService.init(appId: Constants.oneSignalRiderAppId);

    if (result == OneSignalServiceStatus.permissionDenied) {
      launchScreen(const NotificationDeniedScreen(), isNewTask: true);
    }
  }

  Future _createMarkerPoint({
    required mp.Position location,
    required String icon,
    required double iconSize,
    bool isDriver = false,
    bool isDriverWithRider = false,
  }) async {
    final ByteData bytes = await rootBundle.load(icon);
    final Uint8List list = bytes.buffer.asUint8List();

    if (isDriver && _driverAnnotation != null) {
      Globals.pointAnnotationManager?.delete(_driverAnnotation!);
    } else if (isDriverWithRider && _driverWithRiderAnnotation != null) {
      if (_driverAnnotation != null) {
        Globals.pointAnnotationManager?.delete(_driverAnnotation!);
      }

      Globals.pointAnnotationManager?.delete(_driverWithRiderAnnotation!);
    }

    var t = await Globals.pointAnnotationManager?.create(
        mp.PointAnnotationOptions(
            image: list,
            iconSize: iconSize,
            geometry: mp.Point(coordinates: location)));

    if (isDriver) {
      _driverAnnotation = t;
    } else if (isDriverWithRider) {
      _driverWithRiderAnnotation = t;
    }
  }

  Future<List<mp.Position>> getRouteCoordinates({
    required mp.Position origin,
    required mp.Position destination,
    List<mp.Position>? waypoints,
  }) async {
    try {
      String url = 'https://api.mapbox.com/directions/v5/mapbox/driving/'
          '${origin.lng},${origin.lat}';

      // Add waypoints to the URL if there are any
      if (waypoints?.isNotEmpty ?? false) {
        String waypointStr = waypoints!
            .map((waypoint) => '${waypoint.lng},${waypoint.lat}')
            .join(';'); // Join waypoints with semicolons
        url = '$url;$waypointStr';
      }

      // Add the destination
      url = '$url;${destination.lng},${destination.lat}'
          '?geometries=geojson&overview=full&steps=true&access_token=${AppCred.mapBoxPublicTokenKey}';

      // Make the request to the Mapbox Directions API
      final response = await http.get(Uri.parse(url));

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final route = data['routes'][0];
        final geometry = route['geometry'];
        final coordinates = geometry['coordinates'];

        List<mp.Position> polylineCoordinates = [];
        for (var coord in coordinates) {
          polylineCoordinates.add(mp.Position(coord[0], coord[1]));
        }

        return polylineCoordinates;
      } else {
        throw Exception('Failed to load route');
      }
    } catch (e) {
      rethrow;
    }
  }

  _createPolyline({
    required mp.Position startLocation,
    required mp.Position endLocation,
    required int colors,
    List<mp.Position>? waypoints,
  }) async {
    List<mp.Position> coordinates = await getRouteCoordinates(
        origin: startLocation, destination: endLocation, waypoints: waypoints);

    // coordinates.forEach((v){
    //   createMarkerPoint(currentLocation: v);

    // });

    Globals.polylineAnnotationManager!.create(mp.PolylineAnnotationOptions(
        lineOpacity: 1,
        lineWidth: 4,
        lineBorderWidth: 4,
        lineBorderColor: colors,
        geometry: mp.LineString(coordinates: coordinates)));
    //  getCurrentRide();
  }

  void init() async {
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) async {
      if (Globals.isMqttConnected == false) {
        showAppDialog(
          dialogType: AppDialogType.error,
          barrierDismissible: false,
          onAccept: () async {
            launchScreen(const DashboardWrapperScreen(), isNewTask: true);
          },
          title: "Mqtt service error",
        );
        return;
      }
      _checkNotificationPermission();

      // driverMapMarker = await getBytesFromAsset(driverIcon);

      // destinationMapMarker = await getBytesFromAsset(destinationIcon);
      // sourceMapMarker = await getBytesFromAsset(sourceIcon);

      // driverWithRiderMapMarker = await getBytesFromAsset(riderWithDriverIcon);
      // stopMapMarker = await getBytesFromAsset(stopMarker);

      await getCurrentRide();

      // animateMap(target: _getInitialCameraPosition());

      // if (widget.currentRide == null) {
      // } else {
      //   if (_currentRide.driver != null) {
      //     await getUserDetailLocation();
      //   }
      //   hideAppActivity();

      // }
    });
  }

  Future<void> _prepareDriverToSourcePolyline() async {
    Globals.polylineAnnotationManager?.deleteAll();
    _areStopMarkersAdded = false;

    _createPolyline(
        startLocation: mp.Position(
            double.parse(_currentRide.driver!.longitude!),
            double.parse(_currentRide.driver!.latitude!)),
        endLocation: mp.Position(double.parse(_currentRideData.startLongitude!),
            double.parse(_currentRideData.startLatitude!)),
        colors: Colors.black.value);

    // _polyLines = {};
    // _polylinePoints = [];

    // var result = await PolylinePoints().getRouteBetweenCoordinates(
    //   request: PolylineRequest(
    //     origin: PointLatLng(
    //       double.parse(_currentRide.driver!.latitude!),
    //       double.parse(_currentRide.driver!.longitude!),
    //     ),
    //     destination: PointLatLng(double.parse(_currentRideData.startLatitude!),
    //         double.parse(_currentRideData.startLongitude!)),
    //     mode: TravelMode.driving,
    //   ),
    //   googleApiKey: Constants.googleMapAPIKey,
    // );

    // for (var point in result.points) {
    //   _polylinePoints.add(
    //     LatLng(point.latitude, point.longitude),
    //   );
    // }
    // _polyLines.add(
    //   Polyline(
    //     polylineId:
    //         PolylineId(DateTime.now().millisecondsSinceEpoch.toString()),
    //     width: 5,
    //     points: _polylinePoints,
    //     color: AppColors.polylineColor,
    //   ),
    // );
  }

  Future<void> _prepareSourceToDestinationPolyline() async {
    Globals.polylineAnnotationManager?.deleteAll();
    _areStopMarkersAdded = false;

    _createPolyline(
        waypoints: (_currentRideData.rideStops ?? [])
            .map((e) => mp.Position(e.longitude, e.latitude))
            .toList(),
        startLocation: mp.Position(
            double.parse(_currentRideData.startLongitude!),
            double.parse(_currentRideData.startLatitude!)),
        endLocation: mp.Position(double.parse(_currentRideData.endLongitude!),
            double.parse(_currentRideData.endLatitude!)),
        colors: Colors.black.value);

    // _polyLines = {};
    // _polylinePoints = [];

    // var result = await PolylinePoints().getRouteBetweenCoordinates(
    //   request: PolylineRequest(
    //     origin: PointLatLng(double.parse(_currentRideData.startLatitude!),
    //         double.parse(_currentRideData.startLongitude!)),
    //     destination: PointLatLng(
    //       double.parse(_currentRideData.endLatitude!),
    //       double.parse(_currentRideData.endLongitude!),
    //     ),
    //     mode: TravelMode.driving,
    //     wayPoints: (_currentRideData.rideStops ?? [])
    //         .map((e) =>
    //             PolylineWayPoint(location: "${e.latitude},${e.longitude}"))
    //         .toList(),
    //   ),
    //   googleApiKey: Constants.googleMapAPIKey,
    // );

    // for (var point in result.points) {
    //   _polylinePoints.add(
    //     LatLng(point.latitude, point.longitude),
    //   );
    // }

    // _polyLines.add(
    //   Polyline(
    //     color: AppColors.polylineColor,
    //     polylineId:
    //         PolylineId(DateTime.now().millisecondsSinceEpoch.toString()),
    //     width: 5,
    //     points: _polylinePoints,
    //   ),
    // );
  }

  Future<Uint8List> getBytesFromAsset(String path) async {
    double pixelRatio = MediaQuery.of(context).devicePixelRatio;
    ByteData data = await rootBundle.load(path);
    ui.Codec codec = await ui.instantiateImageCodec(data.buffer.asUint8List(),
        targetWidth: pixelRatio.round() * 30);
    ui.FrameInfo fi = await codec.getNextFrame();
    return (await fi.image.toByteData(format: ui.ImageByteFormat.png))!
        .buffer
        .asUint8List();
  }

  Future<void> getCurrentRide(
      {bool showLoading = true, bool fromMqtt = false}) async {
    if (showLoading == true) {
      showAppActivity();
    }
    await getCurrentRideRequest().then((value) async {
      if (value.status == false) {
        toast(value.message);
        return;
      }

      if (value.data!.rideRequest == null &&
          value.data!.onRideRequest == null) {
        launchScreen(const DashboardWrapperScreen(), isNewTask: true);
        return;
      }

      _currentRide = value.data!;
      _currentRideData = value.data!.rideRequest ?? value.data!.onRideRequest!;
      Globals.currentRideId = _currentRideData.id!;

      _rideCanBeCancelled = _currentRideData.status != RideStatus.reached;

      if (_currentRide.rideRequest != null) {
        _rideRequestedTime = DateTime.parse(
          value.data!.rideRequest!.datetime!,
        );
      }

      if (_currentRide.driver != null &&
          _currentRideData.status == RideStatus.arriving) {
        if (_currentRide.driver!.isDashCamInstalled!) {
          _showDashCamInfo();
        }
      }

      if (_currentRideData.status != RideStatus.newRideRequested) {
        if (_currentRide.driver != null) {
          Globals.chatDriverData = UserModel.fromJson(
            _currentRide.driver!.toJson(),
          );
        }
      }

      if (!(_currentRide.isSecondPreauth ?? false) &&
          (_currentRideData.status == RideStatus.reached ||
              _currentRideData.status == RideStatus.completed)) {
        launchScreen(WaitingChargesAndTipScreen(request: _currentRide),
            isNewTask: true);
        return;
      } else if (_currentRideData.status == RideStatus.completed) {
        Globals.pointAnnotationManager?.deleteAll();
        Globals.polylineAnnotationManager?.deleteAll();
        _areStopMarkersAdded = false;
        //Mayfix
        // Globals.mapbox = null;
        launchScreen(
          ReviewScreen(
              rideRequest: _currentRideData, driverData: _currentRide.driver),
          pageRouteAnimation: PageRouteAnimation.SlideBottomTop,
          isNewTask: true,
        );
        return;
      }

      // /* check for stops */
      // if ((_currentRideData.rideStops ?? []).isNotEmpty &&
      //     _currentRideData.isPreAuthDoneForStops == false) {
      //   // hideAppActivity();
      //   // _showPendingPaymentForStops(isDestinationChange: false);
      //   // return;
      // }

      // /* check for destination changes */
      // else if ((_currentRideData.destinationPlaces ?? []).isNotEmpty &&
      //     _currentRideData.isPreAuthDoneForDestinationChange == false) {
      //   // hideAppActivity();
      //   // _showPendingPaymentForStops(isDestinationChange: true);
      // } else {

      // We'll add stop markers in getUserDetailLocation to ensure they're not deleted
      await getUserDetailLocation();
      // }
    }).onError((error, stackTrace) {
      log(error.toString());
      handleError(error, stackTrace);
    });
  }

  void _showPendingPaymentForStops({
    required bool isDestinationChange,
  }) {
    Globals.isRidePendingForPayment = true;
    showAppDialog(
        barrierDismissible: false,
        onAccept: () {
          Navigator.of(context).pop();
          if (isDestinationChange) {
            _getDestinationChangedPrice();
          } else {
            _getStopsPrice();
          }
        },
        dialogType: AppDialogType.info,
        title:
            "You may have pending payment due to ${isDestinationChange ? "destination change" : "ride stops changes"}.\nNow we calculate the new price for you.");
  }

  Future<void> _getStopsPrice() async {
    showAppActivity();
    var response = await getRideStopEstimatePrice(rideId: _currentRideData.id!);
    if (!response.status) {
      toast(Globals.language.errorMsg);
      Future.delayed(
        const Duration(
          seconds: 3,
        ),
        () {
          getCurrentRide();
        },
      );
      return;
    }

    if (response.data!.amount <= 0) {
      _showNoPaymentIsNeeded();
    } else {
      _showPaymentIsNeeded(
        isDestinationChange: false,
        amount: response.data!.amount,
      );
    }
  }

  Future<void> _getDestinationChangedPrice() async {
    showAppActivity();
    var response =
        await getRideChangeDestinationPrice(rideId: _currentRideData.id!);
    if (!response.status) {
      toast(Globals.language.errorMsg);
      Future.delayed(
        const Duration(
          seconds: 3,
        ),
        () {
          getCurrentRide();
        },
      );
      return;
    }
    if (response.data!.amount <= 0) {
      _showNoPaymentIsNeeded();
    } else {
      _showPaymentIsNeeded(
        isDestinationChange: true,
        amount: response.data!.amount,
      );
    }
  }

  void _showNoPaymentIsNeeded() {
    hideAppActivity();
    showAppDialog(
      dialogType: AppDialogType.info,
      title: "No payment is needed",
      onAccept: () {
        Navigator.of(context).pop();
        Globals.isRidePendingForPayment = false;
      },
    );
  }

  void _showPaymentIsNeeded(
      {required num amount, required bool isDestinationChange}) {
    showAppDialog(
      barrierDismissible: false,
      dialogType: AppDialogType.info,
      title:
          "Additional payment of ${Constants.currencySymbol}$amount  is need to continue the ride",
      onAccept: () {
        Globals.isRidePendingForPayment = false;
        if (isDestinationChange) {
          launchScreen(
            SelectPaymentForAdditionalCharges(
              payableAmount: amount,
              rideId: _currentRideData.id!,
              chargesType: AdditionalChargesType.destinationChange,
            ),
            isNewTask: true,
          );
        } else {
          launchScreen(
            SelectPaymentForAdditionalCharges(
              payableAmount: amount,
              rideId: _currentRideData.id!,
              chargesType: AdditionalChargesType.stopsChange,
            ),
            isNewTask: true,
          );
        }
      },
    );
  }

  // onMapCreated(GoogleMapController controller) {
  //   _controller.complete(controller);
  // }

  Future<void> animateMap({
    required mp.Position target,
    double? bearing,
    double? zoom,
  }) async {
    Globals.mapbox!.flyTo(
        mp.CameraOptions(zoom: 16, center: mp.Point(coordinates: target)),
        mp.MapAnimationOptions());
    // final GoogleMapController controller = await _controller.future;
    // controller.animateCamera(
    //   CameraUpdate.newCameraPosition(
    //     CameraPosition(
    //         // tilt: 90,
    //         target: target,
    //         zoom: 11.0),
    //     // CameraPosition(target: driverLocation!, zoom: await controller.getZoomLevel(), bearing: driverMarkerRotation),
    //   ),
    // );
  }

  Widget currentLocationButton() {
    return ElevatedButton(
      onPressed: () {
        mp.Position location;

        if (_currentRide.driver == null) {
          location = mp.Position(
              double.parse(_currentRide.rideRequest!.startLongitude!),
              double.parse(_currentRide.rideRequest!.startLatitude!));
        } else {
          location = mp.Position(
            double.parse(_currentRide.driver!.longitude!),
            double.parse(_currentRide.driver!.latitude!),
          );
        }
        animateMap(target: location);
      },
      style: ElevatedButton.styleFrom(
        shape: const CircleBorder(),
        elevation: 4,
        padding: const EdgeInsets.all(8),
        backgroundColor: Colors.white, // <-- Button color
      ),
      child: const Icon(Icons.gps_fixed, color: Colors.black),
    );
  }

  mqttForCurrentRide() async {
    if (Globals.mqttClient.connectionStatus!.state !=
        MqttConnectionState.connected) {
      await AppMqttService.initialize();
    }
    Globals.mqttClient.subscribe(
        'ride_request_status_${Globals.user.id}', MqttQos.atLeastOnce);

    Globals.mqttClient.updates!
        .listen((List<MqttReceivedMessage<MqttMessage?>>? c) {
      // if (GlobalState.isChatScreenAlreadyOpened) {
      //   Navigator.of(context).pop();
      // }

      final MqttPublishMessage recMess = c![0].payload as MqttPublishMessage;
      final pt =
          MqttPublishPayload.bytesToStringAsString(recMess.payload.message);

      if (jsonDecode(pt)['success_type'] == RideStatus.accepted ||
          jsonDecode(pt)['success_type'] == RideStatus.arriving ||
          jsonDecode(pt)['success_type'] == RideStatus.arrived ||
          jsonDecode(pt)['success_type'] == RideStatus.inProgress ||
          jsonDecode(pt)['success_type'] == RideStatus.reached ||
          jsonDecode(pt)['success_type'] == RideStatus.stopsUpdated ||
          jsonDecode(pt)['success_type'] == RideStatus.destinationUpdated) {
        getCurrentRide(fromMqtt: true);
      } else if (jsonDecode(pt)['success_type'] ==
          RideStatus.newRideAfterDriverCanceled) {
        getCurrentRide(fromMqtt: true);
        /* reload app*/
        // launchScreen(const DashboardWrapperScreen(), isNewTask: true);
      } else if (jsonDecode(pt)['success_type'] ==
          RideStatus.driverStartedWaitingTime) {
        _notifyThatDriverStartedTheWaitingTime();
      } else if (jsonDecode(pt)['success_type'] == RideStatus.canceled) {
        showAppDialog(
          title: "Cancelled by driver",
          dialogType: AppDialogType.info,
          barrierDismissible: false,
          onAccept: () {
            Globals.isDashboardRiderMarkerCreated = false;
            launchScreen(const DashboardWrapperScreen(), isNewTask: true);
          },
        );
      } else if (jsonDecode(pt)['success_type'] == RideStatus.driverCanceled) {
        if (_currentRide.driver != null) {
          showAppDialog(
            title: Globals.language.rideIsCancelledByDriver,
            dialogType: AppDialogType.error,
            barrierDismissible: false,
            onAccept: () {
              Globals.isDashboardRiderMarkerCreated = false;
              launchScreen(const DashboardWrapperScreen(), isNewTask: true);
            },
          );
        }
      } else if (jsonDecode(pt)['success_type'] == RideStatus.reached) {
        getCurrentRide(fromMqtt: true);
      } else if (jsonDecode(pt)['success_type'] == RideStatus.completed) {
        getCurrentRide(fromMqtt: true);
      } else if (jsonDecode(pt)['success_type'] ==
          RideStatus.driverOutOfRoute) {
        _showDriverOutOfRoute(
          msg: jsonDecode(pt)['success_message'],
        );
      }
    });
  }

  void _showDriverOutOfRoute({
    required String msg,
  }) {
    if (_showDriverOutOfRouteWarning) {
      _showDriverOutOfRouteWarning = false;
      showAppDialog(
        dialogType: AppDialogType.error,
        title: msg,
        positiveButtonText: "Open SOS",
        onAccept: () async {
          Navigator.of(context).pop();
          await showDialog(
            context: context,
            builder: (_) {
              return AlertDialog(
                contentPadding: const EdgeInsets.all(0),
                content: AlertScreen(
                  rideId: _currentRideData.id,
                  regionId: _currentRideData.regionId,
                ),
              );
            },
          );
          // _showDriverOutOfRouteWarning = true;
        },
      );
    }
  }

  // Add a helper method to add stop markers
  Future<void> _addStopMarkers() async {
    if (_areStopMarkersAdded == false) {
      _areStopMarkersAdded = true;
      if ((_currentRideData.rideStops ?? []).isNotEmpty) {
        RideStop stop;
        for (var i = 0; i < _currentRideData.rideStops!.length; i++) {
          stop = _currentRideData.rideStops![i];
          await _createMarkerPoint(
              isDriver: false,
              location: mp.Position(stop.longitude, stop.latitude),
              icon: Assets.stopMarker,
              iconSize: 0.5);
        }
        setState(() {
          //
        });
      }
    }
  }

  Future<void> getUserDetailLocation() async {
    if (_currentRide.driver == null) {
      if (_isSourceToDestinationPolylineCreated == false) {
        /* source */
        Globals.pointAnnotationManager?.deleteAll();
        _areStopMarkersAdded = false;

        await await _createMarkerPoint(
            isDriver: false,
            location: mp.Position(
                double.parse(_currentRideData.startLongitude!),
                double.parse(_currentRideData.startLatitude!)),
            icon: Assets.sourceIcon,
            iconSize: .5);

        /* destination */

        await _createMarkerPoint(
            isDriver: false,
            location: mp.Position(double.parse(_currentRideData.endLongitude!),
                double.parse(_currentRideData.endLatitude!)),
            icon: Assets.destinationIcon,
            iconSize: .1);

        // Add stop markers after adding source and destination

        await _prepareSourceToDestinationPolyline();
        setState(() {
          _isSourceToDestinationPolylineCreated = true;
        });
        hideAppActivity();
      }
    } else {
      late mp.Position driverLocation;
      await getUserDetail(userId: _currentRide.driver!.id).then((value) async {
        driverLocation = mp.Position(double.parse(value.data!.longitude!),
            double.parse(value.data!.latitude!));

        if (_currentRideData.status == RideStatus.arriving) {
          /* driver */

          await _createMarkerPoint(
              isDriver: true,
              location: driverLocation,
              icon: Assets.driverIcon,
              iconSize: .3);

          /* source */

          await _createMarkerPoint(
              isDriver: false,
              location: mp.Position(
                  double.parse(_currentRideData.startLongitude!),
                  double.parse(_currentRideData.startLatitude!)),
              icon: Assets.sourceIcon,
              iconSize: .5);

          if (_isDriverToSourcePolylineCreated == false) {
            _isSourceToDestinationPolylineCreated = false;
            await _prepareDriverToSourcePolyline();
            _isDriverToSourcePolylineCreated = true;
          }
        } else if (_currentRideData.status == RideStatus.arrived) {
          Globals.pointAnnotationManager?.deleteAll();
          _areStopMarkersAdded = false;

          /* source */

          await _createMarkerPoint(
              isDriver: false,
              location: mp.Position(
                  double.parse(_currentRideData.startLongitude!),
                  double.parse(_currentRideData.startLatitude!)),
              icon: Assets.sourceIcon,
              iconSize: .5);

          /* destination */

          await _createMarkerPoint(
              isDriver: false,
              location: mp.Position(
                  double.parse(_currentRideData.endLongitude!),
                  double.parse(_currentRideData.endLatitude!)),
              icon: Assets.destinationIcon,
              iconSize: .1);

          if (_isSourceToDestinationPolylineCreated == false) {
            _isDriverToSourcePolylineCreated == false;
            await _prepareSourceToDestinationPolyline();
            _isSourceToDestinationPolylineCreated = true;
          }
        } else {
          /* remove old driver */
          // _animatedMarkers = {};

          /* driver with rider */
          await _createMarkerPoint(
              isDriverWithRider: true,
              location: driverLocation,
              icon: Assets.riderWithDriverIcon,
              iconSize: .2);

          /* source */

          await _createMarkerPoint(
              isDriver: false,
              location: mp.Position(
                  double.parse(_currentRideData.startLongitude!),
                  double.parse(_currentRideData.startLatitude!)),
              icon: Assets.sourceIcon,
              iconSize: .5);

          /* destination */
          await _createMarkerPoint(
              isDriver: false,
              location: mp.Position(
                  double.parse(_currentRideData.endLongitude!),
                  double.parse(_currentRideData.endLatitude!)),
              icon: Assets.destinationIcon,
              iconSize: .1);

          if (_isSourceToDestinationPolylineCreated == false) {
            _isDriverToSourcePolylineCreated == false;
            await _prepareSourceToDestinationPolyline();
            _isSourceToDestinationPolylineCreated = true;
          }
        }

        setState(() {
          hideAppActivity();
        });
      }).catchError((error) {});
    }
    await _addStopMarkers();
    hideAppActivity();
  }

  void confirmQuietPeriodCancelRide(int id, String msg) {
    showAppDialog(
        title: msg,
        dialogType: AppDialogType.confirmation,
        onAccept: () {
          Navigator.of(context).pop();
          cancelRequest(flag: false);
        });
  }

  Future<void> cancelRequest({required bool flag}) async {
    showAppActivity();

    Map req = {
      "id": _currentRideData.id,
      "cancel_by": UserType.rider,
      "status": RideStatus.canceled,
      'is_flag': flag,
    };

    await rideRequestUpdate(request: req, rideId: _currentRideData.id)
        .then((value) async {
      if (value.status == true) {
        if (value.is_flag == true) {
          hideAppActivity();
          confirmQuietPeriodCancelRide(req['id'], value.message ?? '');
        } else {
          Globals.sharedPrefs.remove(SPKeys.remainingTime);
          Globals.isDashboardRiderMarkerCreated = false;
          launchScreen(const DashboardWrapperScreen(), isNewTask: true);

          toast(value.message);
        }
      } else {
        hideAppActivity();
        toast(value.message);
      }
    }).onError((error, stackTrace) {
      hideAppActivity();

      toast(Globals.language.errorMsg);
      handleError(error, stackTrace);
    });

    hideAppActivity();
  }

  Widget _getAddStopWidget() {
    if (_currentRideData.isPoolingRide == true) {
      return const SizedBox();
    }

    return Row(
      children: [
        const Expanded(
          child: Text(
            "Want to add a stop?",
            style: TextStyle(
              fontSize: 18,
              // color: Colors.black,
            ),
          ),
        ),

        IconButton(
            style: ButtonStyle(
              backgroundColor:
                  MaterialStateProperty.all<Color>(Colors.grey.shade300),
            ),
            onPressed: _addStop,
            icon: const Icon(
              Icons.add,
              size: 22,
              color: Colors.black,
            )),

        // AppButtonWidget(
        //   fullWidth: false,
        //   text: "Add Stop",
        //   onTap: _addStop,
        // ),
      ],
    );
  }

  Future<void> _addStop() async {
    bool? isReloadRequired = await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => ManageRideStopScreen(
          currentRide: _currentRide,
        ),
      ),
    );
    if (isReloadRequired == true) {
      _isSourceToDestinationPolylineCreated = false;
      getCurrentRide(
        fromMqtt: true,
      );
    }
  }

  Widget _getChangeDestinationWidget() {
    if (_currentRideData.isPoolingRide == true) {
      return const SizedBox();
    }
    return Row(
      children: [
        const Expanded(
          child: Text(
            "Want to change destination?",
            style: TextStyle(
              fontSize: 18,
              // color: Colors.black,
            ),
          ),
        ),
        IconButton(
            style: ButtonStyle(
              backgroundColor:
                  MaterialStateProperty.all<Color>(Colors.grey.shade300),
            ),
            onPressed: _changeDestination,
            icon: const Icon(
              Icons.edit,
              size: 22,
              color: Colors.black,
            )),

        // AppButtonWidget(
        //     fullWidth: false, text: "Change", onTap: _changeDestination),
      ],
    );
  }

  Future<void> _changeDestination() async {
    if (_currentRideData.destinationPlaces!.length >= 3) {
      toast("Only 3 destination changes are allowed");
      return;
    }
    bool? isReloadRequired = await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => ChangeDestinationScreen(
          rideId: _currentRideData.id!,
        ),
      ),
    );
    if (isReloadRequired == true) {
      _isSourceToDestinationPolylineCreated = false;
      getCurrentRide(
        fromMqtt: true,
      );
    }
  }

  Widget _getShareRideWidget() {
    if ((_currentRideData.rideURL ?? "").trim().isEmpty) {
      return const SizedBox();
    }

    return Row(
      children: [
        const Expanded(
          child: Text(
            "Want to share this ride?",
            style: TextStyle(
              fontSize: 18,
              // color: Colors.black,
            ),
          ),
        ),
        IconButton(
            style: ButtonStyle(
              backgroundColor:
                  MaterialStateProperty.all<Color>(Colors.grey.shade300),
            ),
            onPressed: _shareRide,
            icon: const Icon(
              Icons.share_outlined,
              size: 22,
              color: Colors.black,
            )),

        // AppButtonWidget(fullWidth: false, text: "Share", onTap: _shareRide),
      ],
    );
  }

  Widget _getPlateNumberWidget() {
    if ((_currentRideData.rideURL ?? "").trim().isEmpty) {
      return const SizedBox();
    }

    return Row(
      children: [
        const Expanded(
          child: Text(
            "Vehicle plate number ",
            style: TextStyle(
              fontSize: 18,
              // color: Colors.black,
            ),
          ),
        ),
        Text(
          _currentRideData.vehicle_detail?.plateNumber ?? "",
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            // color: Colors.black,
          ),
        ),
      ],
    );
  }

  Future<void> _shareRide() async {
    showAppActivity();
    try {
      await Share.share(
        "Hi, I'm sharing my trip details. You can track my progress here: ${_currentRideData.rideURL!}",
      );
    } catch (e) {
      toast(Globals.language.errorMsg);
    }
    hideAppActivity();
  }

  Widget _getRideAcceptedView(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(
          height: 20,
        ),
        _getAddStopWidget(),
        _getChangeDestinationWidget(),
        _getShareRideWidget(),
        const Divider(),
        _getPlateNumberWidget(),
        const Divider(),
        _nextStopView(),
        const SizedBox(
          height: 20,
        ),
        Row(
          children: [
            Expanded(
              child: Text(
                _currentRideData.status == RideStatus.driverCanceled
                    ? Globals.language.rideIsCancelledByDriverButWaitingForAdmin
                    : (_currentRideData.status == RideStatus.arrived)
                        ? Globals.language.driverArrivedText
                        : (_currentRideData.status == RideStatus.inProgress)
                            ? Globals.language.youAreOnTheWay
                            : (_currentRideData.status == RideStatus.reached)
                                ? Globals.language.youReached
                                : Globals.language.driverArrivingText,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
                style: TextStyle(
                  fontSize: _currentRideData.status == RideStatus.driverCanceled
                      ? 18
                      : 22,
                  fontWeight: FontWeight.bold,
                  color: _currentRideData.status == RideStatus.driverCanceled
                      ? null
                      : Colors.red,
                ),
              ),
            ),
            _currentRideData.status == RideStatus.inProgress
                ? inkWellWidget(
                    onTap: () {
                      showDialog(
                        context: context,
                        builder: (_) {
                          return AlertDialog(
                            contentPadding: const EdgeInsets.all(0),
                            content: AlertScreen(
                              rideId: _currentRideData.id,
                              regionId: _currentRideData.regionId,
                            ),
                          );
                        },
                      );
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          vertical: 4, horizontal: 8),
                      decoration: BoxDecoration(
                        color: Colors.green,
                        borderRadius: radius(),
                      ),
                      child: Text(Globals.language.sos,
                          style: boldTextStyle(
                            color: Colors.white,
                            size: 22,
                          )),
                    ),
                  )
                : const SizedBox()
          ],
        ),

        ((_currentRideData.isOTPSharingEnabled ?? true) &&
                (_currentRideData.status == RideStatus.arrived
                //  || (_currentRideData.status == RideStatus.reached &&
                // (_currentRide.is_second_preauth ?? false))
                ))
            ? Column(
                children: [
                  const SizedBox(
                    height: 10,
                  ),
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: const BoxDecoration(
                      color: AppColors.lightThemePrimaryColor,
                      borderRadius: BorderRadius.all(
                        Radius.circular(
                          8,
                        ),
                      ),
                    ),
                    child: Center(
                      child: Text(
                        Globals.language.passCode +
                            ((_currentRideData.status == RideStatus.arrived
                                ? ' ${_currentRideData.otp ?? ''}'
                                : ' ${_currentRideData.reachedOTP ?? ''}')),
                        style: const TextStyle(
                          fontSize: 22,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ),
                ],
              )
            : const SizedBox(),
        const SizedBox(
          height: 10,
        ),
        Text(
          (_currentRideData.status != RideStatus.inProgress &&
                  _currentRideData.status != RideStatus.arrived)
              ? _currentRideData.startAddress ?? ''
              : _currentRideData.endAddress ?? '',
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
          style: const TextStyle(
            fontSize: 18,
            // color: Colors.black,
          ),
        ),
        const Divider(
          thickness: 2,
        ),
        _currentRideData.status == RideStatus.arriving
            ? Row(
                children: [
                  Expanded(
                    child: Text(
                      Globals.language.yourArrivalTime,
                      maxLines: 3,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        // color: Colors.black,
                      ),
                    ),
                  ),
                  const SizedBox(
                    width: 6,
                  ),
                  DriverEstimatedTime(
                    rideId: _currentRideData.id!,
                  ),
                ],
              )
            : const SizedBox(),
        const SizedBox(
          height: 10,
        ),
        Row(
          children: [
            Expanded(
              child: Stack(
                children: [
                  CachedNetworkImage(
                    imageUrl: _currentRide.driver!.profileImage ?? '',
                    imageBuilder: (context, imageProvider) {
                      return Container(
                        height: 100,
                        width: 100,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          image: DecorationImage(
                            image: imageProvider,
                            fit: BoxFit.cover,
                          ),
                        ),
                      );
                    },
                    placeholder: (context, url) => Container(
                      height: 100,
                      width: 100,
                      decoration: const BoxDecoration(
                        shape: BoxShape.circle,
                      ),
                      child: const Center(
                        child: SizedBox(
                            height: 30,
                            width: 30,
                            child: CircularProgressIndicator()),
                      ),
                    ),
                    errorWidget: (context, url, error) => Container(
                      height: 100,
                      width: 100,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: Colors.grey,
                          width: 1,
                        ),
                      ),
                      child: const Icon(
                        Icons.account_circle_sharp,
                        size: 90,
                      ),
                    ),
                  ),
                  // CachedNetworkImage(
                  //   imageUrl: Globals.user.profileImage ?? "",
                  //   imageBuilder: (context, imageProvider) {
                  //     return Container(
                  //       height: 100,
                  //       width: 100,
                  //       decoration: BoxDecoration(
                  //         shape: BoxShape.circle,
                  //         image: DecorationImage(
                  //           image: imageProvider,
                  //           fit: BoxFit.cover,
                  //         ),
                  //       ),
                  //     );
                  //   },
                  //   placeholder: (context, url) => Container(
                  //     height: 100,
                  //     width: 100,
                  //     decoration: const BoxDecoration(
                  //       shape: BoxShape.circle,
                  //     ),
                  //     child: const Center(
                  //       child: SizedBox(
                  //           height: 30,
                  //           width: 30,
                  //           child: CircularProgressIndicator()),
                  //     ),
                  //   ),
                  //   errorWidget: (context, url, error) => Container(
                  //     height: 100,
                  //     width: 100,
                  //     decoration: BoxDecoration(
                  //       shape: BoxShape.circle,
                  //       border: Border.all(
                  //         color: Colors.grey,
                  //         width: 1,
                  //       ),
                  //     ),
                  //     child: const Icon(
                  //       Icons.account_circle_sharp,
                  //       size: 90,
                  //     ),
                  //   ),
                  // ),
                ],
              ),
            ),
            SizedBox(
              width: 120,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    _currentRide.driver!.displayName ?? "",
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    textAlign: TextAlign.end,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      // color: Colors.black,
                    ),
                  ),
                  // Text(
                  //   driverData?.gender ?? ' ',
                  //   style: TextStyle(
                  //     fontSize: 18,
                  //   ),
                  // ),
                  (_currentRide.driver!.rating) != null
                      ? SizedBox(
                          height: 10,
                          child: RatingBar.builder(
                            itemSize: 20,
                            ignoreGestures: true,
                            initialRating: _currentRide.driver!.rating! + 0.0,
                            direction: Axis.horizontal,
                            glow: false,
                            wrapAlignment: WrapAlignment.spaceBetween,
                            itemCount: 5,
                            itemPadding: EdgeInsets.zero,
                            itemBuilder: (context, _) => const Icon(
                              Icons.star,
                              color: Colors.amber,
                            ),
                            onRatingUpdate: (rating) {},
                          ),
                        )
                      : const SizedBox(),
                ],
              ),
            ),
          ],
        ),
        const SizedBox(
          height: 20,
        ),
        // _currentRideData.status == RideStatus.arriving || _currentRideData.status == ARRIVED
        //     ?
        Row(
          children: [
            Expanded(
              child: InkWell(
                onTap: () {
                  launchScreen(
                    ChatScreen(
                      userData:
                          UserModel.fromJson(_currentRide.driver!.toJson()),
                      rideId: _currentRideData.id!,
                    ),
                  );
                },
                child: Container(
                  height: 50,
                  decoration: BoxDecoration(
                      color: Colors.grey[100],
                      borderRadius: BorderRadius.circular(
                        20,
                      )),
                  child: Align(
                      alignment: Alignment.centerLeft,
                      child: Padding(
                        padding: const EdgeInsets.only(left: 8.0),
                        child: Text(
                          Globals.language.chatWithYourDriver,
                          style: const TextStyle(
                            fontSize: 16,
                            color: Colors.grey,
                          ),
                        ),
                      )),
                ),
              ),
            ),
            ZegoSendCallInvitationButton(
              onWillPressed: () async {
                return await _doCall();
              },
              onPressed: (code, message, p2) {
                if (code == Constants.zegoNoUserError) {
                  toast("The receiver is not online");
                }
              },
              callID: DateTime.now().millisecondsSinceEpoch.toString(),
              timeoutSeconds: 20,
              icon: ButtonIcon(
                icon: const Icon(
                  Icons.call,
                  // color: Colors.black,
                ),
              ),
              buttonSize: const Size.square(50),
              iconSize: const Size.square(50),
              verticalLayout: false,
              isVideoCall: false,
              resourceID: Constants.zegoResorceId,
              invitees: [
                ZegoUIKitUser(
                  id: _currentRide.driver!.id.toString(),
                  name: _currentRide.driver!.displayName!,
                ),
              ],
            ),
          ],
        )
        // : const SizedBox()
      ],
    );
  }

  @override
  void dispose() {
    if (_routeUpdater != null && _routeUpdater!.isActive) {
      _routeUpdater!.cancel();
    }
    Globals.currentRideScreenUpdator = null;

    super.dispose();
  }

  @override
  void setState(fn) {
    if (mounted) super.setState(fn);
  }

  double _getSlidingUpPanelMaxHeight() {
    if (_currentRide.driver == null) {
      return MediaQuery.sizeOf(context).width + 210;
    }
    if (Platform.isAndroid) {
      if (_currentRideData.status == RideStatus.arriving) {
        return MediaQuery.sizeOf(context).width + 130;
      } else if (_currentRideData.status == RideStatus.arrived ||
          _currentRideData.status == RideStatus.reached) {
        return MediaQuery.sizeOf(context).width + 70;
      } else if (_currentRideData.status == RideStatus.driverCanceled) {
        return MediaQuery.sizeOf(context).width + 30;
      } else if (_currentRideData.status == RideStatus.inProgress) {
        return MediaQuery.sizeOf(context).width + 130;
      }
      return MediaQuery.sizeOf(context).width + 70;
    } else {
      if (_currentRideData.status == RideStatus.arriving) {
        return MediaQuery.sizeOf(context).width + 30;
      } else if (_currentRideData.status == RideStatus.arrived ||
          _currentRideData.status == RideStatus.reached) {
        return MediaQuery.sizeOf(context).width + 70;
      } else if (_currentRideData.status == RideStatus.driverCanceled) {
        return MediaQuery.sizeOf(context).width + 30;
      } else if (_currentRideData.status == RideStatus.inProgress) {
        return MediaQuery.sizeOf(context).width;
      }
      return MediaQuery.sizeOf(context).width;
    }
  }

  mp.Position _getInitialCameraPosition() {
    if (isAppActivityRunning.value) {
      return mp.Position(0, 0);
    } else if (_currentRide.driver == null) {
      return mp.Position(
          double.parse(_currentRide.rideRequest!.startLongitude!),
          double.parse(_currentRide.rideRequest!.startLatitude!));
    }
    return mp.Position(double.parse(_currentRide.driver!.longitude!),
        double.parse(_currentRide.driver!.latitude!));
  }

  @override
  Widget build(BuildContext context) {
    SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
      statusBarBrightness: Brightness.light,
      statusBarIconBrightness: Brightness.dark,
    ));

    return PopScope(
      canPop: false,
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        extendBodyBehindAppBar: true,
        body: Stack(
          alignment: Alignment.bottomCenter,
          children: [
            const MapBoxWrapperWidget(
              isDashboardScreen: false,
              key: ValueKey("map"),
            ),
            // AnimatedMarker(
            //   staticMarkers: {..._staticMarkers, ..._stopMarkers},
            //   animatedMarkers: _animatedMarkers,
            //   duration: const Duration(seconds: 1),
            //   builder: (context, animatedMarkers) {
            //     return GoogleMap(
            //       tiltGesturesEnabled: false,
            //       rotateGesturesEnabled: false,
            //       zoomControlsEnabled: false,
            //       mapToolbarEnabled: false,
            //       compassEnabled: true,
            //       onMapCreated: onMapCreated,
            //       initialCameraPosition: CameraPosition(
            //           target: _getInitialCameraPosition(), zoom: 11.0),
            //       markers: {
            //         ..._staticMarkers,
            //         ..._animatedMarkers,
            //         ..._stopMarkers
            //       },
            //       mapType: MapType.normal,
            //       polylines: _polyLines,
            //     );
            //   },
            // ),
            SlidingUpPanel(
              color: checkIfDarkModeIsOn(context) ? Colors.black : Colors.white,
              padding: const EdgeInsets.only(top: 16, left: 16, right: 16),
              borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(20), topRight: Radius.circular(20)),
              // backdropColor: getPrimaryColor(),
              backdropTapClosesPanel: true,
              defaultPanelState: PanelState.OPEN,

              minHeight: 70,
              maxHeight: isAppActivityRunning.value
                  ? 100
                  : _getSlidingUpPanelMaxHeight(),
              // panel:
              panelBuilder: (sc) => SingleChildScrollView(
                controller: sc,
                child: Padding(
                  padding: const EdgeInsets.only(
                    top: 18.0,
                    bottom: 25,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Center(
                        child: Container(
                          alignment: Alignment.center,
                          margin: const EdgeInsets.only(bottom: 16),
                          height: 5,
                          width: 70,
                          decoration: BoxDecoration(
                            color: checkIfDarkModeIsOn(context)
                                ? Colors.white
                                : Colors.black,
                            // borderRadius: BorderRadius.circular(defaultRadius),
                          ),
                        ),
                      ),
                      isAppActivityRunning.value
                          ? const SizedBox()
                          : _currentRide.driver != null
                              ? _getRideAcceptedView(context)
                              : _getSearchingView()
                    ],
                  ),
                ),
              ),
            ),
            _rideCanBeCancelled
                ? Positioned(
                    top: Platform.isIOS ? 80 : 60,
                    left: 10,
                    child: AppButton(
                      backgoundColor: Colors.white,
                      text: "Cancel Ride",
                      onPressed: () {
                        showAppDialog(
                          title: Globals
                              .language.areYouSureYouWantToCancelThisRide,
                          dialogType: AppDialogType.confirmation,
                          onAccept: () {
                            Navigator.of(context).pop();
                            cancelRequest(flag: true);
                          },
                        );
                      },
                    ))
                : const SizedBox(),
            Positioned(
              top: Platform.isIOS ? 80 : 60,
              right: 0,
              child: currentLocationButton(),
            ),
            const ActivityIndicator(),
          ],
        ),
      ),
    );
  }

  Widget _getSearchingView() {
    return Column(
      children: [
        BookingWidget(
          id: _currentRideData.id,
          rideRequestedTime: _rideRequestedTime,
          onCancel: () {
            showAppDialog(
                title: Globals.language.areYouSureYouWantToCancelThisRide,
                dialogType: AppDialogType.confirmation,
                onAccept: () {
                  Navigator.of(context).pop();
                  cancelRequest(flag: true);
                });
          },
        ),
        const AppAdWidget(adType: AdType.searchingRide),
        const SizedBox(
          height: 20,
        ),
      ],
    );
  }

  Future<bool> _doCall() async {
    showAppActivity();
    var result = await ZegoVoiceCallService.call();
    if (result == ZegoVoiceCallServiceStatus.microphonePermissionDenied) {
      _notifyMicrophonePermissionDenied();
    } else if (result ==
        ZegoVoiceCallServiceStatus.systemAlertPermissionDenied) {
      _notifySystemAlertPermissionDenied();
    } else if (result != ZegoVoiceCallServiceStatus.initialised) {
      toast(Globals.language.errorMsg);
    }
    hideAppActivity();
    return result == ZegoVoiceCallServiceStatus.initialised;
  }

  void _notifyMicrophonePermissionDenied() {
    showAppDialog(
      dialogType: AppDialogType.info,
      title:
          "For the calling feature, please allow the \"Microphone/Record Audio\" permission.",
      onAccept: () async {
        await openAppSettings();
        if (mounted) {
          Navigator.of(context).pop();
        }
      },
    );
  }

  void _notifySystemAlertPermissionDenied() {
    showAppDialog(
      dialogType: AppDialogType.info,
      title:
          "For the calling feature, please allow the \"Display over other apps\" permission.",
      onAccept: () async {
        await openAppSettings();
        if (mounted) {
          Navigator.of(context).pop();
        }
      },
    );
  }

  void _showDashCamInfo() async {
    if (_isAlreadyNotifiedDashCamInfo) {
      return;
    }
    _isAlreadyNotifiedDashCamInfo = true;
    showAppDialog(
      dialogType: AppDialogType.info,
      title:
          "Just a quick heads up: This vehicle has a dash cam installed.\n\nHave a great ride!",
      onAccept: () async {
        Navigator.of(context).pop();
      },
    );
  }

  void _notifyThatDriverStartedTheWaitingTime() async {
    if (_isRiderAlreadyReadingTheWaitingTime) {
      return;
    }
    _isRiderAlreadyReadingTheWaitingTime = true;
    showAppDialog(
      dialogType: AppDialogType.info,
      title:
          "Your driver has started the waiting time. This will be added to your ride cost. Please complete your preparations quickly to continue the ride.",
      onAccept: () async {
        Navigator.of(context).pop();
        _isRiderAlreadyReadingTheWaitingTime = false;
      },
    );
  }

  bool _isRiderAlreadyReadingTheWaitingTime = false;

  Widget _nextStopView() {
    if (_currentRideData.status != RideStatus.inProgress ||
        _currentRideData.nextStop == null) {
      return const SizedBox();
    }
    return InkWell(
      onTap: () {
        _addStop();
      },
      child: Card(
        elevation: 4,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              const Icon(
                Icons.location_on,
                color: Colors.blue,
                size: 32,
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      "Next stop",
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.grey,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      _currentRideData.nextStop?.title ?? '',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    // Text(
                    // _currentRideData.nextStop?.currentAddress ?? '',
                    // style: const TextStyle(
                    //   fontSize: 10,
                    //   fontWeight: FontWeight.bold,
                    // ),
                    // ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
