import 'package:rider/features/booking/models/current_ride_response_model.dart';
import 'package:rider/features/ride_flow/model/change_destination_response_model.dart';
import 'package:rider/features/ride_flow/model/user_response_model.dart';
import 'package:rider/global/models/status_message_model.dart';
import 'package:rider/network/network_utils.dart';

class RideRepository {
  Future<UserResponseModel> getUserDetail({required int driverId}) async {
    return UserResponseModel.fromJson(await handleResponse(
        await buildHttpResponse('user-detail?id=$driverId',
            method: HttpMethod.get)));
  }

  Future<ChangeDestinationResponseModel> changeDeStinationApi(
      {required Map request}) async {
    return ChangeDestinationResponseModel.fromJson(await handleResponse(
        await buildHttpResponse('ride-destination-update',
            request: request, method: HttpMethod.post)));
  }

  Future<CurrentRideResponseModel> getCurrentRideRequest() async {
    return CurrentRideResponseModel.fromJson(await handleResponse(
        await buildHttpResponse('current-riderequest',
            method: HttpMethod.get)));
  }

  Future<StatusMessageModel> rideRequestUpdate(
      {required Map request, required int rideId}) async {
    return StatusMessageModel.fromJson(await handleResponse(
        await buildHttpResponse('riderequest-update/$rideId',
            method: HttpMethod.post, request: request)));
  }
}
