import 'package:rider/global/models/vehicle_details_model.dart';
import 'package:rider/model/ServiceModel.dart';

class UserDetailModel {
  UserData? data;
  String? message;

  UserDetailModel({this.data, this.message});

  factory UserDetailModel.fromJson(Map<String, dynamic> json) {
    return UserDetailModel(
      data: json['data'] != null ? UserData.fromJson(json['data']) : null,
      message: json['message'],
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['message'] = message;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class UserData {
  String? address;
    String? firestore_id;
  String? login_screen;
  String? contactNumber;
  String? createdAt;
  String? apiToken;
  String? displayName;
  ServiceList? driverService;
  String? email;
  String? fcmToken;
  String? firstName;
  String? gender;
  String? referralCode;
  var id;
  int? isOnline;
  int? isVerifiedDriver;
  bool? isQuizCompleted;
  String? quizUrl;

  String? lastName;
  String? lastNotificationSeen;
  String? latitude;
  String? loginType;
  String? longitude;
  String? playerId;
  String? profileImage;
  var serviceIds;
  String? status;
  String? timezone;
  String? uid;
  String? updatedAt;
  UserDetail? userDetail;
  String? userType;
  String? username;
  int? isDocumentRequired;
  num? rating;
  int? isAvailable;
  int? age;
  String? selected_map;

  String? email_verified_at;

  VehicleDetailsModel? vehicleDetailsModel;

  int? region_id;
  int? province_id;

  UserData(
      {this.address,
      this.selected_map,
      this.quizUrl,
      this.vehicleDetailsModel,
      this.age,
      this.contactNumber,
      this.createdAt,
      this.displayName,
      this.driverService,
      this.firestore_id,
      this.email,
      this.isQuizCompleted,
      this.fcmToken,
      this.firstName,
      this.gender,
      this.id,
      this.isOnline,
      this.isVerifiedDriver,
      this.lastName,
      this.lastNotificationSeen,
      this.latitude,
      this.loginType,
      this.longitude,
      this.playerId,
      this.profileImage,
      this.serviceIds,
      this.status,
      this.login_screen,
      this.timezone,
      this.uid,
      this.updatedAt,
      this.userDetail,
      this.userType,
      this.username,
      this.apiToken,
      this.isDocumentRequired,
      this.rating,
      this.isAvailable,
      this.region_id,
      this.province_id,
      this.referralCode,
      this.email_verified_at});

  factory UserData.fromJson(Map<String, dynamic> json) {
    return UserData(
        address: json['address'],
                firestore_id: json['firestore_id'],

        selected_map: json['selected_map'],
        age: json['age'],
        login_screen: json['login_screen'],
        contactNumber: json['contact_number'],
        createdAt: json['created_at'],
        displayName: json['display_name'],
        email: json['email'],
        fcmToken: json['fcm_token'],
        firstName: json['first_name'],
        gender: json['gender'],
        id: json['id'],
        isOnline: json['is_online'],
        isVerifiedDriver: json['is_verified_driver'],
        lastName: json['last_name'],
        lastNotificationSeen: json['last_notification_seen'],
        latitude: json['latitude'],
        loginType: json['login_type'],
        longitude: json['longitude'],
        playerId: json['player_id'],
        profileImage: json['profile_image'],
        serviceIds: json['service_ids'],
        status: json['status'],
        timezone: json['timezone'],
        uid: json['uid'],
        quizUrl: json['quiz_url'],
        isQuizCompleted: json['is_quiz_completed'] == null
            ? null
            : json['is_quiz_completed'],
        updatedAt: json['updated_at'],
        userDetail: json['user_detail'] != null
            ? UserDetail.fromJson(json['user_detail'])
            : null,
   
        vehicleDetailsModel: json['vehicle_details'] != null
            ? VehicleDetailsModel.fromJson(json['vehicle_details'])
            : null,
 
        driverService: json['driver_service'] != null
            ? ServiceList.fromJson(json['driver_service'])
            : null,
        userType: json['user_type'],
        username: json['username'],
        apiToken: json['api_token'],
        isDocumentRequired: json['is_document_required'],
        rating: json['rating'],
        isAvailable: json['is_available'],
        referralCode: json['referral_code'],
        region_id: json['region_id'],
        province_id: json["province_id"],
        email_verified_at: json['email_verified_at']);
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['address'] = this.address;
        data['firestore_id'] = this.firestore_id;

    data['selected_map'] = this.selected_map;

    data['age'] = this.age;
    data['login_screen'] = this.login_screen;

    data['quiz_url'] = this.quizUrl;

    data['contact_number'] = this.contactNumber;
    data['created_at'] = this.createdAt;
    data['display_name'] = this.displayName;
    data['email'] = this.email;
    data['fcm_token'] = this.fcmToken;
    data['first_name'] = this.firstName;
    data['gender'] = this.gender;
    data['id'] = this.id;
    data['is_online'] = this.isOnline;
    data['is_verified_driver'] = this.isVerifiedDriver;
    data['last_name'] = this.lastName;
    data['last_notification_seen'] = this.lastNotificationSeen;
    data['latitude'] = this.latitude;
    data['login_type'] = this.loginType;
    data['longitude'] = this.longitude;
    data['player_id'] = this.playerId;
    data['profile_image'] = this.profileImage;
    data['service_ids'] = this.serviceIds;
    data['status'] = this.status;
    data['timezone'] = this.timezone;
    data['uid'] = this.uid;
    data['updated_at'] = this.updatedAt;
    data['user_type'] = this.userType;
    data['username'] = this.username;
    data['api_token'] = this.apiToken;
    data['is_document_required'] = this.isDocumentRequired;
    data['rating'] = this.rating;

    data['region_id'] = this.region_id;

    data['province_id'] = this.province_id;
    data['is_available'] = this.isAvailable;

    if (data['is_quiz_completed'] != null) {
      data['is_quiz_completed'] = this.isQuizCompleted;
    }

    data['referralCode'] = this.referralCode;
    data['email_verified_at'] = this.email_verified_at;

    if (this.userDetail != null) {
      data['user_detail'] = this.userDetail!.toJson();
    }

    if (this.driverService != null) {
      data['driver_service'] = this.driverService!.toJson();
    }
    return data;
  }
}


class UserDetail {
  String? carColor;
  String? carModel;
  String? carPlateNumber;
  String? carProductionYear;
  String? createdAt;
  // String? homeAddress;
  // String? homeLatitude;
  // String? homeLongitude;
  int? id;
  String? updatedAt;
  int? userId;
  // String? workAddress;
  // String? workLatitude;
  // String? workLongitude;
  // bool? nightDrivingPreference;
  // String? socialSecurityNumber;
  int? age;

  UserDetail({
    this.carColor,
    this.carModel,
    this.carPlateNumber,
    this.carProductionYear,
    this.createdAt,
    // this.homeAddress,
    // this.homeLatitude,
    // this.homeLongitude,
    this.id,
    this.updatedAt,
    this.userId,
    // this.workAddress,
    // this.workLatitude,
    // this.workLongitude,
    this.age,
    // this.socialSecurityNumber,
    // this.nightDrivingPreference,
  });

  factory UserDetail.fromJson(Map<String, dynamic> json) {
    return UserDetail(
        carColor: json['car_color'],
        carModel: json['car_model'],
        carPlateNumber: json['car_plate_number'],
        carProductionYear: json['car_production_year'],
        createdAt: json['created_at'],
        // homeAddress: json['home_address'],
        // homeLatitude: json['home_latitude'],
        // homeLongitude: json['home_longitude'],
        id: json['id'],
        updatedAt: json['updated_at'],
        userId: json['user_id'],
        // workAddress: json['work_address'],
        // workLatitude: json['work_latitude'],
        // workLongitude: json['work_longitude'],
        age: json['age']);
    //   nightDrivingPreference: json['nightDrivingPreference'],
    //   socialSecurityNumber: json['securityNumber'],
    // );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['car_color'] = carColor;
    data['car_model'] = carModel;
    data['car_plate_number'] = carPlateNumber;
    data['car_production_year'] = carProductionYear;
    data['created_at'] = createdAt;
    // data['home_address'] = this.homeAddress;
    // data['home_latitude'] = this.homeLatitude;
    // data['home_longitude'] = this.homeLongitude;
    data['id'] = id;
    data['updated_at'] = updatedAt;
    data['user_id'] = userId;
    // data['work_address'] = this.workAddress;
    // data['work_latitude'] = this.workLatitude;
    // data['work_longitude'] = this.workLongitude;
    data['age'] = age;
    // data['nightDrivingPreference'] = this.nightDrivingPreference;
    // data['securityNumber'] = this.socialSecurityNumber;
    return data;
  }
}

class UserBankAccount {
  String? accountHolderName;
  String? accountNumber;
  String? bankCode;
  String? bankName;
  String? createdAt;
  int? id;
  String? updatedAt;
  int? userId;

  String? bankAddress;
  String? bankCity;
  String? postalCode;

  String? dob;
  int? bankId;
  String? institutionNumber;
  String? transitNumber;

  UserBankAccount(
      {this.accountHolderName,
      this.accountNumber,
      this.bankCode,
      this.bankName,
      this.createdAt,
      this.id,
      this.updatedAt,
      this.userId,
      this.bankAddress,
      this.bankCity,
      this.postalCode,
      this.dob,
      this.bankId,
      this.institutionNumber,
      this.transitNumber});

  factory UserBankAccount.fromJson(Map<String, dynamic> json) {
    return UserBankAccount(
        accountHolderName: json['account_holder_name'],
        accountNumber: json['account_number'],
        bankCode: json['bank_code'],
        bankName: json['bank_name'],
        createdAt: json['created_at'],
        id: json['id'],
        updatedAt: json['updated_at'],
        userId: json['user_id'],
        bankAddress: json["bank_address"],
        bankCity: json["bank_city"],
        postalCode: json["postal_code"],
        dob: json["dob"],
        bankId: json["bank_id"],
        institutionNumber: json["institution_number"],
        transitNumber: json["transit_number"]);
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['account_holder_name'] = accountHolderName;
    data['account_number'] = accountNumber;
    data['bank_code'] = bankCode;
    data['bank_name'] = bankName;
    data['created_at'] = createdAt;
    data['id'] = id;
    data['updated_at'] = updatedAt;
    data['user_id'] = userId;
    data['bank_address'] = bankAddress;
    data["bank_city"] = bankCity;
    data["postal_code"] = postalCode;
    data["dob"] = dob;
    data["bank_id"] = bankId;
    data["institution_number"] = institutionNumber;
    data["transit_number"] = transitNumber;
    return data;
  }
}
