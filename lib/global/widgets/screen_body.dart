import 'package:flutter/material.dart';
import 'package:rider/global/widgets/empty_widget.dart';
import 'package:rider/global/widgets/loader.dart';

class ScreenBody extends StatelessWidget {
  final bool isLoading;
  final Widget child;
  final bool isEmpty;
  final String emptyMessage;
  final Future<void> Function()? onPullToRefresh;
  const ScreenBody({
    super.key,
    required this.isLoading,
    required this.child,
    required this.isEmpty,
    required this.emptyMessage,
    this.onPullToRefresh,
  });

  @override
  Widget build(BuildContext context) {
    if (onPullToRefresh == null) {
      return Stack(
        // alignment: Alignment.center,
        children: [
          ListView(),
          isEmpty
              ? Visibility(
                  visible: !isLoading,
                  child: const RooEmptyWidegt(
                    title: "No data",
                    subtitle: "errorMessage",
                  ))
              : child,
          isLoading
              ? Container(
                  color: Colors.white.withOpacity(.5),
                  width: double.infinity,
                  child: const RooLoader(),
                )
              : const SizedBox()
        ],
      );
    } else {
      return RefreshIndicator(
        onRefresh: onPullToRefresh!,
        child: Stack(
          // alignment: Alignment.center,
          children: [
            ListView(),
            isEmpty
                ? Visibility(
                    visible: !isLoading,
                    child: RooEmptyWidegt(
                      title: "No data",
                      subtitle: emptyMessage,
                    ))
                : child,
            isLoading
                ? Container(
                    color: Colors.white.withOpacity(.5),
                    width: double.infinity,
                    child: const RooLoader(),
                  )
                : const SizedBox()
          ],
        ),
      );
    }
  }
}
