import 'package:flutter/material.dart';
import 'package:rider/global/constants/app_text_styles.dart';
import 'package:rider/global/constants/spacer.dart';

class RooEmptyWidegt extends StatelessWidget {
  final String title;
  final String? subtitle;
  const RooEmptyWidegt({super.key, this.subtitle, required this.title});

  @override
  Widget build(BuildContext context) {
    return ListView(
      children: [
        height20,
        height20,
        height20,
        height20,
        Container(
            alignment: Alignment.bottomCenter,
            child: Column(
              children: [
                height20,
                height20,
                height20,
                // Text("Nothing to show new"),
                Text(
                  title,
                  style: AppTextStyles.header,
                ),
              ],
            )

            // child: EmptyWidget(
            //   image: null,
            //   packageImage: PackageImage.Image_4,
            //   title: title,
            //   subTitle: subtitle ?? "No data",
            //   titleTextStyle: TextStyle(
            //     fontSize: 22,
            //     color: Color(0xff9da9c7),
            //     fontWeight: FontWeight.w500,
            //   ),
            //   subtitleTextStyle: TextStyle(
            //     fontSize: 14,
            //     color: Color(0xffabb8d6),
            //   ),
            // ),
            ),
      ],
    );
  }
}
