import 'package:flutter/material.dart';
import 'package:rider/global/models/ride_model.dart';
import 'package:rider/utils/Common.dart';
import 'package:rider/utils/Extensions/app_common.dart';

class ChatButton extends StatelessWidget {
  final OnRideRequest onRideRequest;
  const ChatButton({super.key, required this.onRideRequest});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      child: inkWellWidget(
        onTap: () {
          // chat_count.value = 0;

          // launchScreen(
          //     context,
          //     ChatScreen(
          //       userData: current_ride.onRideRequest.ride,
          //       rideId: current_ride.onRideRequest!.id!,
          //     ));
        },
        child: Column(
          children: [
            const Stack(
              alignment: Alignment.topLeft,
              children: [
                Padding(
                  padding: EdgeInsets.all(8.0),
                  child: Icon(
                    Icons.chat,
                    size: 30,
                  ),
                ),
                // ValueListenableBuilder<int>(
                //   valueListenable: chat_count,
                //   builder: (context, state, _) {
                //     if (state == 0) {
                //       return SizedBox();
                //     } else {
                //       return CircleAvatar(
                //         backgroundColor: Colors.green,
                //         radius: 10,
                //         child: CustomText(
                //           data: state.toString(),
                //           size: 10,
                //           color: Colors.white,
                //         ),
                //       );
                //     }
                //   },
                // )
              ],
            ),
            const SizedBox(height: 4),
            Text("Chat", style: secondaryTextStyle()),
          ],
        ),
      ),
    );
  }
}
