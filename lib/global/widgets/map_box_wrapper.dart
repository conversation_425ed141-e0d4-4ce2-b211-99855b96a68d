import 'package:location/location.dart' as nlp;
import 'package:mapbox_maps_flutter/mapbox_maps_flutter.dart';
import 'package:rider/app_exports.dart';
import 'package:rider/services/map_box_service.dart';
import 'package:rider/services/models/annotation_managers.dart';

class MapBoxWrapperWidget extends StatefulWidget {
  final void Function(MapboxMap)? onMapCreated;
  final bool isDashboardScreen;
  final double zoom;
  const MapBoxWrapperWidget(
      {super.key,
      this.onMapCreated,
      required this.isDashboardScreen,
      this.zoom = 12});

  @override
  State<MapBoxWrapperWidget> createState() => _MapBoxWrapperWidgetState();
}

class _MapBoxWrapperWidgetState extends State<MapBoxWrapperWidget>
    with WidgetsBindingObserver {
  Set<Factory<OneSequenceGestureRecognizer>> gestureRecognizers = {
    Factory<OneSequenceGestureRecognizer>(() => EagerGestureRecognizer()),
  };

  _onMapCreated(MapboxMap mapbox) async {
    // Initialize the map
    Globals.mapbox = mapbox;

    // Initialize annotation managers using the new approach
    AnnotationManagers annotationManagers =
        await MapBoxService.initializeAnnotationManagers(mapbox);

    // For backward compatibility, still set the global variables
    // This will allow existing code to continue working while we transition
    Globals.pointAnnotationManager = annotationManagers.pointManager;
    Globals.polylineAnnotationManager = annotationManagers.polylineManager;

    if (widget.onMapCreated != null) {
      widget.onMapCreated!(mapbox);
    }

    if (widget.isDashboardScreen == false) {
      nlp.LocationData currentLocation = await nlp.Location().getLocation();
      Globals.mapbox!.location.updateSettings(
          LocationComponentSettings(enabled: false, pulsingEnabled: false));

      Globals.mapbox!.flyTo(
          CameraOptions(
            zoom: widget.zoom,
            center: Point(
              coordinates: Position(
                currentLocation.longitude!.toDouble(),
                currentLocation.latitude!.toDouble(),
              ),
            ),
          ),
          MapAnimationOptions());
    }
  }

  @override
  void initState() {
    WidgetsBinding.instance.addObserver(this);
    super.initState();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangePlatformBrightness() {
    if (mounted) {
      Globals.mapbox?.loadStyleURI(
        Theme.of(context).brightness != Brightness.dark
            ? MapboxStyles.DARK
            : MapboxStyles.STANDARD,
      );
    }
    super.didChangePlatformBrightness();
  }

  @override
  Widget build(BuildContext context) {
    return MapWidget(
      onMapCreated: _onMapCreated,
      styleUri: Theme.of(context).brightness == Brightness.dark
          ? MapboxStyles.DARK
          : MapboxStyles.STANDARD,
      key: const ValueKey("mapWidget"),
      gestureRecognizers: gestureRecognizers,
    );
  }
}
