import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:rider/app_exports.dart';

class <PERSON>oo<PERSON>oader extends StatelessWidget {
  final double? size;
  const RooLoader({super.key, this.size});

  get appRadius => null;

  @override
  Widget build(BuildContext context) {
    return SpinKitWave(
      size: size ?? 60,
      itemBuilder: (BuildContext context, int index) {
        return DecoratedBox(
          decoration: BoxDecoration(
            borderRadius: appRadius,
          ),
          child: LinearProgressIndicator(
            color: Theme.of(context).primaryColor,
            backgroundColor: Colors.black,
          ),
        );
      },
    );
  }
}
