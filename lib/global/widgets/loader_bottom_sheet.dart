import 'package:flutter/material.dart';
import 'package:rider/components/custom_text.dart';

class LoaderBottomSheet extends StatelessWidget {
  const LoaderBottomSheet({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height,
      color: Colors.grey.withOpacity(.3),
      child: BottomSheet(
          onClosing: () {},
          builder: (context) {
            return const Padding(
              padding: EdgeInsets.all(10),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  CustomText(
                    data: "Loading....",
                  ),
                  SizedBox(
                      height: 20,
                      width: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: .5,
                      ))
                ],
              ),
            );
          }),
    );
  }
}
