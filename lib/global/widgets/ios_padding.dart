import 'dart:io';

import 'package:flutter/material.dart';
import 'package:rider/utils/constants.dart';

class IosPadding extends StatefulWidget {
  final Widget child;
  const IosPadding({super.key, required this.child});

  @override
  State<IosPadding> createState() => _IosPaddingState();
}

class _IosPaddingState extends State<IosPadding> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.fromLTRB(
        Layout.scaffoldBodyPadding,
        Layout.scaffoldBodyPadding,
        Layout.scaffoldBodyPadding,
        Platform.isIOS
            ? Layout.scaffoldBodyPadding * 1.5
            : Layout.scaffoldBodyPadding,
      ),
      child: widget.child,
    );
  }
}
