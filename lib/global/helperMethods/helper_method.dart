import 'package:rider/app_counters.dart';
import 'package:rider/app_exports.dart';
import 'package:flutter/services.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:rider/main.dart';
import 'package:rider/screens/location_denied_screen.dart';
import 'dart:ui' as ui;
import 'package:share_plus/share_plus.dart';

class HelperMethods {
  static hanldeError(
      {required BuildContext context,
      String? handledErrorMessage,
      String? unHandledErrorMessage}) {}

  static closeScreen(BuildContext context) {
    Navigator.pop(context);
  }

  static Future<T?> replaceScreen<T>(
      {required BuildContext context, required dynamic screen}) async {
    return await Navigator.of(context).pushReplacement(MaterialPageRoute(
      builder: (context) => screen,
    ));
  }

  static Future<T?> pushScreen<T>(
      {required BuildContext context, required dynamic screen}) async {
    return await Navigator.of(context).push(MaterialPageRoute(
      builder: (context) => screen,
    ));
  }

  static Future<T?> pushAndRemoveAll<T>(
      {required BuildContext context, required dynamic screen}) async {
    return await Navigator.of(context).pushAndRemoveUntil(
        MaterialPageRoute(
          builder: (context) => screen,
        ),
        (Route<dynamic> route) => false);
  }

  static Future<Uint8List> getBytesFromAsset(
      String path, BuildContext context) async {
    double pixelRatio = MediaQuery.of(context).devicePixelRatio;
    ByteData data = await rootBundle.load(path);
    ui.Codec codec = await ui.instantiateImageCodec(data.buffer.asUint8List(),
        targetWidth: pixelRatio.round() * 50);
    ui.FrameInfo fi = await codec.getNextFrame();
    return (await fi.image.toByteData(format: ui.ImageByteFormat.png))!
        .buffer
        .asUint8List();
  }

  static void toast(String? value,
      {ToastGravity? gravity,
      length = Toast.LENGTH_SHORT,
      Color? bgColor,
      Color? textColor,
      bool print = false}) {
    if (value!.isEmpty || (!kIsWeb && Platform.isLinux)) {
      log(value);
    } else {
      Fluttertoast.showToast(
        msg: value.validate(),
        gravity: gravity,
        toastLength: length,
        backgroundColor: Globals.isDarkModeOn ? Colors.white : bgColor,
        textColor: Globals.isDarkModeOn ? Colors.black : textColor,
        timeInSecForIosWeb: 2,
      );
      if (print) log(value);
    }
  }

  static String formatDuration(int totalSeconds) {
    int days = totalSeconds ~/ (24 * 3600);
    int hours = (totalSeconds % (24 * 3600)) ~/ 3600;
    int minutes = (totalSeconds % 3600) ~/ 60;
    int seconds = totalSeconds % 60;

    return '${days}d ${hours}h ${minutes}m ${seconds}s';
  }

  static String firstCharUppercase(String text) {
    return text.substring(0, 1).toUpperCase() + text.substring(1);
  }

  static bool isStripeCardExpired({
    required int expiryMonth,
    required int expiryYear,
  }) {
    return expiryMonth < DateTime.now().month &&
        expiryYear < DateTime.now().year;
  }

  static Future<bool> isImageSizeValid({
    required XFile result,
  }) async {
    final int fileSizeInBytes = await result.length();

    final double fileSizeInMB = fileSizeInBytes / (1024 * 1024);

    if (fileSizeInMB > 5) {
      return false;
    } else {
      return true;
    }
  }

  static String capitalize(String string) {
    if (string.isEmpty) {
      return string;
    }
    return "${string[0].toUpperCase()}${string.substring(1)}";
  }

  static Future<void> showWWWConnectionError({
    required BuildContext context,
  }) async {
    if (Globals.isInternetErrorOpen) {
      return;
    }
    Globals.isInternetErrorOpen = true;
    showAppDialog(
        context: context,
        onAccept: () {
          Navigator.pop(navigatorKey.currentContext!);
          Globals.isInternetErrorOpen = false;
        },
        dialogType: AppDialogType.error,
        title:
            "The internet connection is unstable. Please check your internet connection and try again.");
  }

  static Future<bool> handleLocationMustBeEnabled(
    BuildContext context, {
    String? message,
    bool isMandatory = false,
  }) async {
    if (await Permission.locationWhenInUse.isGranted) {
      return true;
    }

    bool? isUserWantToEnable = await showAppDialog(
        onAccept: () {
          Navigator.of(context).pop(true);
        },
        onCancel: () {
          Navigator.of(context).pop(false);
        },
        dialogType:
            isMandatory ? AppDialogType.info : AppDialogType.confirmation,
        positiveButtonText: "Ok",
        negativeButtonText: "No, later",
        title:
            message ?? "To use this feature please allow location permission.");

    PermissionStatus status;
    if (isUserWantToEnable == true) {
      status = await Permission.locationWhenInUse.request();
      if (status == PermissionStatus.granted) {
        return true;
      } else if (status == PermissionStatus.permanentlyDenied) {
        launchScreen(
          const LocationDeniedScreen(),
        );
        return false;
      }
      return false;
    }
    return false;
  }

  static Future<bool> handleNotificationMustBeEnabled(
    BuildContext context, {
    String? message,
    bool isMandatory = false,
  }) async {
    if (await Permission.notification.isGranted) {
      return true;
    }

    bool? isUserWantToEnable = await showAppDialog(
      onAccept: () {
        Navigator.of(context).pop(true);
      },
      onCancel: () {
        Navigator.of(context).pop(false);
      },
      dialogType: isMandatory ? AppDialogType.info : AppDialogType.confirmation,
      positiveButtonText: "Ok",
      negativeButtonText: "No, later",
      title: message ?? "To use this feature please allow location permission.",
    );

    PermissionStatus status;
    if (isUserWantToEnable == true) {
      status = await Permission.notification.request();
      if (status == PermissionStatus.granted) {
        return true;
      } else if (status == PermissionStatus.permanentlyDenied) {
        // launchScreen(
        //   const LocationDeniedScreen(),
        // );
        return false;
      }
      return false;
    }
    return false;
  }

  static Future<void> getAndApplyCounters() async {
    var response = await getCounterData();
    if (response.status) {
      AppCounters.setInboxCount(response.data!.inboxCount);
      AppCounters.setCareCount(response.data!.careCount);
      AppCounters.setNotificationCount(response.data!.notificationCount);
      AppCounters.setRideIssuesCount(response.data!.rideIssuesCount);
    }
  }
}
