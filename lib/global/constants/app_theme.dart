import 'package:flex_color_scheme/flex_color_scheme.dart';
import 'package:flutter/material.dart';
import 'package:rider/utils/constants.dart';

class AppTheme {
  static ThemeData lightTheme = FlexThemeData.light(
    scaffoldBackground: Colors.white,
    primaryContainer: Colors.white,
    useMaterial3: true,
    tertiary: Colors.white,
    onTertiary: Colors.black,
    secondary: AppColors.lightThemeSecondaryColor,
    dialogBackground: Colors.white,
    appBarBackground: AppColors.lightThemePrimaryColor,
    primary: AppColors.lightThemePrimaryColor,
    subThemesData: const FlexSubThemesData(
      inputDecoratorRadius: 10,
      inputDecoratorIsFilled: false,
      inputDecoratorFillColor: Colors.white,
      dialogRadius: 8,
      tabBarUnselectedItemSchemeColor: SchemeColor.secondary,
      tabBarItemSchemeColor: SchemeColor.primary,
      tabBarIndicatorSchemeColor: SchemeColor.primary,
      tabBarIndicatorWeight: 5,
      materialButtonSchemeColor: SchemeColor.primaryContainer,
      elevatedButtonTextStyle: WidgetStatePropertyAll(
        TextStyle(
          color: Colors.white,
        ),
      ),
    ),
  );

  static ThemeData darkTheme = FlexThemeData.dark(
    primaryContainer: Colors.black,
    scaffoldBackground: Colors.black,
    useMaterial3: true,
    error: Colors.red,
    // tertiaryContainer: AppColors.primaryTextFieldColor,
    tertiary: Colors.black,
    primary: AppColors.darkThemePrimaryColor,
    onTertiary: Colors.white,
    secondary: AppColors.darkThemeSecondaryColor,
    appBarBackground: AppColors.primaryBlackColor,

    subThemesData: FlexSubThemesData(
      inputDecoratorRadius: 10,
      alignedDropdown: true,
      inputDecoratorBorderSchemeColor: SchemeColor.primary,
      inputDecoratorBorderWidth: .2,
      inputDecoratorFocusedBorderWidth: 1,
      dialogRadius: 8,
      inputDecoratorFillColor: AppColors.primaryTextFieldColor,
      tabBarUnselectedItemSchemeColor: SchemeColor.secondary,
      tabBarItemSchemeColor: SchemeColor.primary,
      tabBarIndicatorSchemeColor: SchemeColor.primary,
      tabBarIndicatorWeight: 5,
      materialButtonSchemeColor: SchemeColor.primaryContainer,
      elevatedButtonTextStyle: const WidgetStatePropertyAll(
        TextStyle(
          color: Colors.white,
        ),
      ),
    ),
  );
}
