import 'package:state_beacon/state_beacon.dart';

final inboxCount = Beacon.writable(0);
final chatCount = Beacon.writable(0);
final careCount = Beacon.writable(0);
final notificationCount = Beacon.writable(0);
final rideIssuesCount = Beacon.writable(0);

class AppCounters {
  static void incrementInboxCount() {
    inboxCount.value++;
  }

  static void incrementChatCount() {
    chatCount.value++;
  }

  static void incrementCareCount() {
    careCount.value++;
  }

  static void incrementNotificationCount() {
    notificationCount.value++;
  }

  static void decrementInboxCount() {
    inboxCount.value--;
  }

  static void decrementChatCount() {
    chatCount.value--;
  }

  static void decrementCareCount() {
    careCount.value--;
  }

  static void decrementNotificationCount() {
    notificationCount.value--;
  }

  static void resetInboxCount() {
    inboxCount.value = 0;
  }

  static void resetChatCount() {
    chatCount.value = 0;
  }

  static void resetCareCount() {
    careCount.value = 0;
  }

  static void resetNotificationCount() {
    notificationCount.value = 0;
  }

  static void setInboxCount(int value) {
    inboxCount.value = value;
  }

  static void setChatCount(int value) {
    chatCount.value = value;
  }

  static void setCareCount(int value) {
    careCount.value = value;
  }

  static void setNotificationCount(int value) {
    notificationCount.value = value;
  }
  static void setRideIssuesCount(int value) {
    rideIssuesCount.value = value;
  }
}
