<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
	<dict>
		<key>FLTEnableImpeller</key>
  		<false />
		<key>CFBundleDevelopmentRegion</key>
		<string>$(DEVELOPMENT_LANGUAGE)</string>
		<key>CFBundleDisplayName</key>
		<string>ROOO Rider</string>
		<key>CFBundleExecutable</key>
		<string>$(EXECUTABLE_NAME)</string>
		<key>CFBundleIdentifier</key>
		<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
		<key>CFBundleInfoDictionaryVersion</key>
		<string>6.0</string>
		<key>CFBundleName</key>
		<string>ROOO Rider</string>
		<key>CFBundlePackageType</key>
		<string>APPL</string>
		<key>CFBundleShortVersionString</key>
		<string>$(FLUTTER_BUILD_NAME)</string>
		<key>CFBundleSignature</key>
		<string>????</string>
		<key>CFBundleVersion</key>
		<string>$(FLUTTER_BUILD_NUMBER)</string>
		<key>LSRequiresIPhoneOS</key>
		<true />
		<key>UILaunchStoryboardName</key>
		<string>LaunchScreen</string>
		<key>UIMainStoryboardFile</key>
		<string>Main</string>
		<key>UISupportedInterfaceOrientations</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
		</array>
		<key>UISupportedInterfaceOrientations~ipad</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationPortraitUpsideDown</string>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
		</array>
		<key>CADisableMinimumFrameDurationOnPhone</key>
		<true />
		<key>UIApplicationSupportsIndirectInputEvents</key>
		<true />

		<!-- added -->
		<key>NSCameraUsageDescription</key>
		<string>ROOO Rider app requires access to the Camera to upload your profile photo.</string>
		<key>NSContactsUsageDescription</key>
		<string>ROOO Rider requires access to the Contacts to add your contacts for emergency
			contacts.</string>
		<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
		<string>ROOO Rider requires location permission to enable core app functionality. This includes finding nearby riders for new ride bookings, showing your current location to the driver during a ride, and tracking your ride progress. Without location permission, you will not be able to book new rides or utilize current ride features like real-time tracking.</string>
		<key>NSLocationAlwaysUsageDescription</key>
		<string>ROOO Rider requires location permission to enable core app functionality. This includes finding nearby riders for new ride bookings, showing your current location to the driver during a ride, and tracking your ride progress. Without location permission, you will not be able to book new rides or utilize current ride features like real-time tracking.</string>
		<key>NSLocationWhenInUseUsageDescription</key>
		<string>ROOO Rider requires location permission to enable core app functionality. This includes finding nearby riders for new ride bookings, showing your current location to the driver during a ride, and tracking your ride progress. Without location permission, you will not be able to book new rides or utilize current ride features like real-time tracking.</string>
		<key>NSMicrophoneUsageDescription</key>
		<string>Need microphone access for VOIP call feature</string>
		<key>NSPhotoLibraryAddUsageDescription</key>
		<string>ROOO Rider requires access to the photo library to upload your profile photo.</string>
		<key>NSPhotoLibraryUsageDescription</key>
		<string>ROOO Rider requires access to the photo library to upload your profile photo.</string>
		<key>UIApplicationSupportsIndirectInputEvents</key>
		<true />
		<key>UIBackgroundModes</key>
		<array>
			<string>fetch</string>
			<string>processing</string>
			<string>remote-notification</string>
			<string>voip</string>
		</array>
		<key>BGTaskSchedulerPermittedIdentifiers</key>
		<array>
			<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
		</array>


		<!-- google sign in -->
		<key>GIDClientID</key>
		<string>972284281345-qnp7tvvcimphgoj5fcd4m2e96uqv30vl.apps.googleusercontent.com</string>


		<!-- additional Google + facebook -->
		<key>CFBundleURLTypes</key>
		<array>
			<dict>
				<key>CFBundleTypeRole</key>
				<string>Editor</string>
				<key>CFBundleURLSchemes</key>
				<array>
					<string>com.googleusercontent.apps.972284281345-qnp7tvvcimphgoj5fcd4m2e96uqv30vl</string>
					<string>fb1528308894458646</string>
				</array>
			</dict>
		</array>
		<!-- Facebook -->
		<key>FacebookAppID</key>
		<string>1528308894458646</string>
		<key>FacebookClientToken</key>
		<string>********************************</string>
		<key>FacebookDisplayName</key>
		<string>ROOO Rider</string>
		<key>LSApplicationQueriesSchemes</key>
		<array>
			<string>fbapi</string>
			<string>fb-messenger-share-api</string>
		</array>
	</dict>
</plist>