plugins {
    id "com.android.application"
    // START: FlutterFire Configuration
    id 'com.google.gms.google-services'
    // id 'com.google.firebase.crashlytics'
    // END: FlutterFire Configuration
    id "kotlin-android"
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id "dev.flutter.flutter-gradle-plugin"
}

def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file("key.properties")
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}

android {
    namespace = "app.rooo.rider"
    compileSdk = 35
    ndkVersion = "28.0.13004108"

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_1_8
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId = "app.rooo.rider"
        // You can update the following values to match your application needs.
        // For more information, see: https://flutter.dev/to/review-gradle-config.
        minSdk = 23
        targetSdk = flutter.targetSdkVersion
        versionCode = flutter.versionCode
        versionName = flutter.versionName
    }
     signingConfigs {
         release {
           keyAlias keystoreProperties['keyAlias']
           keyPassword keystoreProperties['keyPassword']
           storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
           storePassword keystoreProperties['storePassword']
       }
    }

    buildTypes {
        release {
            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now, so `flutter run --release` works.
            signingConfig = signingConfigs.release
        }
    }
}

flutter {
    source = "../.."
}

// just for zego, can be removed IF zego is not  used
dependencies {
    implementation 'com.google.firebase:firebase-messaging:21.1.0'
}



// Custom task to copy and rename the APK to a specific location
tasks.register('copyRenameReleaseApk') {
    dependsOn 'assembleRelease'
    doLast {
        def outputDir = new File("/home/<USER>/projects/APKs")
        if (!outputDir.exists()) {
            outputDir.mkdirs()
        }

        // Get the version name from the defaultConfig
        def versionName = android.defaultConfig.versionName
        def versionCode = android.defaultConfig.versionCode

        // Find the APK file
        def apkDir = new File("${buildDir}/outputs/apk/release/")
        def apkFile = new File(apkDir, "app-release.apk")

        if (apkFile.exists()) {
            // Create the destination file with the desired name
            def destFile = new File(outputDir, "ROOO-Rider-${versionName}-${versionCode}.apk")

            // Copy the file to both destinations
            ant.copy(file: apkFile, tofile: destFile)

            println "\n\nAPK copied and renamed to:\n${destFile.absolutePath}\n"
        } else {
            println "\n\nRelease APK not found at ${apkFile.absolutePath}\n\n"
        }
    }
}