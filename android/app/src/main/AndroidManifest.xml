<manifest xmlns:android="http://schemas.android.com/apk/res/android">
    <application
        android:label="ROOO Rider"
        android:name="${applicationName}"
        android:icon="@mipmap/ic_launcher">

        <meta-data
    android:name="io.flutter.embedding.android.EnableImpeller"
    android:value="false" />

        <!-- App metas -->
        <meta-data
            android:name="com.google.android.geo.API_KEY"
            android:value="AIzaSyBE1ks4PYI4zjuLEph1qCo5201O2XKrqM8" />

        <meta-data
            android:name="com.google.android.gms.wallet.api.enabled"
            android:value="true" />
        <meta-data android:name="com.facebook.sdk.ApplicationId"
            android:value="@string/facebook_app_id" />
        <meta-data android:name="com.facebook.sdk.ClientToken"
            android:value="@string/facebook_client_token" />


        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:launchMode="singleTop"
            android:taskAffinity=""
            android:theme="@style/LaunchTheme"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:windowSoftInputMode="adjustResize">


            <!-- Specifies an Android theme to apply to this Activity as soon as
                 the Android process has started. This theme is visible to the user
                 while the Flutter UI initializes. After that, this theme continues
                 to determine the Window background behind the Flutter UI. -->
            <meta-data
                android:name="io.flutter.embedding.android.NormalTheme"
                android:resource="@style/NormalTheme"
            />


            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>


        <!-- Microsoft Activity -->
        <activity android:name="com.microsoft.identity.client.BrowserTabActivity" android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="app.rooo.rider"
                    android:path="/d05/GMlE1E3MDGC6PFQqjqtjYbE="
                    android:scheme="msauth" />
            </intent-filter>
        </activity>

        <!-- Don't delete the meta-data below.
             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java -->
        <meta-data
            android:name="flutterEmbedding"
            android:value="2" />
    </application>
    <!-- Required to query activities that can process text, see:
         https://developer.android.com/training/package-visibility and
         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.

         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin. -->
    <queries>
        <intent>
            <action android:name="android.intent.action.PROCESS_TEXT" />
            <data android:mimeType="text/plain" />
        </intent>


        <provider android:authorities="com.facebook.katana.provider.PlatformProvider" />
    </queries>

    <!-- App permissions -->
    <!-- <uses-permission android:name="android.permission.FOREGROUND_SERVICE" /> -->
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.INTERNET" />

    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.ACCESS_NOTIFICATION_POLICY" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <!--for
    bring app to foreground from background in Android 10 -->
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />

</manifest>